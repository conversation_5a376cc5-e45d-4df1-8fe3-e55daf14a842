{"version": 3, "sources": ["../../prismjs/components/prism-tsx.js"], "sourcesContent": ["(function (Prism) {\n\tvar typescript = Prism.util.clone(Prism.languages.typescript);\n\tPrism.languages.tsx = Prism.languages.extend('jsx', typescript);\n\n\t// doesn't work with TS because TS is too complex\n\tdelete Prism.languages.tsx['parameter'];\n\tdelete Prism.languages.tsx['literal-property'];\n\n\t// This will prevent collisions between TSX tags and TS generic types.\n\t// Idea by https://github.com/karlhorky\n\t// Discussion: https://github.com/PrismJS/prism/issues/2594#issuecomment-710666928\n\tvar tag = Prism.languages.tsx.tag;\n\ttag.pattern = RegExp(/(^|[^\\w$]|(?=<\\/))/.source + '(?:' + tag.pattern.source + ')', tag.pattern.flags);\n\ttag.lookbehind = true;\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AACjB,MAAI,aAAaA,OAAM,KAAK,MAAMA,OAAM,UAAU,UAAU;AAC5D,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,OAAO,UAAU;AAG9D,SAAOA,OAAM,UAAU,IAAI,WAAW;AACtC,SAAOA,OAAM,UAAU,IAAI,kBAAkB;AAK7C,MAAI,MAAMA,OAAM,UAAU,IAAI;AAC9B,MAAI,UAAU,OAAO,qBAAqB,SAAS,QAAQ,IAAI,QAAQ,SAAS,KAAK,IAAI,QAAQ,KAAK;AACtG,MAAI,aAAa;AAClB,GAAE,KAAK;", "names": ["Prism"]}