{"version": 3, "sources": ["../../@popperjs/core/lib/enums.js", "../../@popperjs/core/lib/dom-utils/getNodeName.js", "../../@popperjs/core/lib/dom-utils/getWindow.js", "../../@popperjs/core/lib/dom-utils/instanceOf.js", "../../@popperjs/core/lib/modifiers/applyStyles.js", "../../@popperjs/core/lib/utils/getBasePlacement.js", "../../@popperjs/core/lib/utils/math.js", "../../@popperjs/core/lib/utils/userAgent.js", "../../@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../@popperjs/core/lib/dom-utils/contains.js", "../../@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../@popperjs/core/lib/dom-utils/isTableElement.js", "../../@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../@popperjs/core/lib/dom-utils/getParentNode.js", "../../@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../@popperjs/core/lib/utils/within.js", "../../@popperjs/core/lib/utils/getFreshSideObject.js", "../../@popperjs/core/lib/utils/mergePaddingObject.js", "../../@popperjs/core/lib/utils/expandToHashMap.js", "../../@popperjs/core/lib/modifiers/arrow.js", "../../@popperjs/core/lib/utils/getVariation.js", "../../@popperjs/core/lib/modifiers/computeStyles.js", "../../@popperjs/core/lib/modifiers/eventListeners.js", "../../@popperjs/core/lib/utils/getOppositePlacement.js", "../../@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../@popperjs/core/lib/dom-utils/getViewportRect.js", "../../@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../@popperjs/core/lib/dom-utils/isScrollParent.js", "../../@popperjs/core/lib/dom-utils/getScrollParent.js", "../../@popperjs/core/lib/dom-utils/listScrollParents.js", "../../@popperjs/core/lib/utils/rectToClientRect.js", "../../@popperjs/core/lib/dom-utils/getClippingRect.js", "../../@popperjs/core/lib/utils/computeOffsets.js", "../../@popperjs/core/lib/utils/detectOverflow.js", "../../@popperjs/core/lib/utils/computeAutoPlacement.js", "../../@popperjs/core/lib/modifiers/flip.js", "../../@popperjs/core/lib/modifiers/hide.js", "../../@popperjs/core/lib/modifiers/offset.js", "../../@popperjs/core/lib/modifiers/popperOffsets.js", "../../@popperjs/core/lib/utils/getAltAxis.js", "../../@popperjs/core/lib/modifiers/preventOverflow.js", "../../@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../@popperjs/core/lib/utils/orderModifiers.js", "../../@popperjs/core/lib/utils/debounce.js", "../../@popperjs/core/lib/utils/mergeByName.js", "../../@popperjs/core/lib/createPopper.js", "../../@popperjs/core/lib/popper-lite.js", "../../@popperjs/core/lib/popper.js", "../../tippy.js/src/constants.ts", "../../tippy.js/src/utils.ts", "../../tippy.js/src/dom-utils.ts", "../../tippy.js/src/bindGlobalEventListeners.ts", "../../tippy.js/src/browser.ts", "../../tippy.js/src/validation.ts", "../../tippy.js/src/props.ts", "../../tippy.js/src/template.ts", "../../tippy.js/src/createTippy.ts", "../../tippy.js/src/index.ts", "../../tippy.js/src/addons/createSingleton.ts", "../../tippy.js/src/addons/delegate.ts", "../../tippy.js/src/plugins/animateFill.ts", "../../tippy.js/src/plugins/followCursor.ts", "../../tippy.js/src/plugins/inlinePositioning.ts", "../../tippy.js/src/plugins/sticky.ts", "../../tippy.js/build/base.js", "../../@tiptap/extension-bubble-menu/src/bubble-menu-plugin.ts", "../../@tiptap/extension-bubble-menu/src/bubble-menu.ts", "../../@tiptap/extension-floating-menu/src/floating-menu-plugin.ts", "../../@tiptap/extension-floating-menu/src/floating-menu.ts", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../node_modules/use-sync-external-store/shim/index.js", "../../@tiptap/react/src/EditorContent.tsx", "../../node_modules/fast-deep-equal/es6/react.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../node_modules/use-sync-external-store/shim/with-selector.js", "../../@tiptap/react/src/useEditorState.ts", "../../@tiptap/react/src/useEditor.ts", "../../@tiptap/react/src/Context.tsx", "../../@tiptap/react/src/BubbleMenu.tsx", "../../@tiptap/react/src/FloatingMenu.tsx", "../../@tiptap/react/src/useReactNodeView.ts", "../../@tiptap/react/src/NodeViewContent.tsx", "../../@tiptap/react/src/NodeViewWrapper.tsx", "../../@tiptap/react/src/ReactRenderer.tsx", "../../@tiptap/react/src/ReactNodeViewRenderer.tsx"], "sourcesContent": ["export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n\nexport const TIPPY_DEFAULT_APPEND_TO = () => document.body;\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(\n  obj: Record<string, unknown>,\n  key: string\n): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n\n  // Elements created via a <template> have an ownerDocument with no reference to the body\n  return element?.ownerDocument?.body ? element.ownerDocument : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\nexport function actualContains(parent: Element, child: Element): boolean {\n  let target = child;\n  while (target) {\n    if (parent.contains(target)) {\n      return true;\n    }\n    target = (target.getRootNode?.() as any)?.host;\n  }\n  return false;\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const isIE11 = isBrowser\n  ? // @ts-ignore\n    !!window.msCrypto\n  : false;\n", "import {Targets} from './types';\n\nexport function createMemoryLeakWarning(method: string): string {\n  const txt = method === 'destroy' ? 'n already-' : ' ';\n\n  return [\n    `${method}() was called on a${txt}destroyed instance. This is a no-op but`,\n    'indicates a potential memory leak.',\n  ].join(' ');\n}\n\nexport function clean(value: string): string {\n  const spacesAndTabs = /[ \\t]{2,}/g;\n  const lineStartWithSpaces = /^[ \\t]*/gm;\n\n  return value\n    .replace(spacesAndTabs, ' ')\n    .replace(lineStartWithSpaces, '')\n    .trim();\n}\n\nfunction getDevMessage(message: string): string {\n  return clean(`\n  %ctippy.js\n\n  %c${clean(message)}\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  `);\n}\n\nexport function getFormattedMessage(message: string): string[] {\n  return [\n    getDevMessage(message),\n    // title\n    'color: #00C584; font-size: 1.3em; font-weight: bold;',\n    // message\n    'line-height: 1.5',\n    // footer\n    'color: #a6a095;',\n  ];\n}\n\n// Assume warnings and errors never have the same message\nlet visitedMessages: Set<string>;\nif (__DEV__) {\n  resetVisitedMessages();\n}\n\nexport function resetVisitedMessages(): void {\n  visitedMessages = new Set();\n}\n\nexport function warnWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.warn(...getFormattedMessage(message));\n  }\n}\n\nexport function errorWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.error(...getFormattedMessage(message));\n  }\n}\n\nexport function validateTargets(targets: Targets): void {\n  const didPassFalsyValue = !targets;\n  const didPassPlainObject =\n    Object.prototype.toString.call(targets) === '[object Object]' &&\n    !(targets as any).addEventListener;\n\n  errorWhen(\n    didPassFalsyValue,\n    [\n      'tippy() was passed',\n      '`' + String(targets) + '`',\n      'as its targets (first) argument. Valid types are: String, Element,',\n      'Element[], or NodeList.',\n    ].join(' ')\n  );\n\n  errorWhen(\n    didPassPlainObject,\n    [\n      'tippy() was passed a plain object which is not supported as an argument',\n      'for virtual positioning. Use props.getReferenceClientRect instead.',\n    ].join(' ')\n  );\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\nimport {TIPPY_DEFAULT_APPEND_TO} from './constants';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined\n          ? passedProps[name]\n          : (defaultProps as any)[name] ?? defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE11} from './browser';\nimport {TIPPY_DEFAULT_APPEND_TO, TOUCH_OPTIONS} from './constants';\nimport {\n  actualContains,\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', () => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDocument(): Document {\n    const parent = getCurrentTarget().parentNode as Element;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(fromHide = false): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    const actualTarget =\n      (event.composedPath && event.composedPath()[0]) || event.target;\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      actualContains(popper, actualTarget as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (\n      normalizeToArray(instance.props.triggerTarget || reference).some((el) =>\n        actualContains(el, actualTarget as Element)\n      )\n    ) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    const doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    const doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', Record<string, unknown>> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', Record<string, unknown>>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...prevProps,\n      ...removeUndefinedProps(partialProps),\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n      instance.popperInstance?.forceUpdate();\n\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n  Instance,\n  Props,\n} from '../types';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\nimport {applyStyles, Modifier} from '@popperjs/core';\n\n// The default `applyStyles` modifier has a cleanup function that gets called\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\nconst applyStylesModifier: Modifier<'applyStyles', Record<string, unknown>> = {\n  ...applyStyles,\n  effect({state}) {\n    const initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0',\n      },\n      arrow: {\n        position: 'absolute',\n      },\n      reference: {},\n    };\n\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    }\n\n    // intentionally return no cleanup function\n    // return () => { ... }\n  },\n};\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let individualInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let triggerTargets: Array<Element> = [];\n  let currentTarget: Element | null;\n  let overrides = optionalProps.overrides;\n  let interceptSetPropsCleanups: Array<() => void> = [];\n  let shownOnCreate = false;\n\n  function setTriggerTargets(): void {\n    triggerTargets = individualInstances\n      .map((instance) =>\n        normalizeToArray(instance.props.triggerTarget || instance.reference)\n      )\n      .reduce((acc, item) => acc.concat(item), []);\n  }\n\n  function setReferences(): void {\n    references = individualInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    individualInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton: Instance): Array<() => void> {\n    return individualInstances.map((instance) => {\n      const originalSetProps = instance.setProps;\n\n      instance.setProps = (props): void => {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return (): void => {\n        instance.setProps = originalSetProps;\n      };\n    });\n  }\n\n  // have to pass singleton, as it maybe undefined on first call\n  function prepareInstance(\n    singleton: Instance,\n    target: ReferenceElement\n  ): void {\n    const index = triggerTargets.indexOf(target);\n\n    // bail-out\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n\n    const overrideProps: Partial<Props> = (overrides || [])\n      .concat('content')\n      .reduce((acc, prop) => {\n        (acc as any)[prop] = individualInstances[index].props[prop];\n        return acc;\n      }, {});\n\n    singleton.setProps({\n      ...overrideProps,\n      getReferenceClientRect:\n        typeof overrideProps.getReferenceClientRect === 'function'\n          ? overrideProps.getReferenceClientRect\n          : (): ClientRect => references[index]?.getBoundingClientRect(),\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n\n  const plugin: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onHidden(): void {\n          currentTarget = null;\n        },\n        onClickOutside(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger(instance, event): void {\n          prepareInstance(instance, event.currentTarget as Element);\n        },\n      };\n    },\n  };\n\n  const singleton = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [plugin, ...(optionalProps.plugins || [])],\n    triggerTarget: triggerTargets,\n    popperOptions: {\n      ...optionalProps.popperOptions,\n      modifiers: [\n        ...(optionalProps.popperOptions?.modifiers || []),\n        applyStylesModifier,\n      ],\n    },\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalShow = singleton.show;\n\n  singleton.show = (target?: ReferenceElement | Instance | number): void => {\n    originalShow();\n\n    // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    }\n\n    // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n    if (currentTarget && target == null) {\n      return;\n    }\n\n    // target is index of instance\n    if (typeof target === 'number') {\n      return (\n        references[target] && prepareInstance(singleton, references[target])\n      );\n    }\n\n    // target is a child tippy instance\n    if (individualInstances.indexOf(target as Instance) >= 0) {\n      const ref = (target as Instance).reference;\n      return prepareInstance(singleton, ref);\n    }\n\n    // target is a ReferenceElement\n    if (references.indexOf(target as ReferenceElement) >= 0) {\n      return prepareInstance(singleton, target as ReferenceElement);\n    }\n  };\n\n  singleton.showNext = (): void => {\n    const first = references[0];\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n    const index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = (): void => {\n    const last = references[references.length - 1];\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n    const index = references.indexOf(currentTarget);\n    const target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  const originalSetProps = singleton.setProps;\n\n  singleton.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = (nextInstances): void => {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach((fn) => fn());\n\n    individualInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n\n    singleton.setProps({triggerTarget: triggerTargets});\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n\n  return singleton;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {TOUCH_OPTIONS} from '../constants';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n  let disabled = false;\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {\n    touch: defaultProps.touch,\n    ...nativeProps,\n    showOnCreate: true,\n  };\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type]) < 0\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    const originalEnable = instance.enable;\n    const originalDisable = instance.disable;\n\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = (): void => {\n      originalEnable();\n      childTippyInstances.forEach((instance) => instance.enable());\n      disabled = false;\n    };\n\n    instance.disable = (): void => {\n      originalDisable();\n      childTippyInstances.forEach((instance) => instance.disable());\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument, isMouseEvent} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          mouseCoords = {clientX: event.clientX, clientY: event.clientY};\n        }\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n    let triedPlacements: Array<string> = [];\n\n    const modifier: Modifier<\n      'tippyInlinePositioning',\n      Record<string, unknown>\n    > = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (\n            placement !== state.placement &&\n            triedPlacements.indexOf(state.placement) === -1\n          ) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): Partial<DOMRect> {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n          const index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: DOMRect,\n  clientRects: DOMRect[],\n  cursorRectIndex: number\n): {\n  top: number;\n  bottom: number;\n  left: number;\n  right: number;\n  width: number;\n  height: number;\n} {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import tippy from '../src';\nimport {render} from '../src/template';\n\ntippy.setDefaultProps({render});\n\nexport {default, hideAll} from '../src';\nexport {default as createSingleton} from '../src/addons/createSingleton';\nexport {default as delegate} from '../src/addons/delegate';\nexport {default as animateFill} from '../src/plugins/animateFill';\nexport {default as followCursor} from '../src/plugins/followCursor';\nexport {default as inlinePositioning} from '../src/plugins/inlinePositioning';\nexport {default as sticky} from '../src/plugins/sticky';\nexport {ROUND_ARROW as roundArrow} from '../src/constants';\n", "import {\n  Editor, isNodeSelection, isTextSelection, posToDOMRect,\n} from '@tiptap/core'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface BubbleMenuPluginProps {\n  /**\n   * The plugin key.\n   * @type {PluginKey | string}\n   * @default 'bubbleMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy.js instance.\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * The delay in milliseconds before the menu should be updated.\n   * This can be useful to prevent performance issues.\n   * @type {number}\n   * @default 250\n   */\n  updateDelay?: number\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        element: HTMLElement\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n        from: number\n        to: number\n      }) => boolean)\n    | null\n}\n\nexport type BubbleMenuViewProps = BubbleMenuPluginProps & {\n  view: EditorView\n}\n\nexport class BubbleMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  public updateDelay: number\n\n  private updateDebounceTimer: number | undefined\n\n  public shouldShow: Exclude<BubbleMenuPluginProps['shouldShow'], null> = ({\n    view,\n    state,\n    from,\n    to,\n  }) => {\n    const { doc, selection } = state\n    const { empty } = selection\n\n    // Sometime check for `empty` is not enough.\n    // Doubleclick an empty paragraph returns a node size of 2.\n    // So we check also for an empty text size.\n    const isEmptyTextBlock = !doc.textBetween(from, to).length && isTextSelection(state.selection)\n\n    // When clicking on a element inside the bubble menu the editor \"blur\" event\n    // is called and the bubble menu item is focussed. In this case we should\n    // consider the menu as part of the editor and keep showing the menu\n    const isChildOfMenu = this.element.contains(document.activeElement)\n\n    const hasEditorFocus = view.hasFocus() || isChildOfMenu\n\n    if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor,\n    element,\n    view,\n    tippyOptions = {},\n    updateDelay = 250,\n    shouldShow,\n  }: BubbleMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n    this.updateDelay = updateDelay\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.addEventListener('dragstart', this.dragstartHandler)\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  dragstartHandler = () => {\n    this.hide()\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'top',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const hasValidSelection = state.selection.from !== state.selection.to\n\n    if (this.updateDelay > 0 && hasValidSelection) {\n      this.handleDebouncedUpdate(view, oldState)\n      return\n    }\n\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    this.updateHandler(view, selectionChanged, docChanged, oldState)\n  }\n\n  handleDebouncedUpdate = (view: EditorView, oldState?: EditorState) => {\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    if (!selectionChanged && !docChanged) {\n      return\n    }\n\n    if (this.updateDebounceTimer) {\n      clearTimeout(this.updateDebounceTimer)\n    }\n\n    this.updateDebounceTimer = window.setTimeout(() => {\n      this.updateHandler(view, selectionChanged, docChanged, oldState)\n    }, this.updateDelay)\n  }\n\n  updateHandler = (view: EditorView, selectionChanged: boolean, docChanged: boolean, oldState?: EditorState) => {\n    const { state, composing } = view\n    const { selection } = state\n\n    const isSame = !selectionChanged && !docChanged\n\n    if (composing || isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    // support for CellSelections\n    const { ranges } = selection\n    const from = Math.min(...ranges.map(range => range.$from.pos))\n    const to = Math.max(...ranges.map(range => range.$to.pos))\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      element: this.element,\n      view,\n      state,\n      oldState,\n      from,\n      to,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect\n        || (() => {\n          if (isNodeSelection(state.selection)) {\n            let node = view.nodeDOM(from) as HTMLElement\n\n            if (node) {\n              const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]')\n\n              if (nodeViewWrapper) {\n                node = nodeViewWrapper.firstChild as HTMLElement\n              }\n\n              if (node) {\n                return node.getBoundingClientRect()\n              }\n            }\n          }\n\n          return posToDOMRect(view, from, to)\n        }),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.removeEventListener('dragstart', this.dragstartHandler)\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const BubbleMenuPlugin = (options: BubbleMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new BubbleMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { BubbleMenuPlugin, BubbleMenuPluginProps } from './bubble-menu-plugin.js'\n\nexport type BubbleMenuOptions = Omit<BubbleMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a bubble menu.\n * @see https://tiptap.dev/api/extensions/bubble-menu\n */\nexport const BubbleMenu = Extension.create<BubbleMenuOptions>({\n  name: 'bubbleMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'bubbleMenu',\n      updateDelay: undefined,\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      BubbleMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        updateDelay: this.options.updateDelay,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n", "import {\n  Editor, getText, getTextSerializersFromSchema, posToDOMRect,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface FloatingMenuPluginProps {\n  /**\n   * The plugin key for the floating menu.\n   * @default 'floatingMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   * @default null\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy instance.\n   * @default {}\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   * @default null\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n      }) => boolean)\n    | null\n}\n\nexport type FloatingMenuViewProps = FloatingMenuPluginProps & {\n  /**\n   * The editor view.\n   */\n  view: EditorView\n}\n\nexport class FloatingMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  private getTextContent(node:ProseMirrorNode) {\n    return getText(node, { textSerializers: getTextSerializersFromSchema(this.editor.schema) })\n  }\n\n  public shouldShow: Exclude<FloatingMenuPluginProps['shouldShow'], null> = ({ view, state }) => {\n    const { selection } = state\n    const { $anchor, empty } = selection\n    const isRootDepth = $anchor.depth === 1\n\n    const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent)\n\n    if (\n      !view.hasFocus()\n      || !empty\n      || !isRootDepth\n      || !isEmptyTextBlock\n      || !this.editor.isEditable\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor, element, view, tippyOptions = {}, shouldShow,\n  }: FloatingMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'right',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const { doc, selection } = state\n    const { from, to } = selection\n    const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection)\n\n    if (isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      view,\n      state,\n      oldState,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect || (() => posToDOMRect(view, from, to)),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const FloatingMenuPlugin = (options: FloatingMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new FloatingMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { FloatingMenuPlugin, FloatingMenuPluginProps } from './floating-menu-plugin.js'\n\nexport type FloatingMenuOptions = Omit<FloatingMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a floating menu.\n * @see https://tiptap.dev/api/extensions/floating-menu\n */\nexport const FloatingMenu = Extension.create<FloatingMenuOptions>({\n  name: 'floatingMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'floatingMenu',\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      FloatingMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n", "/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var e=require(\"react\");function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c})},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c})})},[a]);p(d);return d}\nfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return!k(a,d)}catch(f){return!0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;exports.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n", "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  ForwardedRef, forwardRef, HTMLProps, LegacyRef, MutableRefObject,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { ContentComponent, EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\n\nconst mergeRefs = <T extends HTMLDivElement>(\n  ...refs: Array<MutableRefObject<T> | LegacyRef<T> | undefined>\n) => {\n  return (node: T) => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(node)\n      } else if (ref) {\n        (ref as MutableRefObject<T | null>).current = node\n      }\n    })\n  }\n}\n\n/**\n * This component renders all of the editor's node views.\n */\nconst Portals: React.FC<{ contentComponent: ContentComponent }> = ({\n  contentComponent,\n}) => {\n  // For performance reasons, we render the node view portals on state changes only\n  const renderers = useSyncExternalStore(\n    contentComponent.subscribe,\n    contentComponent.getSnapshot,\n    contentComponent.getServerSnapshot,\n  )\n\n  // This allows us to directly render the portals without any additional wrapper\n  return (\n    <>\n      {Object.values(renderers)}\n    </>\n  )\n}\n\nexport interface EditorContentProps extends HTMLProps<HTMLDivElement> {\n  editor: Editor | null;\n  innerRef?: ForwardedRef<HTMLDivElement | null>;\n}\n\nfunction getInstance(): ContentComponent {\n  const subscribers = new Set<() => void>()\n  let renderers: Record<string, React.ReactPortal> = {}\n\n  return {\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(callback: () => void) {\n      subscribers.add(callback)\n      return () => {\n        subscribers.delete(callback)\n      }\n    },\n    getSnapshot() {\n      return renderers\n    },\n    getServerSnapshot() {\n      return renderers\n    },\n    /**\n     * Adds a new NodeView Renderer to the editor.\n     */\n    setRenderer(id: string, renderer: ReactRenderer) {\n      renderers = {\n        ...renderers,\n        [id]: ReactDOM.createPortal(renderer.reactElement, renderer.element, id),\n      }\n\n      subscribers.forEach(subscriber => subscriber())\n    },\n    /**\n     * Removes a NodeView Renderer from the editor.\n     */\n    removeRenderer(id: string) {\n      const nextRenderers = { ...renderers }\n\n      delete nextRenderers[id]\n      renderers = nextRenderers\n      subscribers.forEach(subscriber => subscriber())\n    },\n  }\n}\n\nexport class PureEditorContent extends React.Component<\n  EditorContentProps,\n  { hasContentComponentInitialized: boolean }\n> {\n  editorContentRef: React.RefObject<any>\n\n  initialized: boolean\n\n  unsubscribeToContentComponent?: () => void\n\n  constructor(props: EditorContentProps) {\n    super(props)\n    this.editorContentRef = React.createRef()\n    this.initialized = false\n\n    this.state = {\n      hasContentComponentInitialized: Boolean((props.editor as EditorWithContentComponent | null)?.contentComponent),\n    }\n  }\n\n  componentDidMount() {\n    this.init()\n  }\n\n  componentDidUpdate() {\n    this.init()\n  }\n\n  init() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (editor && !editor.isDestroyed && editor.options.element) {\n      if (editor.contentComponent) {\n        return\n      }\n\n      const element = this.editorContentRef.current\n\n      element.append(...editor.options.element.childNodes)\n\n      editor.setOptions({\n        element,\n      })\n\n      editor.contentComponent = getInstance()\n\n      // Has the content component been initialized?\n      if (!this.state.hasContentComponentInitialized) {\n        // Subscribe to the content component\n        this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {\n          this.setState(prevState => {\n            if (!prevState.hasContentComponentInitialized) {\n              return {\n                hasContentComponentInitialized: true,\n              }\n            }\n            return prevState\n          })\n\n          // Unsubscribe to previous content component\n          if (this.unsubscribeToContentComponent) {\n            this.unsubscribeToContentComponent()\n          }\n        })\n      }\n\n      editor.createNodeViews()\n\n      this.initialized = true\n    }\n  }\n\n  componentWillUnmount() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (!editor) {\n      return\n    }\n\n    this.initialized = false\n\n    if (!editor.isDestroyed) {\n      editor.view.setProps({\n        nodeViews: {},\n      })\n    }\n\n    if (this.unsubscribeToContentComponent) {\n      this.unsubscribeToContentComponent()\n    }\n\n    editor.contentComponent = null\n\n    if (!editor.options.element.firstChild) {\n      return\n    }\n\n    const newElement = document.createElement('div')\n\n    newElement.append(...editor.options.element.childNodes)\n\n    editor.setOptions({\n      element: newElement,\n    })\n  }\n\n  render() {\n    const { editor, innerRef, ...rest } = this.props\n\n    return (\n      <>\n        <div ref={mergeRefs(innerRef, this.editorContentRef)} {...rest} />\n        {/* @ts-ignore */}\n        {editor?.contentComponent && <Portals contentComponent={editor.contentComponent} />}\n      </>\n    )\n  }\n}\n\n// EditorContent should be re-created whenever the Editor instance changes\nconst EditorContentWithKey = forwardRef<HTMLDivElement, EditorContentProps>(\n  (props: Omit<EditorContentProps, 'innerRef'>, ref) => {\n    const key = React.useMemo(() => {\n      return Math.floor(Math.random() * 0xffffffff).toString()\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.editor])\n\n    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement\n    return React.createElement(PureEditorContent, {\n      key,\n      innerRef: ref,\n      ...props,\n    })\n  },\n)\n\nexport const EditorContent = React.memo(EditorContentWithKey)\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n  var envHasBigInt64Array = typeof BigInt64Array !== 'undefined';\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n    if ((a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      for (i of a.entries())\n        if (!equal(i[1], b.get(i[0]))) return false;\n      return true;\n    }\n\n    if ((a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      return true;\n    }\n\n    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        continue;\n      }\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var h=require(\"react\"),n=require(\"use-sync-external-store/shim\");function p(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var q=\"function\"===typeof Object.is?Object.is:p,r=n.useSyncExternalStore,t=h.useRef,u=h.useEffect,v=h.useMemo,w=h.useDebugValue;\nexports.useSyncExternalStoreWithSelector=function(a,b,e,l,g){var c=t(null);if(null===c.current){var f={hasValue:!1,value:null};c.current=f}else f=c.current;c=v(function(){function a(a){if(!c){c=!0;d=a;a=l(a);if(void 0!==g&&f.hasValue){var b=f.value;if(g(b,a))return k=b}return k=a}b=k;if(q(d,a))return b;var e=l(a);if(void 0!==g&&g(b,e))return b;d=a;return k=e}var c=!1,d,k,m=void 0===e?null:e;return[function(){return a(b())},null===m?void 0:function(){return a(m())}]},[b,e,l,g]);var d=r(a,c[0],c[1]);\nu(function(){f.hasValue=!0;f.value=d},[d]);w(d);return d};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\nvar shim = require('use-sync-external-store/shim');\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\nvar useSyncExternalStore = shim.useSyncExternalStore;\n\n// for CommonJS interop.\n\nvar useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\nfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n  // Use this to track the rendered snapshot.\n  var instRef = useRef(null);\n  var inst;\n\n  if (instRef.current === null) {\n    inst = {\n      hasValue: false,\n      value: null\n    };\n    instRef.current = inst;\n  } else {\n    inst = instRef.current;\n  }\n\n  var _useMemo = useMemo(function () {\n    // Track the memoized state using closure variables that are local to this\n    // memoized instance of a getSnapshot function. Intentionally not using a\n    // useRef hook, because that state would be shared across all concurrent\n    // copies of the hook/component.\n    var hasMemo = false;\n    var memoizedSnapshot;\n    var memoizedSelection;\n\n    var memoizedSelector = function (nextSnapshot) {\n      if (!hasMemo) {\n        // The first time the hook is called, there is no memoized result.\n        hasMemo = true;\n        memoizedSnapshot = nextSnapshot;\n\n        var _nextSelection = selector(nextSnapshot);\n\n        if (isEqual !== undefined) {\n          // Even if the selector has changed, the currently rendered selection\n          // may be equal to the new selection. We should attempt to reuse the\n          // current value if possible, to preserve downstream memoizations.\n          if (inst.hasValue) {\n            var currentSelection = inst.value;\n\n            if (isEqual(currentSelection, _nextSelection)) {\n              memoizedSelection = currentSelection;\n              return currentSelection;\n            }\n          }\n        }\n\n        memoizedSelection = _nextSelection;\n        return _nextSelection;\n      } // We may be able to reuse the previous invocation's result.\n\n\n      // We may be able to reuse the previous invocation's result.\n      var prevSnapshot = memoizedSnapshot;\n      var prevSelection = memoizedSelection;\n\n      if (objectIs(prevSnapshot, nextSnapshot)) {\n        // The snapshot is the same as last time. Reuse the previous selection.\n        return prevSelection;\n      } // The snapshot has changed, so we need to compute a new selection.\n\n\n      // The snapshot has changed, so we need to compute a new selection.\n      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n\n      // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n        return prevSelection;\n      }\n\n      memoizedSnapshot = nextSnapshot;\n      memoizedSelection = nextSelection;\n      return nextSelection;\n    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n    // Assigning this to a constant so that Flow knows it can't change.\n    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n    var getSnapshotWithSelector = function () {\n      return memoizedSelector(getSnapshot());\n    };\n\n    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n      return memoizedSelector(maybeGetServerSnapshot());\n    };\n    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n      getSelection = _useMemo[0],\n      getServerSelection = _useMemo[1];\n\n  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n  useEffect(function () {\n    inst.hasValue = true;\n    inst.value = value;\n  }, [value]);\n  useDebugValue(value);\n  return value;\n}\n\nexports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "import type { Editor } from '@tiptap/core'\nimport deepEqual from 'fast-deep-equal/es6/react'\nimport {\n  useDebugValue, useEffect, useLayoutEffect, useState,\n} from 'react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector'\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport type EditorStateSnapshot<TEditor extends Editor | null = Editor | null> = {\n  editor: TEditor;\n  transactionNumber: number;\n};\n\nexport type UseEditorStateOptions<\n  TSelectorResult,\n  TEditor extends Editor | null = Editor | null,\n> = {\n  /**\n   * The editor instance.\n   */\n  editor: TEditor;\n  /**\n   * A selector function to determine the value to compare for re-rendering.\n   */\n  selector: (context: EditorStateSnapshot<TEditor>) => TSelectorResult;\n  /**\n   * A custom equality function to determine if the editor should re-render.\n   * @default `deepEqual` from `fast-deep-equal`\n   */\n  equalityFn?: (a: TSelectorR<PERSON>ult, b: TSelectorResult | null) => boolean;\n};\n\n/**\n * To synchronize the editor instance with the component state,\n * we need to create a separate instance that is not affected by the component re-renders.\n */\nclass EditorStateManager<TEditor extends Editor | null = Editor | null> {\n  private transactionNumber = 0\n\n  private lastTransactionNumber = 0\n\n  private lastSnapshot: EditorStateSnapshot<TEditor>\n\n  private editor: TEditor\n\n  private subscribers = new Set<() => void>()\n\n  constructor(initialEditor: TEditor) {\n    this.editor = initialEditor\n    this.lastSnapshot = { editor: initialEditor, transactionNumber: 0 }\n\n    this.getSnapshot = this.getSnapshot.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.watch = this.watch.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getSnapshot(): EditorStateSnapshot<TEditor> {\n    if (this.transactionNumber === this.lastTransactionNumber) {\n      return this.lastSnapshot\n    }\n    this.lastTransactionNumber = this.transactionNumber\n    this.lastSnapshot = { editor: this.editor, transactionNumber: this.transactionNumber }\n    return this.lastSnapshot\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): EditorStateSnapshot<null> {\n    return { editor: null, transactionNumber: 0 }\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(callback: () => void): () => void {\n    this.subscribers.add(callback)\n    return () => {\n      this.subscribers.delete(callback)\n    }\n  }\n\n  /**\n   * Watch the editor instance for changes.\n   */\n  watch(nextEditor: Editor | null): undefined | (() => void) {\n    this.editor = nextEditor as TEditor\n\n    if (this.editor) {\n      /**\n       * This will force a re-render when the editor state changes.\n       * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.\n       * This could be more efficient, but it's a good trade-off for now.\n       */\n      const fn = () => {\n        this.transactionNumber += 1\n        this.subscribers.forEach(callback => callback())\n      }\n\n      const currentEditor = this.editor\n\n      currentEditor.on('transaction', fn)\n      return () => {\n        currentEditor.off('transaction', fn)\n      }\n    }\n\n    return undefined\n  }\n}\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor>\n): TSelectorResult;\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor | null>\n): TSelectorResult | null;\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor> | UseEditorStateOptions<TSelectorResult, Editor | null>,\n): TSelectorResult | null {\n  const [editorStateManager] = useState(() => new EditorStateManager(options.editor))\n\n  // Using the `useSyncExternalStore` hook to sync the editor instance with the component state\n  const selectedState = useSyncExternalStoreWithSelector(\n    editorStateManager.subscribe,\n    editorStateManager.getSnapshot,\n    editorStateManager.getServerSnapshot,\n    options.selector as UseEditorStateOptions<TSelectorResult, Editor | null>['selector'],\n    options.equalityFn ?? deepEqual,\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    return editorStateManager.watch(options.editor)\n  }, [options.editor, editorStateManager])\n\n  useDebugValue(selectedState)\n\n  return selectedState\n}\n", "import { type EditorOptions, Editor } from '@tiptap/core'\nimport {\n  DependencyList,\n  MutableRefObject,\n  useDebugValue,\n  useEffect,\n  useRef,\n  useState,\n} from 'react'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { useEditorState } from './useEditorState.js'\n\nconst isDev = process.env.NODE_ENV !== 'production'\nconst isSSR = typeof window === 'undefined'\nconst isNext = isSSR || Boolean(typeof window !== 'undefined' && (window as any).next)\n\n/**\n * The options for the `useEditor` hook.\n */\nexport type UseEditorOptions = Partial<EditorOptions> & {\n  /**\n   * Whether to render the editor on the first render.\n   * If client-side rendering, set this to `true`.\n   * If server-side rendering, set this to `false`.\n   * @default true\n   */\n  immediatelyRender?: boolean;\n  /**\n   * Whether to re-render the editor on each transaction.\n   * This is legacy behavior that will be removed in future versions.\n   * @default true\n   */\n  shouldRerenderOnTransaction?: boolean;\n};\n\n/**\n * This class handles the creation, destruction, and re-creation of the editor instance.\n */\nclass EditorInstanceManager {\n  /**\n   * The current editor instance.\n   */\n  private editor: Editor | null = null\n\n  /**\n   * The most recent options to apply to the editor.\n   */\n  private options: MutableRefObject<UseEditorOptions>\n\n  /**\n   * The subscriptions to notify when the editor instance\n   * has been created or destroyed.\n   */\n  private subscriptions = new Set<() => void>()\n\n  /**\n   * A timeout to destroy the editor if it was not mounted within a time frame.\n   */\n  private scheduledDestructionTimeout: ReturnType<typeof setTimeout> | undefined\n\n  /**\n   * Whether the editor has been mounted.\n   */\n  private isComponentMounted = false\n\n  /**\n   * The most recent dependencies array.\n   */\n  private previousDeps: DependencyList | null = null\n\n  /**\n   * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.\n   */\n  public instanceId = ''\n\n  constructor(options: MutableRefObject<UseEditorOptions>) {\n    this.options = options\n    this.subscriptions = new Set<() => void>()\n    this.setEditor(this.getInitialEditor())\n    this.scheduleDestroy()\n\n    this.getEditor = this.getEditor.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n    this.refreshEditorInstance = this.refreshEditorInstance.bind(this)\n    this.scheduleDestroy = this.scheduleDestroy.bind(this)\n    this.onRender = this.onRender.bind(this)\n    this.createEditor = this.createEditor.bind(this)\n  }\n\n  private setEditor(editor: Editor | null) {\n    this.editor = editor\n    this.instanceId = Math.random().toString(36).slice(2, 9)\n\n    // Notify all subscribers that the editor instance has been created\n    this.subscriptions.forEach(cb => cb())\n  }\n\n  private getInitialEditor() {\n    if (this.options.current.immediatelyRender === undefined) {\n      if (isSSR || isNext) {\n        // TODO in the next major release, we should throw an error here\n        if (isDev) {\n          /**\n           * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd\n           * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.\n           */\n          console.warn(\n            'Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.',\n          )\n        }\n\n        // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production\n        return null\n      }\n\n      // Default to immediately rendering when client-side rendering\n      return this.createEditor()\n    }\n\n    if (this.options.current.immediatelyRender && isSSR && isDev) {\n      // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.\n      throw new Error(\n        'Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.',\n      )\n    }\n\n    if (this.options.current.immediatelyRender) {\n      return this.createEditor()\n    }\n\n    return null\n  }\n\n  /**\n   * Create a new editor instance. And attach event listeners.\n   */\n  private createEditor(): Editor {\n    const optionsToApply: Partial<EditorOptions> = {\n      ...this.options.current,\n      // Always call the most recent version of the callback function by default\n      onBeforeCreate: (...args) => this.options.current.onBeforeCreate?.(...args),\n      onBlur: (...args) => this.options.current.onBlur?.(...args),\n      onCreate: (...args) => this.options.current.onCreate?.(...args),\n      onDestroy: (...args) => this.options.current.onDestroy?.(...args),\n      onFocus: (...args) => this.options.current.onFocus?.(...args),\n      onSelectionUpdate: (...args) => this.options.current.onSelectionUpdate?.(...args),\n      onTransaction: (...args) => this.options.current.onTransaction?.(...args),\n      onUpdate: (...args) => this.options.current.onUpdate?.(...args),\n      onContentError: (...args) => this.options.current.onContentError?.(...args),\n      onDrop: (...args) => this.options.current.onDrop?.(...args),\n      onPaste: (...args) => this.options.current.onPaste?.(...args),\n    }\n    const editor = new Editor(optionsToApply)\n\n    // no need to keep track of the event listeners, they will be removed when the editor is destroyed\n\n    return editor\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getEditor(): Editor | null {\n    return this.editor\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): null {\n    return null\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(onStoreChange: () => void) {\n    this.subscriptions.add(onStoreChange)\n\n    return () => {\n      this.subscriptions.delete(onStoreChange)\n    }\n  }\n\n  static compareOptions(a: UseEditorOptions, b: UseEditorOptions) {\n    return (Object.keys(a) as (keyof UseEditorOptions)[]).every(key => {\n      if (['onCreate', 'onBeforeCreate', 'onDestroy', 'onUpdate', 'onTransaction', 'onFocus', 'onBlur', 'onSelectionUpdate', 'onContentError', 'onDrop', 'onPaste'].includes(key)) {\n        // we don't want to compare callbacks, they are always different and only registered once\n        return true\n      }\n\n      // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here\n      if (key === 'extensions' && a.extensions && b.extensions) {\n        if (a.extensions.length !== b.extensions.length) {\n          return false\n        }\n        return a.extensions.every((extension, index) => {\n          if (extension !== b.extensions?.[index]) {\n            return false\n          }\n          return true\n        })\n      }\n      if (a[key] !== b[key]) {\n        // if any of the options have changed, we should update the editor options\n        return false\n      }\n      return true\n    })\n  }\n\n  /**\n   * On each render, we will create, update, or destroy the editor instance.\n   * @param deps The dependencies to watch for changes\n   * @returns A cleanup function\n   */\n  onRender(deps: DependencyList) {\n    // The returned callback will run on each render\n    return () => {\n      this.isComponentMounted = true\n      // Cleanup any scheduled destructions, since we are currently rendering\n      clearTimeout(this.scheduledDestructionTimeout)\n\n      if (this.editor && !this.editor.isDestroyed && deps.length === 0) {\n        // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally\n        if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {\n          // But, the options are different, so we need to update the editor options\n          // Still, this is faster than re-creating the editor\n          this.editor.setOptions({\n            ...this.options.current,\n            editable: this.editor.isEditable,\n          })\n        }\n      } else {\n        // When the editor:\n        // - does not yet exist\n        // - is destroyed\n        // - the deps array changes\n        // We need to destroy the editor instance and re-initialize it\n        this.refreshEditorInstance(deps)\n      }\n\n      return () => {\n        this.isComponentMounted = false\n        this.scheduleDestroy()\n      }\n    }\n  }\n\n  /**\n   * Recreate the editor instance if the dependencies have changed.\n   */\n  private refreshEditorInstance(deps: DependencyList) {\n    if (this.editor && !this.editor.isDestroyed) {\n      // Editor instance already exists\n      if (this.previousDeps === null) {\n        // If lastDeps has not yet been initialized, reuse the current editor instance\n        this.previousDeps = deps\n        return\n      }\n      const depsAreEqual = this.previousDeps.length === deps.length\n        && this.previousDeps.every((dep, index) => dep === deps[index])\n\n      if (depsAreEqual) {\n        // deps exist and are equal, no need to recreate\n        return\n      }\n    }\n\n    if (this.editor && !this.editor.isDestroyed) {\n      // Destroy the editor instance if it exists\n      this.editor.destroy()\n    }\n\n    this.setEditor(this.createEditor())\n\n    // Update the lastDeps to the current deps\n    this.previousDeps = deps\n  }\n\n  /**\n   * Schedule the destruction of the editor instance.\n   * This will only destroy the editor if it was not mounted on the next tick.\n   * This is to avoid destroying the editor instance when it's actually still mounted.\n   */\n  private scheduleDestroy() {\n    const currentInstanceId = this.instanceId\n    const currentEditor = this.editor\n\n    // Wait two ticks to see if the component is still mounted\n    this.scheduledDestructionTimeout = setTimeout(() => {\n      if (this.isComponentMounted && this.instanceId === currentInstanceId) {\n        // If still mounted on the following tick, with the same instanceId, do not destroy the editor\n        if (currentEditor) {\n          // just re-apply options as they might have changed\n          currentEditor.setOptions(this.options.current)\n        }\n        return\n      }\n      if (currentEditor && !currentEditor.isDestroyed) {\n        currentEditor.destroy()\n        if (this.instanceId === currentInstanceId) {\n          this.setEditor(null)\n        }\n      }\n      // This allows the effect to run again between ticks\n      // which may save us from having to re-create the editor\n    }, 1)\n  }\n}\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(\n  options: UseEditorOptions & { immediatelyRender: true },\n  deps?: DependencyList\n): Editor;\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(options?: UseEditorOptions, deps?: DependencyList): Editor | null;\n\nexport function useEditor(\n  options: UseEditorOptions = {},\n  deps: DependencyList = [],\n): Editor | null {\n  const mostRecentOptions = useRef(options)\n\n  mostRecentOptions.current = options\n\n  const [instanceManager] = useState(() => new EditorInstanceManager(mostRecentOptions))\n\n  const editor = useSyncExternalStore(\n    instanceManager.subscribe,\n    instanceManager.getEditor,\n    instanceManager.getServerSnapshot,\n  )\n\n  useDebugValue(editor)\n\n  // This effect will handle creating/updating the editor instance\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(instanceManager.onRender(deps))\n\n  // The default behavior is to re-render on each transaction\n  // This is legacy behavior that will be removed in future versions\n  useEditorState({\n    editor,\n    selector: ({ transactionNumber }) => {\n      if (options.shouldRerenderOnTransaction === false) {\n        // This will prevent the editor from re-rendering on each transaction\n        return null\n      }\n\n      // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`\n      if (options.immediatelyRender && transactionNumber === 0) {\n        return 0\n      }\n      return transactionNumber + 1\n    },\n  })\n\n  return editor\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  createContext, HTMLAttributes, ReactNode, useContext,\n} from 'react'\n\nimport { EditorContent } from './EditorContent.js'\nimport { useEditor, UseEditorOptions } from './useEditor.js'\n\nexport type EditorContextValue = {\n  editor: Editor | null;\n}\n\nexport const EditorContext = createContext<EditorContextValue>({\n  editor: null,\n})\n\nexport const EditorConsumer = EditorContext.Consumer\n\n/**\n * A hook to get the current editor instance.\n */\nexport const useCurrentEditor = () => useContext(EditorContext)\n\nexport type EditorProviderProps = {\n  children?: ReactNode;\n  slotBefore?: ReactNode;\n  slotAfter?: ReactNode;\n  editorContainerProps?: HTMLAttributes<HTMLDivElement>;\n} & UseEditorOptions\n\n/**\n * This is the provider component for the editor.\n * It allows the editor to be accessible across the entire component tree\n * with `useCurrentEditor`.\n */\nexport function EditorProvider({\n  children, slotAfter, slotBefore, editorContainerProps = {}, ...editorOptions\n}: EditorProviderProps) {\n  const editor = useEditor(editorOptions)\n\n  if (!editor) {\n    return null\n  }\n\n  return (\n    <EditorContext.Provider value={{ editor }}>\n      {slotBefore}\n      <EditorConsumer>\n        {({ editor: currentEditor }) => (\n          <EditorContent editor={currentEditor} {...editorContainerProps} />\n        )}\n      </EditorConsumer>\n      {children}\n      {slotAfter}\n    </EditorContext.Provider>\n  )\n}\n", "import { BubbleMenuPlugin, BubbleMenuPluginProps } from '@tiptap/extension-bubble-menu'\nimport React, { useEffect, useState } from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;\n\nexport type BubbleMenuProps = Omit<Optional<BubbleMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: BubbleMenuPluginProps['editor'] | null;\n  className?: string;\n  children: React.ReactNode;\n  updateDelay?: number;\n};\n\nexport const BubbleMenu = (props: BubbleMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'bubbleMenu', editor, tippyOptions = {}, updateDelay, shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = BubbleMenuPlugin({\n      updateDelay,\n      editor: menuEditor,\n      element,\n      pluginKey,\n      shouldShow,\n      tippyOptions,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [props.editor, currentEditor, element])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { FloatingMenuPlugin, FloatingMenuPluginProps } from '@tiptap/extension-floating-menu'\nimport React, {\n  useEffect, useState,\n} from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>\n\nexport type FloatingMenuProps = Omit<Optional<FloatingMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: FloatingMenuPluginProps['editor'] | null;\n  className?: string,\n  children: React.ReactNode\n}\n\nexport const FloatingMenu = (props: FloatingMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'floatingMenu',\n      editor,\n      tippyOptions = {},\n      shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('FloatingMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = FloatingMenuPlugin({\n      pluginKey,\n      editor: menuEditor,\n      element,\n      tippyOptions,\n      shouldShow,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [\n    props.editor,\n    currentEditor,\n    element,\n  ])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { createContext, useContext } from 'react'\n\nexport interface ReactNodeViewContextProps {\n  onDragStart: (event: DragEvent) => void,\n  nodeViewContentRef: (element: HTMLElement | null) => void,\n}\n\nexport const ReactNodeViewContext = createContext<Partial<ReactNodeViewContextProps>>({\n  onDragStart: undefined,\n})\n\nexport const useReactNodeView = () => useContext(ReactNodeViewContext)\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewContentProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewContent: React.FC<NodeViewContentProps> = props => {\n  const Tag = props.as || 'div'\n  const { nodeViewContentRef } = useReactNodeView()\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={nodeViewContentRef}\n      data-node-view-content=\"\"\n      style={{\n        whiteSpace: 'pre-wrap',\n        ...props.style,\n      }}\n    />\n  )\n}\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewWrapperProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewWrapper: React.FC<NodeViewWrapperProps> = React.forwardRef((props, ref) => {\n  const { onDragStart } = useReactNodeView()\n  const Tag = props.as || 'div'\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={ref}\n      data-node-view-wrapper=\"\"\n      onDragStart={onDragStart}\n      style={{\n        whiteSpace: 'normal',\n        ...props.style,\n      }}\n    />\n  )\n})\n", "import { Editor } from '@tiptap/core'\nimport React from 'react'\nimport { flushSync } from 'react-dom'\n\nimport { EditorWithContentComponent } from './Editor.js'\n\n/**\n * Check if a component is a class component.\n * @param Component\n * @returns {boolean}\n */\nfunction isClassComponent(Component: any) {\n  return !!(\n    typeof Component === 'function'\n    && Component.prototype\n    && Component.prototype.isReactComponent\n  )\n}\n\n/**\n * Check if a component is a forward ref component.\n * @param Component\n * @returns {boolean}\n */\nfunction isForwardRefComponent(Component: any) {\n  return !!(\n    typeof Component === 'object'\n    && Component.$$typeof?.toString() === 'Symbol(react.forward_ref)'\n  )\n}\n\nexport interface ReactRendererOptions {\n  /**\n   * The editor instance.\n   * @type {Editor}\n   */\n  editor: Editor,\n\n  /**\n   * The props for the component.\n   * @type {Record<string, any>}\n   * @default {}\n   */\n  props?: Record<string, any>,\n\n  /**\n   * The tag name of the element.\n   * @type {string}\n   * @default 'div'\n   */\n  as?: string,\n\n  /**\n   * The class name of the element.\n   * @type {string}\n   * @default ''\n   * @example 'foo bar'\n   */\n  className?: string,\n}\n\ntype ComponentType<R, P> =\n  React.ComponentClass<P> |\n  React.FunctionComponent<P> |\n  React.ForwardRefExoticComponent<React.PropsWithoutRef<P> & React.RefAttributes<R>>;\n\n/**\n * The ReactRenderer class. It's responsible for rendering React components inside the editor.\n * @example\n * new ReactRenderer(MyComponent, {\n *   editor,\n *   props: {\n *     foo: 'bar',\n *   },\n *   as: 'span',\n * })\n*/\nexport class ReactRenderer<R = unknown, P extends Record<string, any> = object> {\n  id: string\n\n  editor: Editor\n\n  component: any\n\n  element: Element\n\n  props: P\n\n  reactElement: React.ReactNode\n\n  ref: R | null = null\n\n  /**\n   * Immediately creates element and renders the provided React component.\n   */\n  constructor(component: ComponentType<R, P>, {\n    editor,\n    props = {},\n    as = 'div',\n    className = '',\n  }: ReactRendererOptions) {\n    this.id = Math.floor(Math.random() * 0xFFFFFFFF).toString()\n    this.component = component\n    this.editor = editor as EditorWithContentComponent\n    this.props = props as P\n    this.element = document.createElement(as)\n    this.element.classList.add('react-renderer')\n\n    if (className) {\n      this.element.classList.add(...className.split(' '))\n    }\n\n    if (this.editor.isInitialized) {\n      // On first render, we need to flush the render synchronously\n      // Renders afterwards can be async, but this fixes a cursor positioning issue\n      flushSync(() => {\n        this.render()\n      })\n    } else {\n      this.render()\n    }\n  }\n\n  /**\n   * Render the React component.\n   */\n  render(): void {\n    const Component = this.component\n    const props = this.props\n    const editor = this.editor as EditorWithContentComponent\n\n    if (isClassComponent(Component) || isForwardRefComponent(Component)) {\n      // @ts-ignore This is a hack to make the ref work\n      props.ref = (ref: R) => {\n        this.ref = ref\n      }\n    }\n\n    this.reactElement = <Component {...props} />\n\n    editor?.contentComponent?.setRenderer(this.id, this)\n  }\n\n  /**\n   * Re-renders the React component with new props.\n   */\n  updateProps(props: Record<string, any> = {}): void {\n    this.props = {\n      ...this.props,\n      ...props,\n    }\n\n    this.render()\n  }\n\n  /**\n   * Destroy the React component.\n   */\n  destroy(): void {\n    const editor = this.editor as EditorWithContentComponent\n\n    editor?.contentComponent?.removeRenderer(this.id)\n  }\n\n  /**\n   * Update the attributes of the element that holds the React component.\n   */\n  updateAttributes(attributes: Record<string, string>): void {\n    Object.keys(attributes).forEach(key => {\n      this.element.setAttribute(key, attributes[key])\n    })\n  }\n}\n", "import {\n  DecorationWithType,\n  Editor,\n  getRenderedAttributes,\n  NodeView,\n  NodeViewProps,\n  NodeViewRenderer,\n  NodeViewRendererOptions,\n} from '@tiptap/core'\nimport { Node, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport React, { ComponentType } from 'react'\n\nimport { EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\nimport { ReactNodeViewContext, ReactNodeViewContextProps } from './useReactNodeView.js'\n\nexport interface ReactNodeViewRendererOptions extends NodeViewRendererOptions {\n  /**\n   * This function is called when the node view is updated.\n   * It allows you to compare the old node with the new node and decide if the component should update.\n   */\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode;\n        oldDecorations: readonly Decoration[];\n        oldInnerDecorations: DecorationSource;\n        newNode: ProseMirrorNode;\n        newDecorations: readonly Decoration[];\n        innerDecorations: DecorationSource;\n        updateProps: () => void;\n      }) => boolean)\n    | null;\n  /**\n   * The tag name of the element wrapping the React component.\n   */\n  as?: string;\n  /**\n   * The class name of the element wrapping the React component.\n   */\n  className?: string;\n  /**\n   * Attributes that should be applied to the element wrapping the React component.\n   * If this is a function, it will be called each time the node view is updated.\n   * If this is an object, it will be applied once when the node view is mounted.\n   */\n  attrs?:\n    | Record<string, string>\n    | ((props: {\n        node: ProseMirrorNode;\n        HTMLAttributes: Record<string, any>;\n      }) => Record<string, string>);\n}\n\nexport class ReactNodeView<\n  Component extends ComponentType<NodeViewProps> = ComponentType<NodeViewProps>,\n  NodeEditor extends Editor = Editor,\n  Options extends ReactNodeViewRendererOptions = ReactNodeViewRendererOptions,\n> extends NodeView<Component, NodeEditor, Options> {\n  /**\n   * The renderer instance.\n   */\n  renderer!: ReactRenderer<unknown, NodeViewProps>\n\n  /**\n   * The element that holds the rich-text content of the node.\n   */\n  contentDOMElement!: HTMLElement | null\n\n  /**\n   * Setup the React component.\n   * Called on initialization.\n   */\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n    } satisfies NodeViewProps\n\n    if (!(this.component as any).displayName) {\n      const capitalizeFirstChar = (string: string): string => {\n        return string.charAt(0).toUpperCase() + string.substring(1)\n      }\n\n      this.component.displayName = capitalizeFirstChar(this.extension.name)\n    }\n\n    const onDragStart = this.onDragStart.bind(this)\n    const nodeViewContentRef: ReactNodeViewContextProps['nodeViewContentRef'] = element => {\n      if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {\n        element.appendChild(this.contentDOMElement)\n      }\n    }\n    const context = { onDragStart, nodeViewContentRef }\n    const Component = this.component\n    // For performance reasons, we memoize the provider component\n    // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n    const ReactNodeViewProvider: React.FunctionComponent<NodeViewProps> = React.memo(\n      componentProps => {\n        return (\n          <ReactNodeViewContext.Provider value={context}>\n            {React.createElement(Component, componentProps)}\n          </ReactNodeViewContext.Provider>\n        )\n      },\n    )\n\n    ReactNodeViewProvider.displayName = 'ReactNodeView'\n\n    if (this.node.isLeaf) {\n      this.contentDOMElement = null\n    } else if (this.options.contentDOMElementTag) {\n      this.contentDOMElement = document.createElement(this.options.contentDOMElementTag)\n    } else {\n      this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div')\n    }\n\n    if (this.contentDOMElement) {\n      this.contentDOMElement.dataset.nodeViewContentReact = ''\n      // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari\n      // With this fix it seems to work fine\n      // See: https://github.com/ueberdosis/tiptap/issues/1197\n      this.contentDOMElement.style.whiteSpace = 'inherit'\n    }\n\n    let as = this.node.isInline ? 'span' : 'div'\n\n    if (this.options.as) {\n      as = this.options.as\n    }\n\n    const { className = '' } = this.options\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n\n    this.renderer = new ReactRenderer(ReactNodeViewProvider, {\n      editor: this.editor,\n      props,\n      as,\n      className: `node-${this.node.type.name} ${className}`.trim(),\n    })\n\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n    this.updateElementAttributes()\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (\n      this.renderer.element.firstElementChild\n      && !this.renderer.element.firstElementChild?.hasAttribute('data-node-view-wrapper')\n    ) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.contentDOMElement\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(\n    node: Node,\n    decorations: readonly Decoration[],\n    innerDecorations: DecorationSource,\n  ): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.renderer.updateProps(props)\n      if (typeof this.options.attrs === 'function') {\n        this.updateElementAttributes()\n      }\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (\n      node === this.node\n      && this.decorations === decorations\n      && this.innerDecorations === innerDecorations\n    ) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    this.renderer.element.classList.add('ProseMirror-selectednode')\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    this.renderer.element.classList.remove('ProseMirror-selectednode')\n  }\n\n  /**\n   * Destroy the React component instance.\n   */\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n    this.contentDOMElement = null\n  }\n\n  /**\n   * Update the attributes of the top-level element that holds the React component.\n   * Applying the attributes defined in the `attrs` option.\n   */\n  updateElementAttributes() {\n    if (this.options.attrs) {\n      let attrsObj: Record<string, string> = {}\n\n      if (typeof this.options.attrs === 'function') {\n        const extensionAttributes = this.editor.extensionManager.attributes\n        const HTMLAttributes = getRenderedAttributes(this.node, extensionAttributes)\n\n        attrsObj = this.options.attrs({ node: this.node, HTMLAttributes })\n      } else {\n        attrsObj = this.options.attrs\n      }\n\n      this.renderer.updateAttributes(attrsObj)\n    }\n  }\n}\n\n/**\n * Create a React node view renderer.\n */\nexport function ReactNodeViewRenderer(\n  component: ComponentType<NodeViewProps>,\n  options?: Partial<ReactNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as EditorWithContentComponent).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n\n    return new ReactNodeView(component, props, options)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,iBAAiB,CAAC,KAAK,QAAQ,OAAO,IAAI;AAC9C,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,sBAAmC,eAAe,OAAO,SAAU,KAAK,WAAW;AAC5F,SAAO,IAAI,OAAO,CAAC,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AACpE,GAAG,CAAC,CAAC;AACE,IAAI,aAA0B,CAAC,EAAE,OAAO,gBAAgB,CAAC,IAAI,CAAC,EAAE,OAAO,SAAU,KAAK,WAAW;AACtG,SAAO,IAAI,OAAO,CAAC,WAAW,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AAC/E,GAAG,CAAC,CAAC;AAEE,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB,CAAC,YAAY,MAAM,WAAW,YAAY,MAAM,WAAW,aAAa,OAAO,UAAU;;;AC9BtG,SAAR,YAA6B,SAAS;AAC3C,SAAO,WAAW,QAAQ,YAAY,IAAI,YAAY,IAAI;AAC5D;;;ACFe,SAAR,UAA2B,MAAM;AACtC,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,SAAS,MAAM,mBAAmB;AACzC,QAAI,gBAAgB,KAAK;AACzB,WAAO,gBAAgB,cAAc,eAAe,SAAS;AAAA,EAC/D;AAEA,SAAO;AACT;;;ACTA,SAAS,UAAU,MAAM;AACvB,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,aAAa,MAAM;AAE1B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;;;AChBA,SAAS,YAAY,MAAM;AACzB,MAAI,QAAQ,KAAK;AACjB,SAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,QAAI,QAAQ,MAAM,OAAO,IAAI,KAAK,CAAC;AACnC,QAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,QAAI,UAAU,MAAM,SAAS,IAAI;AAEjC,QAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,IACF;AAKA,WAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAUA,OAAM;AAC9C,UAAI,QAAQ,WAAWA,KAAI;AAE3B,UAAI,UAAU,OAAO;AACnB,gBAAQ,gBAAgBA,KAAI;AAAA,MAC9B,OAAO;AACL,gBAAQ,aAAaA,OAAM,UAAU,OAAO,KAAK,KAAK;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM;AAClB,MAAI,gBAAgB;AAAA,IAClB,QAAQ;AAAA,MACN,UAAU,MAAM,QAAQ;AAAA,MACxB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,WAAW,CAAC;AAAA,EACd;AACA,SAAO,OAAO,MAAM,SAAS,OAAO,OAAO,cAAc,MAAM;AAC/D,QAAM,SAAS;AAEf,MAAI,MAAM,SAAS,OAAO;AACxB,WAAO,OAAO,MAAM,SAAS,MAAM,OAAO,cAAc,KAAK;AAAA,EAC/D;AAEA,SAAO,WAAY;AACjB,WAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,UAAI,UAAU,MAAM,SAAS,IAAI;AACjC,UAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,UAAI,kBAAkB,OAAO,KAAK,MAAM,OAAO,eAAe,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,cAAc,IAAI,CAAC;AAE9G,UAAI,QAAQ,gBAAgB,OAAO,SAAUC,QAAO,UAAU;AAC5D,QAAAA,OAAM,QAAQ,IAAI;AAClB,eAAOA;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,UAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,MACF;AAEA,aAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,WAAW;AACnD,gBAAQ,gBAAgB,SAAS;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAGA,IAAO,sBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ;AAAA,EACA,UAAU,CAAC,eAAe;AAC5B;;;AClFe,SAAR,iBAAkC,WAAW;AAClD,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;;;ACHO,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,QAAQ,KAAK;;;ACFT,SAAR,cAA+B;AACpC,MAAI,SAAS,UAAU;AAEvB,MAAI,UAAU,QAAQ,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM,GAAG;AACnE,WAAO,OAAO,OAAO,IAAI,SAAU,MAAM;AACvC,aAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,IACjC,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AAEA,SAAO,UAAU;AACnB;;;ACTe,SAAR,mBAAoC;AACzC,SAAO,CAAC,iCAAiC,KAAK,YAAY,CAAC;AAC7D;;;ACCe,SAAR,sBAAuC,SAAS,cAAc,iBAAiB;AACpF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AAEA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AAEA,MAAI,aAAa,QAAQ,sBAAsB;AAC/C,MAAI,SAAS;AACb,MAAI,SAAS;AAEb,MAAI,gBAAgB,cAAc,OAAO,GAAG;AAC1C,aAAS,QAAQ,cAAc,IAAI,MAAM,WAAW,KAAK,IAAI,QAAQ,eAAe,IAAI;AACxF,aAAS,QAAQ,eAAe,IAAI,MAAM,WAAW,MAAM,IAAI,QAAQ,gBAAgB,IAAI;AAAA,EAC7F;AAEA,MAAI,OAAO,UAAU,OAAO,IAAI,UAAU,OAAO,IAAI,QACjD,iBAAiB,KAAK;AAE1B,MAAI,mBAAmB,CAAC,iBAAiB,KAAK;AAC9C,MAAI,KAAK,WAAW,QAAQ,oBAAoB,iBAAiB,eAAe,aAAa,MAAM;AACnG,MAAI,KAAK,WAAW,OAAO,oBAAoB,iBAAiB,eAAe,YAAY,MAAM;AACjG,MAAI,QAAQ,WAAW,QAAQ;AAC/B,MAAI,SAAS,WAAW,SAAS;AACjC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;;;ACrCe,SAAR,cAA+B,SAAS;AAC7C,MAAI,aAAa,sBAAsB,OAAO;AAG9C,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,QAAQ;AAErB,MAAI,KAAK,IAAI,WAAW,QAAQ,KAAK,KAAK,GAAG;AAC3C,YAAQ,WAAW;AAAA,EACrB;AAEA,MAAI,KAAK,IAAI,WAAW,SAAS,MAAM,KAAK,GAAG;AAC7C,aAAS,WAAW;AAAA,EACtB;AAEA,SAAO;AAAA,IACL,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;;;ACvBe,SAAR,SAA0B,QAAQ,OAAO;AAC9C,MAAI,WAAW,MAAM,eAAe,MAAM,YAAY;AAEtD,MAAI,OAAO,SAAS,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT,WACS,YAAY,aAAa,QAAQ,GAAG;AACzC,QAAI,OAAO;AAEX,OAAG;AACD,UAAI,QAAQ,OAAO,WAAW,IAAI,GAAG;AACnC,eAAO;AAAA,MACT;AAGA,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC,SAAS;AAAA,EACX;AAGF,SAAO;AACT;;;ACrBe,SAAR,iBAAkC,SAAS;AAChD,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;;;ACFe,SAAR,eAAgC,SAAS;AAC9C,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,QAAQ,YAAY,OAAO,CAAC,KAAK;AAChE;;;ACFe,SAAR,mBAAoC,SAAS;AAElD,WAAS,UAAU,OAAO,IAAI,QAAQ;AAAA;AAAA,IACtC,QAAQ;AAAA,QAAa,OAAO,UAAU;AACxC;;;ACFe,SAAR,cAA+B,SAAS;AAC7C,MAAI,YAAY,OAAO,MAAM,QAAQ;AACnC,WAAO;AAAA,EACT;AAEA;AAAA;AAAA;AAAA;AAAA,IAGE,QAAQ;AAAA,IACR,QAAQ;AAAA,KACR,aAAa,OAAO,IAAI,QAAQ,OAAO;AAAA;AAAA,IAEvC,mBAAmB,OAAO;AAAA;AAG9B;;;ACVA,SAAS,oBAAoB,SAAS;AACpC,MAAI,CAAC,cAAc,OAAO;AAAA,EAC1B,iBAAiB,OAAO,EAAE,aAAa,SAAS;AAC9C,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ;AACjB;AAIA,SAAS,mBAAmB,SAAS;AACnC,MAAI,YAAY,WAAW,KAAK,YAAY,CAAC;AAC7C,MAAI,OAAO,WAAW,KAAK,YAAY,CAAC;AAExC,MAAI,QAAQ,cAAc,OAAO,GAAG;AAElC,QAAI,aAAa,iBAAiB,OAAO;AAEzC,QAAI,WAAW,aAAa,SAAS;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,cAAc,cAAc,OAAO;AAEvC,MAAI,aAAa,WAAW,GAAG;AAC7B,kBAAc,YAAY;AAAA,EAC5B;AAEA,SAAO,cAAc,WAAW,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,YAAY,WAAW,CAAC,IAAI,GAAG;AAC3F,QAAI,MAAM,iBAAiB,WAAW;AAItC,QAAI,IAAI,cAAc,UAAU,IAAI,gBAAgB,UAAU,IAAI,YAAY,WAAW,CAAC,aAAa,aAAa,EAAE,QAAQ,IAAI,UAAU,MAAM,MAAM,aAAa,IAAI,eAAe,YAAY,aAAa,IAAI,UAAU,IAAI,WAAW,QAAQ;AACpP,aAAO;AAAA,IACT,OAAO;AACL,oBAAc,YAAY;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO;AACT;AAIe,SAAR,gBAAiC,SAAS;AAC/C,MAAIC,UAAS,UAAU,OAAO;AAC9B,MAAI,eAAe,oBAAoB,OAAO;AAE9C,SAAO,gBAAgB,eAAe,YAAY,KAAK,iBAAiB,YAAY,EAAE,aAAa,UAAU;AAC3G,mBAAe,oBAAoB,YAAY;AAAA,EACjD;AAEA,MAAI,iBAAiB,YAAY,YAAY,MAAM,UAAU,YAAY,YAAY,MAAM,UAAU,iBAAiB,YAAY,EAAE,aAAa,WAAW;AAC1J,WAAOA;AAAA,EACT;AAEA,SAAO,gBAAgB,mBAAmB,OAAO,KAAKA;AACxD;;;ACpEe,SAAR,yBAA0C,WAAW;AAC1D,SAAO,CAAC,OAAO,QAAQ,EAAE,QAAQ,SAAS,KAAK,IAAI,MAAM;AAC3D;;;ACDO,SAAS,OAAOC,MAAK,OAAOC,MAAK;AACtC,SAAO,IAAQD,MAAK,IAAQ,OAAOC,IAAG,CAAC;AACzC;AACO,SAAS,eAAeD,MAAK,OAAOC,MAAK;AAC9C,MAAI,IAAI,OAAOD,MAAK,OAAOC,IAAG;AAC9B,SAAO,IAAIA,OAAMA,OAAM;AACzB;;;ACPe,SAAR,qBAAsC;AAC3C,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;;;ACNe,SAAR,mBAAoC,eAAe;AACxD,SAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,GAAG,aAAa;AAC9D;;;ACHe,SAAR,gBAAiC,OAAO,MAAM;AACnD,SAAO,KAAK,OAAO,SAAU,SAAS,KAAK;AACzC,YAAQ,GAAG,IAAI;AACf,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;;;ACKA,IAAI,kBAAkB,SAASC,iBAAgB,SAAS,OAAO;AAC7D,YAAU,OAAO,YAAY,aAAa,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IAC/E,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,SAAO,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AAC5G;AAEA,SAAS,MAAM,MAAM;AACnB,MAAI;AAEJ,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK,MACZ,UAAU,KAAK;AACnB,MAAI,eAAe,MAAM,SAAS;AAClC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,OAAO,yBAAyB,aAAa;AACjD,MAAI,aAAa,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK;AACzD,MAAI,MAAM,aAAa,WAAW;AAElC,MAAI,CAAC,gBAAgB,CAACA,gBAAe;AACnC;AAAA,EACF;AAEA,MAAI,gBAAgB,gBAAgB,QAAQ,SAAS,KAAK;AAC1D,MAAI,YAAY,cAAc,YAAY;AAC1C,MAAI,UAAU,SAAS,MAAM,MAAM;AACnC,MAAI,UAAU,SAAS,MAAM,SAAS;AACtC,MAAI,UAAU,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM,UAAU,IAAI,IAAIA,eAAc,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG;AACrH,MAAI,YAAYA,eAAc,IAAI,IAAI,MAAM,MAAM,UAAU,IAAI;AAChE,MAAI,oBAAoB,gBAAgB,YAAY;AACpD,MAAI,aAAa,oBAAoB,SAAS,MAAM,kBAAkB,gBAAgB,IAAI,kBAAkB,eAAe,IAAI;AAC/H,MAAI,oBAAoB,UAAU,IAAI,YAAY;AAGlD,MAAIC,OAAM,cAAc,OAAO;AAC/B,MAAIC,OAAM,aAAa,UAAU,GAAG,IAAI,cAAc,OAAO;AAC7D,MAAI,SAAS,aAAa,IAAI,UAAU,GAAG,IAAI,IAAI;AACnD,MAAIC,UAAS,OAAOF,MAAK,QAAQC,IAAG;AAEpC,MAAI,WAAW;AACf,QAAM,cAAc,IAAI,KAAK,wBAAwB,CAAC,GAAG,sBAAsB,QAAQ,IAAIC,SAAQ,sBAAsB,eAAeA,UAAS,QAAQ;AAC3J;AAEA,SAASC,QAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,wBAAwB;AAEzE,MAAI,gBAAgB,MAAM;AACxB;AAAA,EACF;AAGA,MAAI,OAAO,iBAAiB,UAAU;AACpC,mBAAe,MAAM,SAAS,OAAO,cAAc,YAAY;AAE/D,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,MAAM,SAAS,QAAQ,YAAY,GAAG;AAClD;AAAA,EACF;AAEA,QAAM,SAAS,QAAQ;AACzB;AAGA,IAAO,gBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,QAAQA;AAAA,EACR,UAAU,CAAC,eAAe;AAAA,EAC1B,kBAAkB,CAAC,iBAAiB;AACtC;;;ACzFe,SAAR,aAA8B,WAAW;AAC9C,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;;;ACOA,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAIA,SAAS,kBAAkB,MAAM,KAAK;AACpC,MAAI,IAAI,KAAK,GACT,IAAI,KAAK;AACb,MAAI,MAAM,IAAI,oBAAoB;AAClC,SAAO;AAAA,IACL,GAAG,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,IAC3B,GAAG,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,EAC7B;AACF;AAEO,SAAS,YAAY,OAAO;AACjC,MAAI;AAEJ,MAAIC,UAAS,MAAM,QACf,aAAa,MAAM,YACnB,YAAY,MAAM,WAClB,YAAY,MAAM,WAClB,UAAU,MAAM,SAChB,WAAW,MAAM,UACjB,kBAAkB,MAAM,iBACxB,WAAW,MAAM,UACjB,eAAe,MAAM,cACrB,UAAU,MAAM;AACpB,MAAI,aAAa,QAAQ,GACrB,IAAI,eAAe,SAAS,IAAI,YAChC,aAAa,QAAQ,GACrB,IAAI,eAAe,SAAS,IAAI;AAEpC,MAAI,QAAQ,OAAO,iBAAiB,aAAa,aAAa;AAAA,IAC5D;AAAA,IACA;AAAA,EACF,CAAC,IAAI;AAAA,IACH;AAAA,IACA;AAAA,EACF;AAEA,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,MAAM;AAEV,MAAI,UAAU;AACZ,QAAI,eAAe,gBAAgBA,OAAM;AACzC,QAAI,aAAa;AACjB,QAAI,YAAY;AAEhB,QAAI,iBAAiB,UAAUA,OAAM,GAAG;AACtC,qBAAe,mBAAmBA,OAAM;AAExC,UAAI,iBAAiB,YAAY,EAAE,aAAa,YAAY,aAAa,YAAY;AACnF,qBAAa;AACb,oBAAY;AAAA,MACd;AAAA,IACF;AAGA,mBAAe;AAEf,QAAI,cAAc,QAAQ,cAAc,QAAQ,cAAc,UAAU,cAAc,KAAK;AACzF,cAAQ;AACR,UAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACzF,aAAa,UAAU;AAAA;AACvB,WAAK,UAAU,WAAW;AAC1B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAEA,QAAI,cAAc,SAAS,cAAc,OAAO,cAAc,WAAW,cAAc,KAAK;AAC1F,cAAQ;AACR,UAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACzF,aAAa,SAAS;AAAA;AACtB,WAAK,UAAU,WAAW;AAC1B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,eAAe,OAAO,OAAO;AAAA,IAC/B;AAAA,EACF,GAAG,YAAY,UAAU;AAEzB,MAAI,QAAQ,iBAAiB,OAAO,kBAAkB;AAAA,IACpD;AAAA,IACA;AAAA,EACF,GAAG,UAAUA,OAAM,CAAC,IAAI;AAAA,IACtB;AAAA,IACA;AAAA,EACF;AAEA,MAAI,MAAM;AACV,MAAI,MAAM;AAEV,MAAI,iBAAiB;AACnB,QAAI;AAEJ,WAAO,OAAO,OAAO,CAAC,GAAG,eAAe,iBAAiB,CAAC,GAAG,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,aAAa,IAAI,oBAAoB,MAAM,IAAI,eAAe,IAAI,SAAS,IAAI,QAAQ,iBAAiB,IAAI,SAAS,IAAI,UAAU,eAAe;AAAA,EAClT;AAEA,SAAO,OAAO,OAAO,CAAC,GAAG,eAAe,kBAAkB,CAAC,GAAG,gBAAgB,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,gBAAgB,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,gBAAgB,YAAY,IAAI,gBAAgB;AAC9M;AAEA,SAAS,cAAc,OAAO;AAC5B,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,wBAAwB,QAAQ,iBAChC,kBAAkB,0BAA0B,SAAS,OAAO,uBAC5D,oBAAoB,QAAQ,UAC5B,WAAW,sBAAsB,SAAS,OAAO,mBACjD,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,OAAO;AAC7D,MAAI,eAAe;AAAA,IACjB,WAAW,iBAAiB,MAAM,SAAS;AAAA,IAC3C,WAAW,aAAa,MAAM,SAAS;AAAA,IACvC,QAAQ,MAAM,SAAS;AAAA,IACvB,YAAY,MAAM,MAAM;AAAA,IACxB;AAAA,IACA,SAAS,MAAM,QAAQ,aAAa;AAAA,EACtC;AAEA,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,OAAO,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACvG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU,MAAM,QAAQ;AAAA,MACxB;AAAA,MACA;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,MAAI,MAAM,cAAc,SAAS,MAAM;AACrC,UAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,OAAO,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACrG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU;AAAA,MACV,UAAU;AAAA,MACV;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,yBAAyB,MAAM;AAAA,EACjC,CAAC;AACH;AAGA,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;;;ACtKA,IAAI,UAAU;AAAA,EACZ,SAAS;AACX;AAEA,SAASC,QAAO,MAAM;AACpB,MAAI,QAAQ,KAAK,OACb,WAAW,KAAK,UAChB,UAAU,KAAK;AACnB,MAAI,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO;AACjD,MAAIC,UAAS,UAAU,MAAM,SAAS,MAAM;AAC5C,MAAI,gBAAgB,CAAC,EAAE,OAAO,MAAM,cAAc,WAAW,MAAM,cAAc,MAAM;AAEvF,MAAI,QAAQ;AACV,kBAAc,QAAQ,SAAU,cAAc;AAC5C,mBAAa,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAClE,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ;AACV,IAAAA,QAAO,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,EAC5D;AAEA,SAAO,WAAY;AACjB,QAAI,QAAQ;AACV,oBAAc,QAAQ,SAAU,cAAc;AAC5C,qBAAa,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,MACrE,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ;AACV,MAAAA,QAAO,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAC/D;AAAA,EACF;AACF;AAGA,IAAO,yBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI,SAAS,KAAK;AAAA,EAAC;AAAA,EACnB,QAAQD;AAAA,EACR,MAAM,CAAC;AACT;;;AChDA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACe,SAAR,qBAAsC,WAAW;AACtD,SAAO,UAAU,QAAQ,0BAA0B,SAAU,SAAS;AACpE,WAAO,KAAK,OAAO;AAAA,EACrB,CAAC;AACH;;;ACVA,IAAIE,QAAO;AAAA,EACT,OAAO;AAAA,EACP,KAAK;AACP;AACe,SAAR,8BAA+C,WAAW;AAC/D,SAAO,UAAU,QAAQ,cAAc,SAAU,SAAS;AACxD,WAAOA,MAAK,OAAO;AAAA,EACrB,CAAC;AACH;;;ACPe,SAAR,gBAAiC,MAAM;AAC5C,MAAI,MAAM,UAAU,IAAI;AACxB,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,IAAI;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;;;ACNe,SAAR,oBAAqC,SAAS;AAQnD,SAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO,gBAAgB,OAAO,EAAE;AAC5F;;;ACRe,SAAR,gBAAiC,SAAS,UAAU;AACzD,MAAI,MAAM,UAAU,OAAO;AAC3B,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,iBAAiB,IAAI;AACzB,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,QAAI,iBAAiB,iBAAiB;AAEtC,QAAI,kBAAkB,CAAC,kBAAkB,aAAa,SAAS;AAC7D,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG,IAAI,oBAAoB,OAAO;AAAA,IAClC;AAAA,EACF;AACF;;;ACvBe,SAAR,gBAAiC,SAAS;AAC/C,MAAI;AAEJ,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,YAAY,gBAAgB,OAAO;AACvC,MAAI,QAAQ,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACpG,MAAI,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,OAAO,KAAK,cAAc,GAAG,OAAO,KAAK,cAAc,CAAC;AAC5G,MAAI,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,OAAO,KAAK,eAAe,GAAG,OAAO,KAAK,eAAe,CAAC;AACjH,MAAI,IAAI,CAAC,UAAU,aAAa,oBAAoB,OAAO;AAC3D,MAAI,IAAI,CAAC,UAAU;AAEnB,MAAI,iBAAiB,QAAQ,IAAI,EAAE,cAAc,OAAO;AACtD,SAAK,IAAI,KAAK,aAAa,OAAO,KAAK,cAAc,CAAC,IAAI;AAAA,EAC5D;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC3Be,SAAR,eAAgC,SAAS;AAE9C,MAAI,oBAAoB,iBAAiB,OAAO,GAC5C,WAAW,kBAAkB,UAC7B,YAAY,kBAAkB,WAC9B,YAAY,kBAAkB;AAElC,SAAO,6BAA6B,KAAK,WAAW,YAAY,SAAS;AAC3E;;;ACLe,SAAR,gBAAiC,MAAM;AAC5C,MAAI,CAAC,QAAQ,QAAQ,WAAW,EAAE,QAAQ,YAAY,IAAI,CAAC,KAAK,GAAG;AAEjE,WAAO,KAAK,cAAc;AAAA,EAC5B;AAEA,MAAI,cAAc,IAAI,KAAK,eAAe,IAAI,GAAG;AAC/C,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,cAAc,IAAI,CAAC;AAC5C;;;ACJe,SAAR,kBAAmC,SAAS,MAAM;AACvD,MAAI;AAEJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,eAAe,gBAAgB,OAAO;AAC1C,MAAI,SAAS,mBAAmB,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACxH,MAAI,MAAM,UAAU,YAAY;AAChC,MAAI,SAAS,SAAS,CAAC,GAAG,EAAE,OAAO,IAAI,kBAAkB,CAAC,GAAG,eAAe,YAAY,IAAI,eAAe,CAAC,CAAC,IAAI;AACjH,MAAI,cAAc,KAAK,OAAO,MAAM;AACpC,SAAO,SAAS;AAAA;AAAA,IAChB,YAAY,OAAO,kBAAkB,cAAc,MAAM,CAAC,CAAC;AAAA;AAC7D;;;ACzBe,SAAR,iBAAkC,MAAM;AAC7C,SAAO,OAAO,OAAO,CAAC,GAAG,MAAM;AAAA,IAC7B,MAAM,KAAK;AAAA,IACX,KAAK,KAAK;AAAA,IACV,OAAO,KAAK,IAAI,KAAK;AAAA,IACrB,QAAQ,KAAK,IAAI,KAAK;AAAA,EACxB,CAAC;AACH;;;ACQA,SAAS,2BAA2B,SAAS,UAAU;AACrD,MAAI,OAAO,sBAAsB,SAAS,OAAO,aAAa,OAAO;AACrE,OAAK,MAAM,KAAK,MAAM,QAAQ;AAC9B,OAAK,OAAO,KAAK,OAAO,QAAQ;AAChC,OAAK,SAAS,KAAK,MAAM,QAAQ;AACjC,OAAK,QAAQ,KAAK,OAAO,QAAQ;AACjC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,IAAI,KAAK;AACd,OAAK,IAAI,KAAK;AACd,SAAO;AACT;AAEA,SAAS,2BAA2B,SAAS,gBAAgB,UAAU;AACrE,SAAO,mBAAmB,WAAW,iBAAiB,gBAAgB,SAAS,QAAQ,CAAC,IAAI,UAAU,cAAc,IAAI,2BAA2B,gBAAgB,QAAQ,IAAI,iBAAiB,gBAAgB,mBAAmB,OAAO,CAAC,CAAC;AAC9O;AAKA,SAAS,mBAAmB,SAAS;AACnC,MAAIC,mBAAkB,kBAAkB,cAAc,OAAO,CAAC;AAC9D,MAAI,oBAAoB,CAAC,YAAY,OAAO,EAAE,QAAQ,iBAAiB,OAAO,EAAE,QAAQ,KAAK;AAC7F,MAAI,iBAAiB,qBAAqB,cAAc,OAAO,IAAI,gBAAgB,OAAO,IAAI;AAE9F,MAAI,CAAC,UAAU,cAAc,GAAG;AAC9B,WAAO,CAAC;AAAA,EACV;AAGA,SAAOA,iBAAgB,OAAO,SAAU,gBAAgB;AACtD,WAAO,UAAU,cAAc,KAAK,SAAS,gBAAgB,cAAc,KAAK,YAAY,cAAc,MAAM;AAAA,EAClH,CAAC;AACH;AAIe,SAAR,gBAAiC,SAAS,UAAU,cAAc,UAAU;AACjF,MAAI,sBAAsB,aAAa,oBAAoB,mBAAmB,OAAO,IAAI,CAAC,EAAE,OAAO,QAAQ;AAC3G,MAAIA,mBAAkB,CAAC,EAAE,OAAO,qBAAqB,CAAC,YAAY,CAAC;AACnE,MAAI,sBAAsBA,iBAAgB,CAAC;AAC3C,MAAI,eAAeA,iBAAgB,OAAO,SAAU,SAAS,gBAAgB;AAC3E,QAAI,OAAO,2BAA2B,SAAS,gBAAgB,QAAQ;AACvE,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,2BAA2B,SAAS,qBAAqB,QAAQ,CAAC;AACrE,eAAa,QAAQ,aAAa,QAAQ,aAAa;AACvD,eAAa,SAAS,aAAa,SAAS,aAAa;AACzD,eAAa,IAAI,aAAa;AAC9B,eAAa,IAAI,aAAa;AAC9B,SAAO;AACT;;;ACjEe,SAAR,eAAgC,MAAM;AAC3C,MAAIC,aAAY,KAAK,WACjB,UAAU,KAAK,SACf,YAAY,KAAK;AACrB,MAAI,gBAAgB,YAAY,iBAAiB,SAAS,IAAI;AAC9D,MAAI,YAAY,YAAY,aAAa,SAAS,IAAI;AACtD,MAAI,UAAUA,WAAU,IAAIA,WAAU,QAAQ,IAAI,QAAQ,QAAQ;AAClE,MAAI,UAAUA,WAAU,IAAIA,WAAU,SAAS,IAAI,QAAQ,SAAS;AACpE,MAAI;AAEJ,UAAQ,eAAe;AAAA,IACrB,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAI,QAAQ;AAAA,MAC3B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAIA,WAAU;AAAA,MAC7B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAIA,WAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAI,QAAQ;AAAA,QACzB,GAAG;AAAA,MACL;AACA;AAAA,IAEF;AACE,gBAAU;AAAA,QACR,GAAGA,WAAU;AAAA,QACb,GAAGA,WAAU;AAAA,MACf;AAAA,EACJ;AAEA,MAAI,WAAW,gBAAgB,yBAAyB,aAAa,IAAI;AAEzE,MAAI,YAAY,MAAM;AACpB,QAAI,MAAM,aAAa,MAAM,WAAW;AAExC,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC3De,SAAR,eAAgC,OAAO,SAAS;AACrD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,qBAAqB,SAAS,WAC9B,YAAY,uBAAuB,SAAS,MAAM,YAAY,oBAC9D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,MAAM,WAAW,mBAC3D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,kBAAkB,mBAC5D,wBAAwB,SAAS,cACjC,eAAe,0BAA0B,SAAS,WAAW,uBAC7D,wBAAwB,SAAS,gBACjC,iBAAiB,0BAA0B,SAAS,SAAS,uBAC7D,uBAAuB,SAAS,aAChC,cAAc,yBAAyB,SAAS,QAAQ,sBACxD,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,IAAI;AAChD,MAAI,gBAAgB,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AACvH,MAAI,aAAa,mBAAmB,SAAS,YAAY;AACzD,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,UAAU,MAAM,SAAS,cAAc,aAAa,cAAc;AACtE,MAAI,qBAAqB,gBAAgB,UAAU,OAAO,IAAI,UAAU,QAAQ,kBAAkB,mBAAmB,MAAM,SAAS,MAAM,GAAG,UAAU,cAAc,QAAQ;AAC7K,MAAI,sBAAsB,sBAAsB,MAAM,SAAS,SAAS;AACxE,MAAIC,iBAAgB,eAAe;AAAA,IACjC,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,MAAI,mBAAmB,iBAAiB,OAAO,OAAO,CAAC,GAAG,YAAYA,cAAa,CAAC;AACpF,MAAI,oBAAoB,mBAAmB,SAAS,mBAAmB;AAGvE,MAAI,kBAAkB;AAAA,IACpB,KAAK,mBAAmB,MAAM,kBAAkB,MAAM,cAAc;AAAA,IACpE,QAAQ,kBAAkB,SAAS,mBAAmB,SAAS,cAAc;AAAA,IAC7E,MAAM,mBAAmB,OAAO,kBAAkB,OAAO,cAAc;AAAA,IACvE,OAAO,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc;AAAA,EAC5E;AACA,MAAI,aAAa,MAAM,cAAc;AAErC,MAAI,mBAAmB,UAAU,YAAY;AAC3C,QAAIC,UAAS,WAAW,SAAS;AACjC,WAAO,KAAK,eAAe,EAAE,QAAQ,SAAU,KAAK;AAClD,UAAI,WAAW,CAAC,OAAO,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,IAAI;AACvD,UAAI,OAAO,CAAC,KAAK,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,MAAM;AACnD,sBAAgB,GAAG,KAAKA,QAAO,IAAI,IAAI;AAAA,IACzC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AC5De,SAAR,qBAAsC,OAAO,SAAS;AAC3D,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,YAAY,SAAS,WACrB,WAAW,SAAS,UACpB,eAAe,SAAS,cACxB,UAAU,SAAS,SACnB,iBAAiB,SAAS,gBAC1B,wBAAwB,SAAS,uBACjC,wBAAwB,0BAA0B,SAAS,aAAgB;AAC/E,MAAI,YAAY,aAAa,SAAS;AACtC,MAAIC,cAAa,YAAY,iBAAiB,sBAAsB,oBAAoB,OAAO,SAAUC,YAAW;AAClH,WAAO,aAAaA,UAAS,MAAM;AAAA,EACrC,CAAC,IAAI;AACL,MAAI,oBAAoBD,YAAW,OAAO,SAAUC,YAAW;AAC7D,WAAO,sBAAsB,QAAQA,UAAS,KAAK;AAAA,EACrD,CAAC;AAED,MAAI,kBAAkB,WAAW,GAAG;AAClC,wBAAoBD;AAAA,EACtB;AAGA,MAAI,YAAY,kBAAkB,OAAO,SAAU,KAAKC,YAAW;AACjE,QAAIA,UAAS,IAAI,eAAe,OAAO;AAAA,MACrC,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,iBAAiBA,UAAS,CAAC;AAC9B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,OAAO,KAAK,SAAS,EAAE,KAAK,SAAU,GAAG,GAAG;AACjD,WAAO,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,EACnC,CAAC;AACH;;;AClCA,SAAS,8BAA8B,WAAW;AAChD,MAAI,iBAAiB,SAAS,MAAM,MAAM;AACxC,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,oBAAoB,qBAAqB,SAAS;AACtD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAEhB,MAAI,MAAM,cAAc,IAAI,EAAE,OAAO;AACnC;AAAA,EACF;AAEA,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,OAAO,kBACpD,8BAA8B,QAAQ,oBACtC,UAAU,QAAQ,SAClB,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,wBAAwB,QAAQ,gBAChC,iBAAiB,0BAA0B,SAAS,OAAO,uBAC3D,wBAAwB,QAAQ;AACpC,MAAI,qBAAqB,MAAM,QAAQ;AACvC,MAAI,gBAAgB,iBAAiB,kBAAkB;AACvD,MAAI,kBAAkB,kBAAkB;AACxC,MAAI,qBAAqB,gCAAgC,mBAAmB,CAAC,iBAAiB,CAAC,qBAAqB,kBAAkB,CAAC,IAAI,8BAA8B,kBAAkB;AAC3L,MAAIC,cAAa,CAAC,kBAAkB,EAAE,OAAO,kBAAkB,EAAE,OAAO,SAAU,KAAKC,YAAW;AAChG,WAAO,IAAI,OAAO,iBAAiBA,UAAS,MAAM,OAAO,qBAAqB,OAAO;AAAA,MACnF,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,IAAIA,UAAS;AAAA,EAChB,GAAG,CAAC,CAAC;AACL,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,YAAY,oBAAI,IAAI;AACxB,MAAI,qBAAqB;AACzB,MAAI,wBAAwBD,YAAW,CAAC;AAExC,WAAS,IAAI,GAAG,IAAIA,YAAW,QAAQ,KAAK;AAC1C,QAAI,YAAYA,YAAW,CAAC;AAE5B,QAAI,iBAAiB,iBAAiB,SAAS;AAE/C,QAAI,mBAAmB,aAAa,SAAS,MAAM;AACnD,QAAI,aAAa,CAAC,KAAK,MAAM,EAAE,QAAQ,cAAc,KAAK;AAC1D,QAAI,MAAM,aAAa,UAAU;AACjC,QAAI,WAAW,eAAe,OAAO;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,oBAAoB,aAAa,mBAAmB,QAAQ,OAAO,mBAAmB,SAAS;AAEnG,QAAI,cAAc,GAAG,IAAI,WAAW,GAAG,GAAG;AACxC,0BAAoB,qBAAqB,iBAAiB;AAAA,IAC5D;AAEA,QAAI,mBAAmB,qBAAqB,iBAAiB;AAC7D,QAAI,SAAS,CAAC;AAEd,QAAI,eAAe;AACjB,aAAO,KAAK,SAAS,cAAc,KAAK,CAAC;AAAA,IAC3C;AAEA,QAAI,cAAc;AAChB,aAAO,KAAK,SAAS,iBAAiB,KAAK,GAAG,SAAS,gBAAgB,KAAK,CAAC;AAAA,IAC/E;AAEA,QAAI,OAAO,MAAM,SAAU,OAAO;AAChC,aAAO;AAAA,IACT,CAAC,GAAG;AACF,8BAAwB;AACxB,2BAAqB;AACrB;AAAA,IACF;AAEA,cAAU,IAAI,WAAW,MAAM;AAAA,EACjC;AAEA,MAAI,oBAAoB;AAEtB,QAAI,iBAAiB,iBAAiB,IAAI;AAE1C,QAAI,QAAQ,SAASE,OAAMC,KAAI;AAC7B,UAAI,mBAAmBH,YAAW,KAAK,SAAUC,YAAW;AAC1D,YAAIG,UAAS,UAAU,IAAIH,UAAS;AAEpC,YAAIG,SAAQ;AACV,iBAAOA,QAAO,MAAM,GAAGD,GAAE,EAAE,MAAM,SAAU,OAAO;AAChD,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,kBAAkB;AACpB,gCAAwB;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,KAAK,gBAAgB,KAAK,GAAG,MAAM;AAC1C,UAAI,OAAO,MAAM,EAAE;AAEnB,UAAI,SAAS,QAAS;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,MAAM,cAAc,uBAAuB;AAC7C,UAAM,cAAc,IAAI,EAAE,QAAQ;AAClC,UAAM,YAAY;AAClB,UAAM,QAAQ;AAAA,EAChB;AACF;AAGA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAAA,EAC3B,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AACF;;;AC/IA,SAAS,eAAe,UAAU,MAAM,kBAAkB;AACxD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,MACjB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAEA,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK,SAAS,iBAAiB;AAAA,IACnD,OAAO,SAAS,QAAQ,KAAK,QAAQ,iBAAiB;AAAA,IACtD,QAAQ,SAAS,SAAS,KAAK,SAAS,iBAAiB;AAAA,IACzD,MAAM,SAAS,OAAO,KAAK,QAAQ,iBAAiB;AAAA,EACtD;AACF;AAEA,SAAS,sBAAsB,UAAU;AACvC,SAAO,CAAC,KAAK,OAAO,QAAQ,IAAI,EAAE,KAAK,SAAU,MAAM;AACrD,WAAO,SAAS,IAAI,KAAK;AAAA,EAC3B,CAAC;AACH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAChB,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,mBAAmB,MAAM,cAAc;AAC3C,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,gBAAgB;AAAA,EAClB,CAAC;AACD,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,aAAa;AAAA,EACf,CAAC;AACD,MAAI,2BAA2B,eAAe,mBAAmB,aAAa;AAC9E,MAAI,sBAAsB,eAAe,mBAAmB,YAAY,gBAAgB;AACxF,MAAI,oBAAoB,sBAAsB,wBAAwB;AACtE,MAAI,mBAAmB,sBAAsB,mBAAmB;AAChE,QAAM,cAAc,IAAI,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,gCAAgC;AAAA,IAChC,uBAAuB;AAAA,EACzB,CAAC;AACH;AAGA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,kBAAkB,CAAC,iBAAiB;AAAA,EACpC,IAAI;AACN;;;ACzDO,SAAS,wBAAwB,WAAW,OAAOE,SAAQ;AAChE,MAAI,gBAAgB,iBAAiB,SAAS;AAC9C,MAAI,iBAAiB,CAAC,MAAM,GAAG,EAAE,QAAQ,aAAa,KAAK,IAAI,KAAK;AAEpE,MAAI,OAAO,OAAOA,YAAW,aAAaA,QAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,IACxE;AAAA,EACF,CAAC,CAAC,IAAIA,SACF,WAAW,KAAK,CAAC,GACjB,WAAW,KAAK,CAAC;AAErB,aAAW,YAAY;AACvB,cAAY,YAAY,KAAK;AAC7B,SAAO,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK,IAAI;AAAA,IACjD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM,SAChB,OAAO,MAAM;AACjB,MAAI,kBAAkB,QAAQ,QAC1BA,UAAS,oBAAoB,SAAS,CAAC,GAAG,CAAC,IAAI;AACnD,MAAI,OAAO,WAAW,OAAO,SAAU,KAAK,WAAW;AACrD,QAAI,SAAS,IAAI,wBAAwB,WAAW,MAAM,OAAOA,OAAM;AACvE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,wBAAwB,KAAK,MAAM,SAAS,GAC5C,IAAI,sBAAsB,GAC1B,IAAI,sBAAsB;AAE9B,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,cAAc,cAAc,KAAK;AACvC,UAAM,cAAc,cAAc,KAAK;AAAA,EACzC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAO,iBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU,CAAC,eAAe;AAAA,EAC1B,IAAI;AACN;;;ACnDA,SAAS,cAAc,MAAM;AAC3B,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAKhB,QAAM,cAAc,IAAI,IAAI,eAAe;AAAA,IACzC,WAAW,MAAM,MAAM;AAAA,IACvB,SAAS,MAAM,MAAM;AAAA,IACrB,UAAU;AAAA,IACV,WAAW,MAAM;AAAA,EACnB,CAAC;AACH;AAGA,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;;;ACxBe,SAAR,WAA4B,MAAM;AACvC,SAAO,SAAS,MAAM,MAAM;AAC9B;;;ACUA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAChB,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,QAAQ,kBACrD,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,UAAU,QAAQ,SAClB,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,IAAI;AAC1D,MAAI,WAAW,eAAe,OAAO;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,YAAY,aAAa,MAAM,SAAS;AAC5C,MAAI,kBAAkB,CAAC;AACvB,MAAI,WAAW,yBAAyB,aAAa;AACrD,MAAI,UAAU,WAAW,QAAQ;AACjC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,oBAAoB,OAAO,iBAAiB,aAAa,aAAa,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IACvG,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,MAAI,8BAA8B,OAAO,sBAAsB,WAAW;AAAA,IACxE,UAAU;AAAA,IACV,SAAS;AAAA,EACX,IAAI,OAAO,OAAO;AAAA,IAChB,UAAU;AAAA,IACV,SAAS;AAAA,EACX,GAAG,iBAAiB;AACpB,MAAI,sBAAsB,MAAM,cAAc,SAAS,MAAM,cAAc,OAAO,MAAM,SAAS,IAAI;AACrG,MAAI,OAAO;AAAA,IACT,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,CAACA,gBAAe;AAClB;AAAA,EACF;AAEA,MAAI,eAAe;AACjB,QAAI;AAEJ,QAAI,WAAW,aAAa,MAAM,MAAM;AACxC,QAAI,UAAU,aAAa,MAAM,SAAS;AAC1C,QAAI,MAAM,aAAa,MAAM,WAAW;AACxC,QAAIC,UAASD,eAAc,QAAQ;AACnC,QAAIE,OAAMD,UAAS,SAAS,QAAQ;AACpC,QAAIE,OAAMF,UAAS,SAAS,OAAO;AACnC,QAAI,WAAW,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI;AAC/C,QAAI,SAAS,cAAc,QAAQ,cAAc,GAAG,IAAI,WAAW,GAAG;AACtE,QAAI,SAAS,cAAc,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,GAAG;AAGxE,QAAI,eAAe,MAAM,SAAS;AAClC,QAAI,YAAY,UAAU,eAAe,cAAc,YAAY,IAAI;AAAA,MACrE,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,QAAI,qBAAqB,MAAM,cAAc,kBAAkB,IAAI,MAAM,cAAc,kBAAkB,EAAE,UAAU,mBAAmB;AACxI,QAAI,kBAAkB,mBAAmB,QAAQ;AACjD,QAAI,kBAAkB,mBAAmB,OAAO;AAMhD,QAAI,WAAW,OAAO,GAAG,cAAc,GAAG,GAAG,UAAU,GAAG,CAAC;AAC3D,QAAI,YAAY,kBAAkB,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC5M,QAAI,YAAY,kBAAkB,CAAC,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC7M,QAAI,oBAAoB,MAAM,SAAS,SAAS,gBAAgB,MAAM,SAAS,KAAK;AACpF,QAAI,eAAe,oBAAoB,aAAa,MAAM,kBAAkB,aAAa,IAAI,kBAAkB,cAAc,IAAI;AACjI,QAAI,uBAAuB,wBAAwB,uBAAuB,OAAO,SAAS,oBAAoB,QAAQ,MAAM,OAAO,wBAAwB;AAC3J,QAAI,YAAYA,UAAS,YAAY,sBAAsB;AAC3D,QAAI,YAAYA,UAAS,YAAY;AACrC,QAAI,kBAAkB,OAAO,SAAS,IAAQC,MAAK,SAAS,IAAIA,MAAKD,SAAQ,SAAS,IAAQE,MAAK,SAAS,IAAIA,IAAG;AACnH,IAAAH,eAAc,QAAQ,IAAI;AAC1B,SAAK,QAAQ,IAAI,kBAAkBC;AAAA,EACrC;AAEA,MAAI,cAAc;AAChB,QAAI;AAEJ,QAAI,YAAY,aAAa,MAAM,MAAM;AAEzC,QAAI,WAAW,aAAa,MAAM,SAAS;AAE3C,QAAI,UAAUD,eAAc,OAAO;AAEnC,QAAI,OAAO,YAAY,MAAM,WAAW;AAExC,QAAI,OAAO,UAAU,SAAS,SAAS;AAEvC,QAAI,OAAO,UAAU,SAAS,QAAQ;AAEtC,QAAI,eAAe,CAAC,KAAK,IAAI,EAAE,QAAQ,aAAa,MAAM;AAE1D,QAAI,wBAAwB,yBAAyB,uBAAuB,OAAO,SAAS,oBAAoB,OAAO,MAAM,OAAO,yBAAyB;AAE7J,QAAI,aAAa,eAAe,OAAO,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B;AAE7I,QAAI,aAAa,eAAe,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B,UAAU;AAEhJ,QAAI,mBAAmB,UAAU,eAAe,eAAe,YAAY,SAAS,UAAU,IAAI,OAAO,SAAS,aAAa,MAAM,SAAS,SAAS,aAAa,IAAI;AAExK,IAAAA,eAAc,OAAO,IAAI;AACzB,SAAK,OAAO,IAAI,mBAAmB;AAAA,EACrC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAO,0BAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAC7B;;;AC7Ie,SAAR,qBAAsC,SAAS;AACpD,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;;;ACDe,SAAR,cAA+B,MAAM;AAC1C,MAAI,SAAS,UAAU,IAAI,KAAK,CAAC,cAAc,IAAI,GAAG;AACpD,WAAO,gBAAgB,IAAI;AAAA,EAC7B,OAAO;AACL,WAAO,qBAAqB,IAAI;AAAA,EAClC;AACF;;;ACDA,SAAS,gBAAgB,SAAS;AAChC,MAAI,OAAO,QAAQ,sBAAsB;AACzC,MAAI,SAAS,MAAM,KAAK,KAAK,IAAI,QAAQ,eAAe;AACxD,MAAI,SAAS,MAAM,KAAK,MAAM,IAAI,QAAQ,gBAAgB;AAC1D,SAAO,WAAW,KAAK,WAAW;AACpC;AAIe,SAAR,iBAAkC,yBAAyB,cAAc,SAAS;AACvF,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,MAAI,0BAA0B,cAAc,YAAY;AACxD,MAAI,uBAAuB,cAAc,YAAY,KAAK,gBAAgB,YAAY;AACtF,MAAI,kBAAkB,mBAAmB,YAAY;AACrD,MAAI,OAAO,sBAAsB,yBAAyB,sBAAsB,OAAO;AACvF,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM;AAAA,IAClC,eAAe,eAAe,GAAG;AAC/B,eAAS,cAAc,YAAY;AAAA,IACrC;AAEA,QAAI,cAAc,YAAY,GAAG;AAC/B,gBAAU,sBAAsB,cAAc,IAAI;AAClD,cAAQ,KAAK,aAAa;AAC1B,cAAQ,KAAK,aAAa;AAAA,IAC5B,WAAW,iBAAiB;AAC1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG,KAAK,OAAO,OAAO,aAAa,QAAQ;AAAA,IAC3C,GAAG,KAAK,MAAM,OAAO,YAAY,QAAQ;AAAA,IACzC,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;;;ACvDA,SAAS,MAAM,WAAW;AACxB,MAAI,MAAM,oBAAI,IAAI;AAClB,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,SAAS,CAAC;AACd,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,IAAI,SAAS,MAAM,QAAQ;AAAA,EACjC,CAAC;AAED,WAAS,KAAK,UAAU;AACtB,YAAQ,IAAI,SAAS,IAAI;AACzB,QAAI,WAAW,CAAC,EAAE,OAAO,SAAS,YAAY,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC;AACjF,aAAS,QAAQ,SAAU,KAAK;AAC9B,UAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACrB,YAAI,cAAc,IAAI,IAAI,GAAG;AAE7B,YAAI,aAAa;AACf,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,KAAK,QAAQ;AAAA,EACtB;AAEA,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,GAAG;AAE/B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEe,SAAR,eAAgC,WAAW;AAEhD,MAAI,mBAAmB,MAAM,SAAS;AAEtC,SAAO,eAAe,OAAO,SAAU,KAAK,OAAO;AACjD,WAAO,IAAI,OAAO,iBAAiB,OAAO,SAAU,UAAU;AAC5D,aAAO,SAAS,UAAU;AAAA,IAC5B,CAAC,CAAC;AAAA,EACJ,GAAG,CAAC,CAAC;AACP;;;AC3Ce,SAAR,SAA0BI,KAAI;AACnC,MAAI;AACJ,SAAO,WAAY;AACjB,QAAI,CAAC,SAAS;AACZ,gBAAU,IAAI,QAAQ,SAAU,SAAS;AACvC,gBAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,oBAAU;AACV,kBAAQA,IAAG,CAAC;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AACF;;;ACde,SAAR,YAA6B,WAAW;AAC7C,MAAI,SAAS,UAAU,OAAO,SAAUC,SAAQ,SAAS;AACvD,QAAI,WAAWA,QAAO,QAAQ,IAAI;AAClC,IAAAA,QAAO,QAAQ,IAAI,IAAI,WAAW,OAAO,OAAO,CAAC,GAAG,UAAU,SAAS;AAAA,MACrE,SAAS,OAAO,OAAO,CAAC,GAAG,SAAS,SAAS,QAAQ,OAAO;AAAA,MAC5D,MAAM,OAAO,OAAO,CAAC,GAAG,SAAS,MAAM,QAAQ,IAAI;AAAA,IACrD,CAAC,IAAI;AACL,WAAOA;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO,OAAO,KAAK,MAAM,EAAE,IAAI,SAAU,KAAK;AAC5C,WAAO,OAAO,GAAG;AAAA,EACnB,CAAC;AACH;;;ACJA,IAAI,kBAAkB;AAAA,EACpB,WAAW;AAAA,EACX,WAAW,CAAC;AAAA,EACZ,UAAU;AACZ;AAEA,SAAS,mBAAmB;AAC1B,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AAEA,SAAO,CAAC,KAAK,KAAK,SAAU,SAAS;AACnC,WAAO,EAAE,WAAW,OAAO,QAAQ,0BAA0B;AAAA,EAC/D,CAAC;AACH;AAEO,SAAS,gBAAgB,kBAAkB;AAChD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB,CAAC;AAAA,EACtB;AAEA,MAAI,oBAAoB,kBACpB,wBAAwB,kBAAkB,kBAC1CC,oBAAmB,0BAA0B,SAAS,CAAC,IAAI,uBAC3D,yBAAyB,kBAAkB,gBAC3C,iBAAiB,2BAA2B,SAAS,kBAAkB;AAC3E,SAAO,SAASC,cAAaC,YAAWC,SAAQ,SAAS;AACvD,QAAI,YAAY,QAAQ;AACtB,gBAAU;AAAA,IACZ;AAEA,QAAI,QAAQ;AAAA,MACV,WAAW;AAAA,MACX,kBAAkB,CAAC;AAAA,MACnB,SAAS,OAAO,OAAO,CAAC,GAAG,iBAAiB,cAAc;AAAA,MAC1D,eAAe,CAAC;AAAA,MAChB,UAAU;AAAA,QACR,WAAWD;AAAA,QACX,QAAQC;AAAA,MACV;AAAA,MACA,YAAY,CAAC;AAAA,MACb,QAAQ,CAAC;AAAA,IACX;AACA,QAAI,mBAAmB,CAAC;AACxB,QAAI,cAAc;AAClB,QAAI,WAAW;AAAA,MACb;AAAA,MACA,YAAY,SAAS,WAAW,kBAAkB;AAChD,YAAIC,WAAU,OAAO,qBAAqB,aAAa,iBAAiB,MAAM,OAAO,IAAI;AACzF,+BAAuB;AACvB,cAAM,UAAU,OAAO,OAAO,CAAC,GAAG,gBAAgB,MAAM,SAASA,QAAO;AACxE,cAAM,gBAAgB;AAAA,UACpB,WAAW,UAAUF,UAAS,IAAI,kBAAkBA,UAAS,IAAIA,WAAU,iBAAiB,kBAAkBA,WAAU,cAAc,IAAI,CAAC;AAAA,UAC3I,QAAQ,kBAAkBC,OAAM;AAAA,QAClC;AAGA,YAAI,mBAAmB,eAAe,YAAY,CAAC,EAAE,OAAOH,mBAAkB,MAAM,QAAQ,SAAS,CAAC,CAAC;AAEvG,cAAM,mBAAmB,iBAAiB,OAAO,SAAU,GAAG;AAC5D,iBAAO,EAAE;AAAA,QACX,CAAC;AACD,2BAAmB;AACnB,eAAO,SAAS,OAAO;AAAA,MACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,aAAa,SAAS,cAAc;AAClC,YAAI,aAAa;AACf;AAAA,QACF;AAEA,YAAI,kBAAkB,MAAM,UACxBE,aAAY,gBAAgB,WAC5BC,UAAS,gBAAgB;AAG7B,YAAI,CAAC,iBAAiBD,YAAWC,OAAM,GAAG;AACxC;AAAA,QACF;AAGA,cAAM,QAAQ;AAAA,UACZ,WAAW,iBAAiBD,YAAW,gBAAgBC,OAAM,GAAG,MAAM,QAAQ,aAAa,OAAO;AAAA,UAClG,QAAQ,cAAcA,OAAM;AAAA,QAC9B;AAMA,cAAM,QAAQ;AACd,cAAM,YAAY,MAAM,QAAQ;AAKhC,cAAM,iBAAiB,QAAQ,SAAU,UAAU;AACjD,iBAAO,MAAM,cAAc,SAAS,IAAI,IAAI,OAAO,OAAO,CAAC,GAAG,SAAS,IAAI;AAAA,QAC7E,CAAC;AAED,iBAASE,SAAQ,GAAGA,SAAQ,MAAM,iBAAiB,QAAQA,UAAS;AAClE,cAAI,MAAM,UAAU,MAAM;AACxB,kBAAM,QAAQ;AACd,YAAAA,SAAQ;AACR;AAAA,UACF;AAEA,cAAI,wBAAwB,MAAM,iBAAiBA,MAAK,GACpDC,MAAK,sBAAsB,IAC3B,yBAAyB,sBAAsB,SAC/C,WAAW,2BAA2B,SAAS,CAAC,IAAI,wBACpD,OAAO,sBAAsB;AAEjC,cAAI,OAAOA,QAAO,YAAY;AAC5B,oBAAQA,IAAG;AAAA,cACT;AAAA,cACA,SAAS;AAAA,cACT;AAAA,cACA;AAAA,YACF,CAAC,KAAK;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA,MAGA,QAAQ,SAAS,WAAY;AAC3B,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,mBAAS,YAAY;AACrB,kBAAQ,KAAK;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAAA,MACD,SAAS,SAAS,UAAU;AAC1B,+BAAuB;AACvB,sBAAc;AAAA,MAChB;AAAA,IACF;AAEA,QAAI,CAAC,iBAAiBJ,YAAWC,OAAM,GAAG;AACxC,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,OAAO,EAAE,KAAK,SAAUI,QAAO;AACjD,UAAI,CAAC,eAAe,QAAQ,eAAe;AACzC,gBAAQ,cAAcA,MAAK;AAAA,MAC7B;AAAA,IACF,CAAC;AAMD,aAAS,qBAAqB;AAC5B,YAAM,iBAAiB,QAAQ,SAAU,MAAM;AAC7C,YAAI,OAAO,KAAK,MACZ,eAAe,KAAK,SACpBH,WAAU,iBAAiB,SAAS,CAAC,IAAI,cACzCI,UAAS,KAAK;AAElB,YAAI,OAAOA,YAAW,YAAY;AAChC,cAAI,YAAYA,QAAO;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAASJ;AAAA,UACX,CAAC;AAED,cAAI,SAAS,SAASK,UAAS;AAAA,UAAC;AAEhC,2BAAiB,KAAK,aAAa,MAAM;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,yBAAyB;AAChC,uBAAiB,QAAQ,SAAUH,KAAI;AACrC,eAAOA,IAAG;AAAA,MACZ,CAAC;AACD,yBAAmB,CAAC;AAAA,IACtB;AAEA,WAAO;AAAA,EACT;AACF;AACO,IAAI,eAA4B,gBAAgB;;;AC/LvD,IAAI,mBAAmB,CAAC,wBAAgB,uBAAe,uBAAe,mBAAW;AACjF,IAAII,gBAA4B,gBAAgB;AAAA,EAC9C;AACF,CAAC;;;ACED,IAAIC,oBAAmB,CAAC,wBAAgB,uBAAe,uBAAe,qBAAa,gBAAQ,cAAM,yBAAiB,eAAO,YAAI;AAC7H,IAAIC,gBAA4B,gBAAgB;AAAA,EAC9C,kBAAkBD;AACpB,CAAC;;;ACVM,IAAME,YAAS;AACf,IAAMC,gBAAa;AACnB,IAAMC,iBAAc;AACpB,IAAMC,cAAW;AACjB,IAAMC,kBAAe;AAErB,IAAMC,gBAAgB;EAACC,SAAS;EAAMC,SAAS;AAAzB;AAEtB,IAAMC,0BAA0B,SAA1BA,2BAA0B;AAAA,SAAMC,SAASC;AAAf;ACThC,SAASC,eACdC,KACAC,KACS;AACT,SAAO,CAAA,EAAGF,eAAeG,KAAKF,KAAKC,GAA5B;AACR;AAEM,SAASE,wBACdC,OACAC,QACAC,cACG;AACH,MAAIC,MAAMC,QAAQJ,KAAd,GAAsB;AACxB,QAAMK,IAAIL,MAAMC,MAAD;AACf,WAAOI,KAAK,OACRF,MAAMC,QAAQF,YAAd,IACEA,aAAaD,MAAD,IACZC,eACFG;EACL;AAED,SAAOL;AACR;AAEM,SAASM,OAAON,OAAYO,MAAuB;AACxD,MAAMC,MAAM,CAAA,EAAGC,SAASX,KAAKE,KAAjB;AACZ,SAAOQ,IAAIE,QAAQ,SAAZ,MAA2B,KAAKF,IAAIE,QAAWH,OAAf,GAAA,IAA0B;AAClE;AAEM,SAASI,uBAAuBX,OAAYY,MAAkB;AACnE,SAAO,OAAOZ,UAAU,aAAaA,MAAK,MAAL,QAASY,IAAT,IAAiBZ;AACvD;AAEM,SAASa,UACdC,KACAC,IACkB;AAElB,MAAIA,OAAO,GAAG;AACZ,WAAOD;EACR;AAED,MAAIE;AAEJ,SAAO,SAACC,KAAc;AACpBC,iBAAaF,OAAD;AACZA,cAAUG,WAAW,WAAM;AACzBL,MAAAA,IAAGG,GAAD;IACH,GAAEF,EAFiB;EAGrB;AACF;AAEM,SAASK,iBAAoBxB,KAAQyB,MAA4B;AACtE,MAAMC,QAAK,OAAA,OAAA,CAAA,GAAO1B,GAAP;AACXyB,OAAKE,QAAQ,SAAC1B,KAAQ;AACpB,WAAQyB,MAAczB,GAAf;EACR,CAFD;AAGA,SAAOyB;AACR;AAEM,SAASE,cAAcxB,OAAyB;AACrD,SAAOA,MAAMyB,MAAM,KAAZ,EAAmBC,OAAOC,OAA1B;AACR;AAEM,SAASC,iBAAoB5B,OAAqB;AACvD,SAAQ,CAAA,EAAW6B,OAAO7B,KAAnB;AACR;AAEM,SAAS8B,aAAgBC,KAAU/B,OAAgB;AACxD,MAAI+B,IAAIrB,QAAQV,KAAZ,MAAuB,IAAI;AAC7B+B,QAAIC,KAAKhC,KAAT;EACD;AACF;AAMM,SAASiC,OAAUF,KAAe;AACvC,SAAOA,IAAIL,OAAO,SAACQ,MAAMjC,QAAP;AAAA,WAAiB8B,IAAIrB,QAAQwB,IAAZ,MAAsBjC;EAAvC,CAAX;AACR;AAMM,SAASkC,kBAAiBC,WAAqC;AACpE,SAAOA,UAAUX,MAAM,GAAhB,EAAqB,CAArB;AACR;AAEM,SAASY,UAAUrC,OAA8B;AACtD,SAAO,CAAA,EAAGsC,MAAMxC,KAAKE,KAAd;AACR;AAEM,SAASuC,qBACd3C,KACkC;AAClC,SAAO4C,OAAOnB,KAAKzB,GAAZ,EAAiB6C,OAAO,SAACC,KAAK7C,KAAQ;AAC3C,QAAID,IAAIC,GAAD,MAAU8C,QAAW;AACzBD,UAAY7C,GAAb,IAAoBD,IAAIC,GAAD;IACxB;AAED,WAAO6C;EACR,GAAE,CAAA,CANI;AAOR;ACtGM,SAASE,MAAsB;AACpC,SAAOnD,SAASoD,cAAc,KAAvB;AACR;AAEM,SAASC,WAAU9C,OAAqD;AAC7E,SAAO,CAAC,WAAW,UAAZ,EAAwB+C,KAAK,SAACxC,MAAD;AAAA,WAAUD,OAAON,OAAOO,IAAR;EAAhB,CAA7B;AACR;AAEM,SAASyC,WAAWhD,OAAmC;AAC5D,SAAOM,OAAON,OAAO,UAAR;AACd;AAEM,SAASiD,aAAajD,OAAqC;AAChE,SAAOM,OAAON,OAAO,YAAR;AACd;AAEM,SAASkD,mBAAmBlD,OAAuC;AACxE,SAAO,CAAC,EAAEA,SAASA,MAAMmD,UAAUnD,MAAMmD,OAAOC,cAAcpD;AAC/D;AAEM,SAASqD,mBAAmBrD,OAA2B;AAC5D,MAAI8C,WAAU9C,KAAD,GAAS;AACpB,WAAO,CAACA,KAAD;EACR;AAED,MAAIgD,WAAWhD,KAAD,GAAS;AACrB,WAAOqC,UAAUrC,KAAD;EACjB;AAED,MAAIG,MAAMC,QAAQJ,KAAd,GAAsB;AACxB,WAAOA;EACR;AAED,SAAOqC,UAAU5C,SAAS6D,iBAAiBtD,KAA1B,CAAD;AACjB;AAEM,SAASuD,sBACdC,KACAxD,OACM;AACNwD,MAAIjC,QAAQ,SAACkC,IAAO;AAClB,QAAIA,IAAI;AACNA,SAAGC,MAAMC,qBAAwB3D,QAAjC;IACD;EACF,CAJD;AAKD;AAEM,SAAS4D,mBACdJ,KACAK,OACM;AACNL,MAAIjC,QAAQ,SAACkC,IAAO;AAClB,QAAIA,IAAI;AACNA,SAAGK,aAAa,cAAcD,KAA9B;IACD;EACF,CAJD;AAKD;AAEM,SAASE,iBACdC,mBACU;AAAA,MAAA;AACV,MAAA,oBAAkBpC,iBAAiBoC,iBAAD,GAA3BC,UAAP,kBAAA,CAAA;AAGA,SAAOA,WAAO,SAAP,wBAAAA,QAASC,kBAAT,QAAA,sBAAwBxE,OAAOuE,QAAQC,gBAAgBzE;AAC/D;AAEM,SAAS0E,iCACdC,gBACAC,OACS;AACT,MAAOC,UAAoBD,MAApBC,SAASC,UAAWF,MAAXE;AAEhB,SAAOH,eAAeI,MAAM,SAAA,MAAsC;AAAA,QAApCC,aAAoC,KAApCA,YAAYC,cAAwB,KAAxBA,aAAaC,QAAW,KAAXA;AACrD,QAAOC,oBAAqBD,MAArBC;AACP,QAAMC,gBAAgB1C,kBAAiBuC,YAAYtC,SAAb;AACtC,QAAM0C,aAAaJ,YAAYK,cAAcC;AAE7C,QAAI,CAACF,YAAY;AACf,aAAO;IACR;AAED,QAAMG,cAAcJ,kBAAkB,WAAWC,WAAWI,IAAKC,IAAI;AACrE,QAAMC,iBAAiBP,kBAAkB,QAAQC,WAAWO,OAAQF,IAAI;AACxE,QAAMG,eAAeT,kBAAkB,UAAUC,WAAWS,KAAMC,IAAI;AACtE,QAAMC,gBAAgBZ,kBAAkB,SAASC,WAAWY,MAAOF,IAAI;AAEvE,QAAMG,aACJlB,WAAWS,MAAMX,UAAUU,cAAcL;AAC3C,QAAMgB,gBACJrB,UAAUE,WAAWY,SAASD,iBAAiBR;AACjD,QAAMiB,cACJpB,WAAWc,OAAOjB,UAAUgB,eAAeV;AAC7C,QAAMkB,eACJxB,UAAUG,WAAWiB,QAAQD,gBAAgBb;AAE/C,WAAOe,cAAcC,iBAAiBC,eAAeC;EACtD,CAxBM;AAyBR;AAEM,SAASC,4BACdC,KACAC,QACAC,UACM;AACN,MAAMC,SAAYF,SAAN;AAMZ,GAAC,iBAAiB,qBAAlB,EAAyC1E,QAAQ,SAAC8C,OAAU;AAC1D2B,QAAIG,MAAD,EAAS9B,OAAO6B,QAAnB;EACD,CAFD;AAGD;AAMM,SAASE,eAAeC,QAAiBC,OAAyB;AACvE,MAAIC,SAASD;AACb,SAAOC,QAAQ;AAAA,QAAA;AACb,QAAIF,OAAOG,SAASD,MAAhB,GAAyB;AAC3B,aAAO;IACR;AACDA,aAAUA,OAAOE,eAAX,OAAA,UAAA,sBAAIF,OAAOE,YAAP,MAAJ,OAAA,SAAG,oBAAiCC;EAC3C;AACD,SAAO;AACR;AClIM,IAAMC,eAAe;EAACC,SAAS;AAAV;AAC5B,IAAIC,oBAAoB;AAQjB,SAASC,uBAA6B;AAC3C,MAAIH,aAAaC,SAAS;AACxB;EACD;AAEDD,eAAaC,UAAU;AAEvB,MAAIG,OAAOC,aAAa;AACtBvH,aAASwH,iBAAiB,aAAaC,mBAAvC;EACD;AACF;AAOM,SAASA,sBAA4B;AAC1C,MAAMC,MAAMH,YAAYG,IAAZ;AAEZ,MAAIA,MAAMN,oBAAoB,IAAI;AAChCF,iBAAaC,UAAU;AAEvBnH,aAAS2H,oBAAoB,aAAaF,mBAA1C;EACD;AAEDL,sBAAoBM;AACrB;AAQM,SAASE,eAAqB;AACnC,MAAMC,gBAAgB7H,SAAS6H;AAE/B,MAAIpE,mBAAmBoE,aAAD,GAAiB;AACrC,QAAMC,WAAWD,cAAcnE;AAE/B,QAAImE,cAAcE,QAAQ,CAACD,SAAS1D,MAAM4D,WAAW;AACnDH,oBAAcE,KAAd;IACD;EACF;AACF;AAEc,SAASE,2BAAiC;AACvDjI,WAASwH,iBAAiB,cAAcH,sBAAsBzH,aAA9D;AACA0H,SAAOE,iBAAiB,QAAQI,YAAhC;AACD;AC9DM,IAAMM,YACX,OAAOZ,WAAW,eAAe,OAAOtH,aAAa;AAEhD,IAAMmI,SAASD;;EAElB,CAAC,CAACZ,OAAOc;IACT;ACJG,SAASC,wBAAwB3B,QAAwB;AAC9D,MAAM4B,MAAM5B,WAAW,YAAY,eAAe;AAElD,SAAO,CACFA,SADE,uBACyB4B,MADzB,2CAEL,oCAFK,EAGLC,KAAK,GAHA;AAIR;AAEM,SAASC,MAAMjI,OAAuB;AAC3C,MAAMkI,gBAAgB;AACtB,MAAMC,sBAAsB;AAE5B,SAAOnI,MACJoI,QAAQF,eAAe,GADnB,EAEJE,QAAQD,qBAAqB,EAFzB,EAGJE,KAHI;AAIR;AAED,SAASC,cAAcC,SAAyB;AAC9C,SAAON,MAAK,2BAGRA,MAAMM,OAAD,IAHG,uFAAA;AAOb;AAEM,SAASC,oBAAoBD,SAA2B;AAC7D,SAAO;IACLD,cAAcC,OAAD;;IAEb;;IAEA;;IAEA;EAPK;AASR;AAGD,IAAIE;AACJ,IAAA,MAAa;AACXC,uBAAoB;AACrB;AAEM,SAASA,uBAA6B;AAC3CD,oBAAkB,oBAAIE,IAAJ;AACnB;AAEM,SAASC,SAASC,WAAoBN,SAAuB;AAClE,MAAIM,aAAa,CAACJ,gBAAgBK,IAAIP,OAApB,GAA8B;AAAA,QAAA;AAC9CE,oBAAgBM,IAAIR,OAApB;AACA,KAAA,WAAAS,SAAQC,KAAR,MAAA,UAAgBT,oBAAoBD,OAAD,CAAnC;EACD;AACF;AAEM,SAASW,UAAUL,WAAoBN,SAAuB;AACnE,MAAIM,aAAa,CAACJ,gBAAgBK,IAAIP,OAApB,GAA8B;AAAA,QAAA;AAC9CE,oBAAgBM,IAAIR,OAApB;AACA,KAAA,YAAAS,SAAQG,MAAR,MAAA,WAAiBX,oBAAoBD,OAAD,CAApC;EACD;AACF;AAEM,SAASa,gBAAgBC,SAAwB;AACtD,MAAMC,oBAAoB,CAACD;AAC3B,MAAME,qBACJ/G,OAAOgH,UAAU/I,SAASX,KAAKuJ,OAA/B,MAA4C,qBAC5C,CAAEA,QAAgBpC;AAEpBiC,YACEI,mBACA,CACE,sBACA,MAAMG,OAAOJ,OAAD,IAAY,KACxB,sEACA,yBAJF,EAKErB,KAAK,GALP,CAFO;AAUTkB,YACEK,oBACA,CACE,2EACA,oEAFF,EAGEvB,KAAK,GAHP,CAFO;AAOV;ACjFD,IAAM0B,cAAc;EAClBC,aAAa;EACbC,cAAc;EACdC,mBAAmB;EACnBC,QAAQ;AAJU;AAOpB,IAAMC,cAAc;EAClBC,WAAW;EACXC,WAAW;EACXC,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,UAAU;EACVC,MAAM;EACNC,OAAO;EACPC,QAAQ;AATU;AAYb,IAAMC,eAA0B,OAAA,OAAA;EACrCC,UAAUlL;EACVmL,MAAM;IACJR,SAAS;IACTS,UAAU;EAFN;EAINC,OAAO;EACPC,UAAU,CAAC,KAAK,GAAN;EACVC,wBAAwB;EACxBC,aAAa;EACbC,kBAAkB;EAClBC,aAAa;EACbtG,mBAAmB;EACnBuG,qBAAqB;EACrBC,gBAAgB;EAChBpG,QAAQ,CAAC,GAAG,EAAJ;EACRqG,eAhBqC,SAAA,gBAgBrB;EAAA;EAChBC,gBAjBqC,SAAA,iBAiBpB;EAAA;EACjBC,UAlBqC,SAAA,WAkB1B;EAAA;EACXC,WAnBqC,SAAA,YAmBzB;EAAA;EACZC,UApBqC,SAAA,WAoB1B;EAAA;EACXC,QArBqC,SAAA,SAqB5B;EAAA;EACTC,SAtBqC,SAAA,UAsB3B;EAAA;EACVC,QAvBqC,SAAA,SAuB5B;EAAA;EACTC,SAxBqC,SAAA,UAwB3B;EAAA;EACVC,WAzBqC,SAAA,YAyBzB;EAAA;EACZC,aA1BqC,SAAA,cA0BvB;EAAA;EACdC,gBA3BqC,SAAA,iBA2BpB;EAAA;EACjB5J,WAAW;EACX6J,SAAS,CAAA;EACTC,eAAe,CAAA;EACfC,QAAQ;EACRC,cAAc;EACdC,OAAO;EACPC,SAAS;EACTC,eAAe;AAnCsB,GAoClC7C,aACAK,WArCkC;AAwCvC,IAAMyC,cAAchK,OAAOnB,KAAKoJ,YAAZ;AAEb,IAAMgC,kBAA4C,SAA5CA,iBAA6CC,cAAiB;AAEzE,MAAA,MAAa;AACXC,kBAAcD,cAAc,CAAA,CAAf;EACd;AAED,MAAMrL,OAAOmB,OAAOnB,KAAKqL,YAAZ;AACbrL,OAAKE,QAAQ,SAAC1B,KAAQ;AACnB4K,iBAAqB5K,GAAtB,IAA6B6M,aAAa7M,GAAD;EAC1C,CAFD;AAGD;AAEM,SAAS+M,uBACdC,aACgB;AAChB,MAAMZ,UAAUY,YAAYZ,WAAW,CAAA;AACvC,MAAMvC,eAAcuC,QAAQxJ,OAAgC,SAACC,KAAKoK,QAAW;AAC3E,QAAOC,OAAsBD,OAAtBC,MAAM7M,eAAgB4M,OAAhB5M;AAEb,QAAI6M,MAAM;AAAA,UAAA;AACRrK,UAAIqK,IAAD,IACDF,YAAYE,IAAD,MAAWpK,SAClBkK,YAAYE,IAAD,KADf,QAEKtC,aAAqBsC,IAAtB,MAFJ,OAAA,QAEmC7M;IACtC;AAED,WAAOwC;EACR,GAAE,CAAA,CAXiB;AAapB,SAAA,OAAA,OAAA,CAAA,GACKmK,aACAnD,YAFL;AAID;AAEM,SAASsD,sBACd5J,YACA6I,SACyB;AACzB,MAAMgB,WAAWhB,UACbzJ,OAAOnB,KAAKuL,uBAAsB,OAAA,OAAA,CAAA,GAAKnC,cAAL;IAAmBwB;EAAnB,CAAA,CAAA,CAAlC,IACAO;AAEJ,MAAM7H,QAAQsI,SAASxK,OACrB,SAACC,KAA+C7C,KAAQ;AACtD,QAAMqN,iBACJ9J,WAAU+J,aAAV,gBAAqCtN,GAArC,KAA+C,IAC/CwI,KAFoB;AAItB,QAAI,CAAC6E,eAAe;AAClB,aAAOxK;IACR;AAED,QAAI7C,QAAQ,WAAW;AACrB6C,UAAI7C,GAAD,IAAQqN;IACZ,OAAM;AACL,UAAI;AACFxK,YAAI7C,GAAD,IAAQuN,KAAKC,MAAMH,aAAX;MACZ,SAAQI,GAAG;AACV5K,YAAI7C,GAAD,IAAQqN;MACZ;IACF;AAED,WAAOxK;EACR,GACD,CAAA,CAtBY;AAyBd,SAAOiC;AACR;AAEM,SAAS4I,cACdnK,YACAuB,OACO;AACP,MAAM6I,MAAG,OAAA,OAAA,CAAA,GACJ7I,OADI;IAEPwF,SAASxJ,uBAAuBgE,MAAMwF,SAAS,CAAC/G,UAAD,CAAhB;EAFxB,GAGHuB,MAAMsG,mBACN,CAAA,IACA+B,sBAAsB5J,YAAWuB,MAAMsH,OAAlB,CALlB;AAQTuB,MAAI7C,OAAJ,OAAA,OAAA,CAAA,GACKF,aAAaE,MACb6C,IAAI7C,IAFT;AAKA6C,MAAI7C,OAAO;IACTC,UACE4C,IAAI7C,KAAKC,aAAa,SAASjG,MAAMuG,cAAcsC,IAAI7C,KAAKC;IAC9DT,SACEqD,IAAI7C,KAAKR,YAAY,SACjBxF,MAAMuG,cACJ,OACA,gBACFsC,IAAI7C,KAAKR;EARN;AAWX,SAAOqD;AACR;AAEM,SAASb,cACdD,cACAT,SACM;AAAA,MAFNS,iBAEM,QAAA;AAFNA,mBAA+B,CAAA;EAEzB;AAAA,MADNT,YACM,QAAA;AADNA,cAAoB,CAAA;EACd;AACN,MAAM5K,OAAOmB,OAAOnB,KAAKqL,YAAZ;AACbrL,OAAKE,QAAQ,SAACkM,MAAS;AACrB,QAAMC,iBAAiBtM,iBACrBqJ,cACAjI,OAAOnB,KAAKqI,WAAZ,CAFqC;AAKvC,QAAIiE,qBAAqB,CAAChO,eAAe+N,gBAAgBD,IAAjB;AAGxC,QAAIE,oBAAoB;AACtBA,2BACE1B,QAAQvK,OAAO,SAACoL,QAAD;AAAA,eAAYA,OAAOC,SAASU;MAA5B,CAAf,EAAiDG,WAAW;IAC/D;AAEDhF,aACE+E,oBACA,CAAA,MACOF,OADP,KAEE,wEACA,6DACA,QACA,gEACA,wDANF,EAOEzF,KAAK,GAPP,CAFM;EAWT,CAzBD;AA0BD;AC9LD,IAAM6F,YAAY,SAAZA,aAAY;AAAA,SAAmB;AAAnB;AAElB,SAASC,wBAAwB7J,SAAkB8J,MAAoB;AACrE9J,UAAQ4J,UAAS,CAAV,IAAgBE;AACxB;AAED,SAASC,mBAAmBhO,OAAuC;AACjE,MAAMkK,SAAQtH,IAAG;AAEjB,MAAI5C,UAAU,MAAM;AAClBkK,IAAAA,OAAM+D,YAAY9O;EACnB,OAAM;AACL+K,IAAAA,OAAM+D,YAAY7O;AAElB,QAAI0D,WAAU9C,KAAD,GAAS;AACpBkK,MAAAA,OAAMgE,YAAYlO,KAAlB;IACD,OAAM;AACL8N,8BAAwB5D,QAAOlK,KAAR;IACxB;EACF;AAED,SAAOkK;AACR;AAEM,SAASiE,WAAWhE,SAAyBxF,OAAoB;AACtE,MAAI7B,WAAU6B,MAAMwF,OAAP,GAAiB;AAC5B2D,4BAAwB3D,SAAS,EAAV;AACvBA,YAAQ+D,YAAYvJ,MAAMwF,OAA1B;EACD,WAAU,OAAOxF,MAAMwF,YAAY,YAAY;AAC9C,QAAIxF,MAAMqF,WAAW;AACnB8D,8BAAwB3D,SAASxF,MAAMwF,OAAhB;IACxB,OAAM;AACLA,cAAQiE,cAAczJ,MAAMwF;IAC7B;EACF;AACF;AAEM,SAASkE,YAAYC,SAAuC;AACjE,MAAMtI,MAAMsI,QAAOC;AACnB,MAAMC,cAAcnM,UAAU2D,IAAIyI,QAAL;AAE7B,SAAO;IACLzI;IACAmE,SAASqE,YAAYE,KAAK,SAACC,MAAD;AAAA,aAAUA,KAAKC,UAAUpI,SAASvH,aAAxB;IAAV,CAAjB;IACTiL,OAAOsE,YAAYE,KACjB,SAACC,MAAD;AAAA,aACEA,KAAKC,UAAUpI,SAASrH,WAAxB,KACAwP,KAAKC,UAAUpI,SAASpH,eAAxB;IAFF,CADK;IAKPyP,UAAUL,YAAYE,KAAK,SAACC,MAAD;AAAA,aACzBA,KAAKC,UAAUpI,SAAStH,cAAxB;IADyB,CAAjB;EARL;AAYR;AAEM,SAASiN,OACd5E,UAIA;AACA,MAAM+G,UAAS1L,IAAG;AAElB,MAAMoD,MAAMpD,IAAG;AACfoD,MAAIiI,YAAYjP;AAChBgH,MAAIlC,aAAa,cAAc,QAA/B;AACAkC,MAAIlC,aAAa,YAAY,IAA7B;AAEA,MAAMqG,UAAUvH,IAAG;AACnBuH,UAAQ8D,YAAYhP;AACpBkL,UAAQrG,aAAa,cAAc,QAAnC;AAEAqK,aAAWhE,SAAS5C,SAAS5C,KAAnB;AAEV2J,EAAAA,QAAOJ,YAAYlI,GAAnB;AACAA,MAAIkI,YAAY/D,OAAhB;AAEA2E,WAASvH,SAAS5C,OAAO4C,SAAS5C,KAA1B;AAER,WAASmK,SAASC,WAAkBC,WAAwB;AAC1D,QAAA,eAA8BX,YAAYC,OAAD,GAAlCtI,OAAP,aAAOA,KAAKmE,WAAZ,aAAYA,SAASD,SAArB,aAAqBA;AAErB,QAAI8E,UAAUzE,OAAO;AACnBvE,MAAAA,KAAIlC,aAAa,cAAckL,UAAUzE,KAAzC;IACD,OAAM;AACLvE,MAAAA,KAAIiJ,gBAAgB,YAApB;IACD;AAED,QAAI,OAAOD,UAAU/E,cAAc,UAAU;AAC3CjE,MAAAA,KAAIlC,aAAa,kBAAkBkL,UAAU/E,SAA7C;IACD,OAAM;AACLjE,MAAAA,KAAIiJ,gBAAgB,gBAApB;IACD;AAED,QAAID,UAAU5E,SAAS;AACrBpE,MAAAA,KAAIlC,aAAa,gBAAgB,EAAjC;IACD,OAAM;AACLkC,MAAAA,KAAIiJ,gBAAgB,cAApB;IACD;AAEDjJ,IAAAA,KAAItC,MAAM2G,WACR,OAAO2E,UAAU3E,aAAa,WACvB2E,UAAU3E,WADjB,OAEI2E,UAAU3E;AAEhB,QAAI2E,UAAU1E,MAAM;AAClBtE,MAAAA,KAAIlC,aAAa,QAAQkL,UAAU1E,IAAnC;IACD,OAAM;AACLtE,MAAAA,KAAIiJ,gBAAgB,MAApB;IACD;AAED,QACEF,UAAU5E,YAAY6E,UAAU7E,WAChC4E,UAAU/E,cAAcgF,UAAUhF,WAClC;AACAmE,iBAAWhE,UAAS5C,SAAS5C,KAAnB;IACX;AAED,QAAIqK,UAAU9E,OAAO;AACnB,UAAI,CAACA,QAAO;AACVlE,QAAAA,KAAIkI,YAAYF,mBAAmBgB,UAAU9E,KAAX,CAAlC;MACD,WAAU6E,UAAU7E,UAAU8E,UAAU9E,OAAO;AAC9ClE,QAAAA,KAAIkJ,YAAYhF,MAAhB;AACAlE,QAAAA,KAAIkI,YAAYF,mBAAmBgB,UAAU9E,KAAX,CAAlC;MACD;IACF,WAAUA,QAAO;AAChBlE,MAAAA,KAAIkJ,YAAYhF,MAAhB;IACD;EACF;AAED,SAAO;IACLoE,QAAAA;IACAQ;EAFK;AAIR;AAID3C,OAAOgD,UAAU;ACjHjB,IAAIC,YAAY;AAChB,IAAIC,qBAAsD,CAAA;AAGnD,IAAIC,mBAA+B,CAAA;AAE3B,SAASC,YACtBnM,YACAyJ,aACU;AACV,MAAMlI,QAAQ4I,cAAcnK,YAAD,OAAA,OAAA,CAAA,GACtBqH,cACAmC,uBAAuBrK,qBAAqBsK,WAAD,CAArB,CAFA,CAAA;AAQ3B,MAAI2C;AACJ,MAAIC;AACJ,MAAIC;AACJ,MAAIC,qBAAqB;AACzB,MAAIC,gCAAgC;AACpC,MAAIC,eAAe;AACnB,MAAIC,sBAAsB;AAC1B,MAAIC;AACJ,MAAIC;AACJ,MAAIC;AACJ,MAAIC,YAA8B,CAAA;AAClC,MAAIC,uBAAuBtP,UAASuP,aAAazL,MAAMwG,mBAApB;AACnC,MAAIkF;AAKJ,MAAMC,KAAKlB;AACX,MAAMmB,iBAAiB;AACvB,MAAMtE,UAAUhK,OAAO0C,MAAMsH,OAAP;AAEtB,MAAMpI,QAAQ;;IAEZ2M,WAAW;;IAEX/I,WAAW;;IAEXgJ,aAAa;;IAEbC,WAAW;;IAEXC,SAAS;EAVG;AAad,MAAMpJ,WAAqB;;IAEzB+I;IACAlN,WAAAA;IACAkL,QAAQ1L,IAAG;IACX2N;IACA5L;IACAd;IACAoI;;IAEA2E;IACAC;IACA1C,YAAAA;IACA2C;IACAC,MAAAA;IACAC;IACAC;IACAC;IACAC;IACAC;EAnByB;AAyB3B,MAAI,CAACzM,MAAMwH,QAAQ;AACjB,QAAA,MAAa;AACXjD,gBAAU,MAAM,0CAAP;IACV;AAED,WAAO3B;EACR;AAKD,MAAA,gBAA2B5C,MAAMwH,OAAO5E,QAAb,GAApB+G,UAAP,cAAOA,QAAQQ,WAAf,cAAeA;AAEfR,EAAAA,QAAOxK,aAAa,mBAAkC,EAAtD;AACAwK,EAAAA,QAAOgC,KAAP,WAAoC/I,SAAS+I;AAE7C/I,WAAS+G,SAASA;AAClBlL,EAAAA,WAAUD,SAASoE;AACnB+G,EAAAA,QAAOnL,SAASoE;AAEhB,MAAM8J,eAAepF,QAAQqF,IAAI,SAACxE,QAAD;AAAA,WAAYA,OAAOhM,GAAGyG,QAAV;EAAZ,CAAZ;AACrB,MAAMgK,kBAAkBnO,WAAUoO,aAAa,eAAvB;AAExBC,eAAY;AACZC,8BAA2B;AAC3BC,eAAY;AAEZC,aAAW,YAAY,CAACrK,QAAD,CAAb;AAEV,MAAI5C,MAAMyH,cAAc;AACtByF,iBAAY;EACb;AAIDvD,EAAAA,QAAOrH,iBAAiB,cAAc,WAAM;AAC1C,QAAIM,SAAS5C,MAAMuG,eAAe3D,SAAS1D,MAAM4D,WAAW;AAC1DF,eAASqJ,mBAAT;IACD;EACF,CAJD;AAMAtC,EAAAA,QAAOrH,iBAAiB,cAAc,WAAM;AAC1C,QACEM,SAAS5C,MAAMuG,eACf3D,SAAS5C,MAAM2H,QAAQ5L,QAAQ,YAA/B,KAAgD,GAChD;AACAoR,kBAAW,EAAG7K,iBAAiB,aAAakJ,oBAA5C;IACD;EACF,CAPD;AASA,SAAO5I;AAKP,WAASwK,6BAAyD;AAChE,QAAO1F,QAAS9E,SAAS5C,MAAlB0H;AACP,WAAOlM,MAAMC,QAAQiM,KAAd,IAAuBA,QAAQ,CAACA,OAAO,CAAR;EACvC;AAED,WAAS2F,2BAAoC;AAC3C,WAAOD,2BAA0B,EAAG,CAAH,MAAU;EAC5C;AAED,WAASE,uBAAgC;AAAA,QAAA;AAEvC,WAAO,CAAC,GAAA,wBAAC1K,SAAS5C,MAAMwH,WAAhB,QAAC,sBAAuBgD;EACjC;AAED,WAAS+C,mBAA4B;AACnC,WAAO7B,iBAAiBjN;EACzB;AAED,WAAS0O,cAAwB;AAC/B,QAAMzL,SAAS6L,iBAAgB,EAAGC;AAClC,WAAO9L,SAAStC,iBAAiBsC,MAAD,IAAW5G;EAC5C;AAED,WAAS2S,6BAA6C;AACpD,WAAO/D,YAAYC,OAAD;EACnB;AAED,WAAS+D,SAASC,QAAyB;AAIzC,QACG/K,SAAS1D,MAAM6M,aAAa,CAACnJ,SAAS1D,MAAM4D,aAC7Cd,aAAaC,WACZmJ,oBAAoBA,iBAAiBxP,SAAS,SAC/C;AACA,aAAO;IACR;AAED,WAAOR,wBACLwH,SAAS5C,MAAMkG,OACfyH,SAAS,IAAI,GACb7H,aAAaI,KAHe;EAK/B;AAED,WAAS8G,aAAaY,UAAwB;AAAA,QAAxBA,aAAwB,QAAA;AAAxBA,iBAAW;IAAa;AAC5CjE,IAAAA,QAAO5K,MAAM8O,gBACXjL,SAAS5C,MAAMuG,eAAe,CAACqH,WAAW,KAAK;AACjDjE,IAAAA,QAAO5K,MAAM8G,SAAb,KAAyBjD,SAAS5C,MAAM6F;EACzC;AAED,WAASoH,WACPa,MACA7R,MACA8R,uBACM;AAAA,QADNA,0BACM,QAAA;AADNA,8BAAwB;IAClB;AACNrB,iBAAa9P,QAAQ,SAACoR,aAAgB;AACpC,UAAIA,YAAYF,IAAD,GAAQ;AACrBE,oBAAYF,IAAD,EAAX,MAAAE,aAAsB/R,IAAX;MACZ;IACF,CAJD;AAMA,QAAI8R,uBAAuB;AAAA,UAAA;AACzB,OAAA,kBAAAnL,SAAS5C,OAAM8N,IAAf,EAAA,MAAA,iBAAwB7R,IAAxB;IACD;EACF;AAED,WAASgS,6BAAmC;AAC1C,QAAOjI,OAAQpD,SAAS5C,MAAjBgG;AAEP,QAAI,CAACA,KAAKR,SAAS;AACjB;IACD;AAED,QAAM0I,OAAI,UAAWlI,KAAKR;AAC1B,QAAMmG,MAAKhC,QAAOgC;AAClB,QAAMwC,QAAQlR,iBAAiB2F,SAAS5C,MAAM4H,iBAAiBnJ,UAAjC;AAE9B0P,UAAMvR,QAAQ,SAACoN,MAAS;AACtB,UAAMoE,eAAepE,KAAKxB,aAAa0F,IAAlB;AAErB,UAAItL,SAAS1D,MAAM4D,WAAW;AAC5BkH,aAAK7K,aAAa+O,MAAME,eAAkBA,eAAN,MAAsBzC,MAAOA,GAAjE;MACD,OAAM;AACL,YAAM0C,YAAYD,gBAAgBA,aAAa3K,QAAQkI,KAAI,EAAzB,EAA6BjI,KAA7B;AAElC,YAAI2K,WAAW;AACbrE,eAAK7K,aAAa+O,MAAMG,SAAxB;QACD,OAAM;AACLrE,eAAKM,gBAAgB4D,IAArB;QACD;MACF;IACF,CAdD;EAeD;AAED,WAASnB,8BAAoC;AAC3C,QAAIH,mBAAmB,CAAChK,SAAS5C,MAAMgG,KAAKC,UAAU;AACpD;IACD;AAED,QAAMkI,QAAQlR,iBAAiB2F,SAAS5C,MAAM4H,iBAAiBnJ,UAAjC;AAE9B0P,UAAMvR,QAAQ,SAACoN,MAAS;AACtB,UAAIpH,SAAS5C,MAAMuG,aAAa;AAC9ByD,aAAK7K,aACH,iBACAyD,SAAS1D,MAAM4D,aAAakH,SAASuD,iBAAgB,IACjD,SACA,OAJN;MAMD,OAAM;AACLvD,aAAKM,gBAAgB,eAArB;MACD;IACF,CAXD;EAYD;AAED,WAASgE,mCAAyC;AAChDnB,gBAAW,EAAG1K,oBAAoB,aAAa+I,oBAA/C;AACAd,yBAAqBA,mBAAmB3N,OACtC,SAACwE,UAAD;AAAA,aAAcA,aAAaiK;IAA3B,CADmB;EAGtB;AAED,WAAS+C,gBAAgB7O,OAAsC;AAE7D,QAAIsC,aAAaC,SAAS;AACxB,UAAIiJ,gBAAgBxL,MAAM9D,SAAS,aAAa;AAC9C;MACD;IACF;AAED,QAAM4S,eACH9O,MAAM+O,gBAAgB/O,MAAM+O,aAAN,EAAqB,CAArB,KAA4B/O,MAAMkC;AAG3D,QACEgB,SAAS5C,MAAMuG,eACf9E,eAAekI,SAAQ6E,YAAT,GACd;AACA;IACD;AAGD,QACEvR,iBAAiB2F,SAAS5C,MAAM4H,iBAAiBnJ,UAAjC,EAA4CL,KAAK,SAACU,IAAD;AAAA,aAC/D2C,eAAe3C,IAAI0P,YAAL;IADiD,CAAjE,GAGA;AACA,UAAIxM,aAAaC,SAAS;AACxB;MACD;AAED,UACEW,SAAS1D,MAAM4D,aACfF,SAAS5C,MAAM2H,QAAQ5L,QAAQ,OAA/B,KAA2C,GAC3C;AACA;MACD;IACF,OAAM;AACLkR,iBAAW,kBAAkB,CAACrK,UAAUlD,KAAX,CAAnB;IACX;AAED,QAAIkD,SAAS5C,MAAMqG,gBAAgB,MAAM;AACvCzD,eAASqJ,mBAAT;AACArJ,eAASwJ,KAAT;AAKAnB,sCAAgC;AAChCzO,iBAAW,WAAM;AACfyO,wCAAgC;MACjC,CAFS;AAOV,UAAI,CAACrI,SAAS1D,MAAM6M,WAAW;AAC7B2C,4BAAmB;MACpB;IACF;EACF;AAED,WAASC,cAAoB;AAC3BzD,mBAAe;EAChB;AAED,WAAS0D,eAAqB;AAC5B1D,mBAAe;EAChB;AAED,WAAS2D,mBAAyB;AAChC,QAAMC,MAAM3B,YAAW;AACvB2B,QAAIxM,iBAAiB,aAAaiM,iBAAiB,IAAnD;AACAO,QAAIxM,iBAAiB,YAAYiM,iBAAiB7T,aAAlD;AACAoU,QAAIxM,iBAAiB,cAAcsM,cAAclU,aAAjD;AACAoU,QAAIxM,iBAAiB,aAAaqM,aAAajU,aAA/C;EACD;AAED,WAASgU,sBAA4B;AACnC,QAAMI,MAAM3B,YAAW;AACvB2B,QAAIrM,oBAAoB,aAAa8L,iBAAiB,IAAtD;AACAO,QAAIrM,oBAAoB,YAAY8L,iBAAiB7T,aAArD;AACAoU,QAAIrM,oBAAoB,cAAcmM,cAAclU,aAApD;AACAoU,QAAIrM,oBAAoB,aAAakM,aAAajU,aAAlD;EACD;AAED,WAASqU,kBAAkB5I,UAAkB6I,UAA4B;AACvEC,oBAAgB9I,UAAU,WAAM;AAC9B,UACE,CAACvD,SAAS1D,MAAM4D,aAChB6G,QAAO6D,cACP7D,QAAO6D,WAAW3L,SAAS8H,OAA3B,GACA;AACAqF,iBAAQ;MACT;IACF,CARc;EAShB;AAED,WAASE,iBAAiB/I,UAAkB6I,UAA4B;AACtEC,oBAAgB9I,UAAU6I,QAAX;EAChB;AAED,WAASC,gBAAgB9I,UAAkB6I,UAA4B;AACrE,QAAM3N,MAAMoM,2BAA0B,EAAGpM;AAEzC,aAASE,SAAS7B,OAA8B;AAC9C,UAAIA,MAAMkC,WAAWP,KAAK;AACxBD,oCAA4BC,KAAK,UAAUE,QAAhB;AAC3ByN,iBAAQ;MACT;IACF;AAID,QAAI7I,aAAa,GAAG;AAClB,aAAO6I,SAAQ;IAChB;AAED5N,gCAA4BC,KAAK,UAAUgK,4BAAhB;AAC3BjK,gCAA4BC,KAAK,OAAOE,QAAb;AAE3B8J,mCAA+B9J;EAChC;AAED,WAAS4N,GACPC,WACAC,SACAC,SACM;AAAA,QADNA,YACM,QAAA;AADNA,gBAA6C;IACvC;AACN,QAAMnB,QAAQlR,iBAAiB2F,SAAS5C,MAAM4H,iBAAiBnJ,UAAjC;AAC9B0P,UAAMvR,QAAQ,SAACoN,MAAS;AACtBA,WAAK1H,iBAAiB8M,WAAWC,SAASC,OAA1C;AACA/D,gBAAUlO,KAAK;QAAC2M;QAAMoF;QAAWC;QAASC;MAA3B,CAAf;IACD,CAHD;EAID;AAED,WAASxC,eAAqB;AAC5B,QAAIO,yBAAwB,GAAI;AAC9B8B,SAAG,cAAchI,YAAW;QAACxM,SAAS;MAAV,CAA1B;AACFwU,SAAG,YAAYI,cAA+B;QAAC5U,SAAS;MAAV,CAA5C;IACH;AAEDkC,kBAAc+F,SAAS5C,MAAM2H,OAAhB,EAAyB/K,QAAQ,SAACwS,WAAc;AAC3D,UAAIA,cAAc,UAAU;AAC1B;MACD;AAEDD,SAAGC,WAAWjI,UAAZ;AAEF,cAAQiI,WAAR;QACE,KAAK;AACHD,aAAG,cAAcI,YAAf;AACF;QACF,KAAK;AACHJ,aAAGlM,SAAS,aAAa,QAAQuM,gBAA/B;AACF;QACF,KAAK;AACHL,aAAG,YAAYK,gBAAb;AACF;MATJ;IAWD,CAlBD;EAmBD;AAED,WAASC,kBAAwB;AAC/BlE,cAAU3O,QAAQ,SAAA,MAAyD;AAAA,UAAvDoN,OAAuD,KAAvDA,MAAMoF,YAAiD,KAAjDA,WAAWC,UAAsC,KAAtCA,SAASC,UAA6B,KAA7BA;AAC5CtF,WAAKvH,oBAAoB2M,WAAWC,SAASC,OAA7C;IACD,CAFD;AAGA/D,gBAAY,CAAA;EACb;AAED,WAASpE,WAAUzH,OAAoB;AAAA,QAAA;AACrC,QAAIgQ,0BAA0B;AAE9B,QACE,CAAC9M,SAAS1D,MAAM2M,aAChB8D,uBAAuBjQ,KAAD,KACtBuL,+BACA;AACA;IACD;AAED,QAAM2E,eAAa,oBAAAxE,qBAAgB,OAAhB,SAAA,kBAAkBxP,UAAS;AAE9CwP,uBAAmB1L;AACnBgM,oBAAgBhM,MAAMgM;AAEtBqB,gCAA2B;AAE3B,QAAI,CAACnK,SAAS1D,MAAM4D,aAAaxE,aAAaoB,KAAD,GAAS;AAKpDgL,yBAAmB9N,QAAQ,SAAC2E,UAAD;AAAA,eAAcA,SAAS7B,KAAD;MAAtB,CAA3B;IACD;AAGD,QACEA,MAAM9D,SAAS,YACdgH,SAAS5C,MAAM2H,QAAQ5L,QAAQ,YAA/B,IAA+C,KAC9CiP,uBACFpI,SAAS5C,MAAMqG,gBAAgB,SAC/BzD,SAAS1D,MAAM4D,WACf;AACA4M,gCAA0B;IAC3B,OAAM;AACLxC,mBAAaxN,KAAD;IACb;AAED,QAAIA,MAAM9D,SAAS,SAAS;AAC1BoP,2BAAqB,CAAC0E;IACvB;AAED,QAAIA,2BAA2B,CAACE,YAAY;AAC1CC,mBAAanQ,KAAD;IACb;EACF;AAED,WAAS+L,YAAY/L,OAAyB;AAC5C,QAAMkC,SAASlC,MAAMkC;AACrB,QAAMkO,gCACJvC,iBAAgB,EAAG1L,SAASD,MAA5B,KAAuC+H,QAAO9H,SAASD,MAAhB;AAEzC,QAAIlC,MAAM9D,SAAS,eAAekU,+BAA+B;AAC/D;IACD;AAED,QAAMrQ,iBAAiBsQ,oBAAmB,EACvC7S,OAAOyM,OADa,EAEpBgD,IAAI,SAAChD,SAAW;AAAA,UAAA;AACf,UAAM/G,YAAW+G,QAAOnL;AACxB,UAAMU,UAAK,wBAAG0D,UAASgJ,mBAAZ,OAAA,SAAG,sBAAyB1M;AAEvC,UAAIA,QAAO;AACT,eAAO;UACLY,YAAY6J,QAAOqG,sBAAP;UACZjQ,aAAab;UACbc;QAHK;MAKR;AAED,aAAO;IACR,CAfoB,EAgBpBjD,OAAOC,OAhBa;AAkBvB,QAAIwC,iCAAiCC,gBAAgBC,KAAjB,GAAyB;AAC3D4O,uCAAgC;AAChCuB,mBAAanQ,KAAD;IACb;EACF;AAED,WAAS6P,aAAa7P,OAAyB;AAC7C,QAAMuQ,aACJN,uBAAuBjQ,KAAD,KACrBkD,SAAS5C,MAAM2H,QAAQ5L,QAAQ,OAA/B,KAA2C,KAAKiP;AAEnD,QAAIiF,YAAY;AACd;IACD;AAED,QAAIrN,SAAS5C,MAAMuG,aAAa;AAC9B3D,eAASyJ,sBAAsB3M,KAA/B;AACA;IACD;AAEDmQ,iBAAanQ,KAAD;EACb;AAED,WAAS8P,iBAAiB9P,OAAyB;AACjD,QACEkD,SAAS5C,MAAM2H,QAAQ5L,QAAQ,SAA/B,IAA4C,KAC5C2D,MAAMkC,WAAW2L,iBAAgB,GACjC;AACA;IACD;AAGD,QACE3K,SAAS5C,MAAMuG,eACf7G,MAAMwQ,iBACNvG,QAAO9H,SAASnC,MAAMwQ,aAAtB,GACA;AACA;IACD;AAEDL,iBAAanQ,KAAD;EACb;AAED,WAASiQ,uBAAuBjQ,OAAuB;AACrD,WAAOsC,aAAaC,UAChBoL,yBAAwB,MAAO3N,MAAM9D,KAAKG,QAAQ,OAAnB,KAA+B,IAC9D;EACL;AAED,WAASoU,uBAA6B;AACpCC,0BAAqB;AAErB,QAAA,mBAMIxN,SAAS5C,OALXuH,gBADF,iBACEA,eACA9J,YAFF,iBAEEA,WACA4C,UAHF,iBAGEA,QACA+F,yBAJF,iBAIEA,wBACAK,iBALF,iBAKEA;AAGF,QAAMlB,SAAQ+H,qBAAoB,IAAK5D,YAAYC,OAAD,EAASpE,QAAQ;AAEnE,QAAM8K,oBAAoBjK,yBACtB;MACE4J,uBAAuB5J;MACvBkK,gBACElK,uBAAuBkK,kBAAkB/C,iBAAgB;IAH7D,IAKA9O;AAEJ,QAAM8R,gBAA8D;MAClEnI,MAAM;MACNoI,SAAS;MACTC,OAAO;MACPC,UAAU,CAAC,eAAD;MACVvU,IALkE,SAAAA,IAAA,OAKtD;AAAA,YAAR+C,SAAQ,MAARA;AACF,YAAIoO,qBAAoB,GAAI;AAC1B,cAAA,wBAAcG,2BAA0B,GAAjCpM,MAAP,sBAAOA;AAEP,WAAC,aAAa,oBAAoB,SAAlC,EAA6CzE,QAAQ,SAACsR,MAAS;AAC7D,gBAAIA,SAAS,aAAa;AACxB7M,kBAAIlC,aAAa,kBAAkBD,OAAMzB,SAAzC;YACD,OAAM;AACL,kBAAIyB,OAAMyR,WAAWhH,OAAjB,iBAAuCuE,IAAvC,GAAgD;AAClD7M,oBAAIlC,aAAJ,UAAyB+O,MAAQ,EAAjC;cACD,OAAM;AACL7M,oBAAIiJ,gBAAJ,UAA4B4D,IAA5B;cACD;YACF;UACF,CAVD;AAYAhP,UAAAA,OAAMyR,WAAWhH,SAAS,CAAA;QAC3B;MACF;IAvBiE;AA6BpE,QAAMiH,YAAsC,CAC1C;MACExI,MAAM;MACNkH,SAAS;QACPjP,QAAAA;MADO;IAFX,GAMA;MACE+H,MAAM;MACNkH,SAAS;QACPuB,SAAS;UACPtQ,KAAK;UACLG,QAAQ;UACRE,MAAM;UACNG,OAAO;QAJA;MADF;IAFX,GAWA;MACEqH,MAAM;MACNkH,SAAS;QACPuB,SAAS;MADF;IAFX,GAMA;MACEzI,MAAM;MACNkH,SAAS;QACPwB,UAAU,CAACrK;MADJ;IAFX,GAMA8J,aA9B0C;AAiC5C,QAAIjD,qBAAoB,KAAM/H,QAAO;AACnCqL,gBAAUvT,KAAK;QACb+K,MAAM;QACNkH,SAAS;UACPhQ,SAASiG;UACTsL,SAAS;QAFF;MAFI,CAAf;IAOD;AAEDD,cAAUvT,KAAV,MAAAuT,YAAmBrJ,iBAAa,OAAb,SAAAA,cAAeqJ,cAAa,CAAA,CAAtC;AAEThO,aAASgJ,iBAAiBmF,cACxBV,mBACA1G,SAFoC,OAAA,OAAA,CAAA,GAI/BpC,eAJ+B;MAKlC9J;MACA6N;MACAsF;IAPkC,CAAA,CAAA;EAUvC;AAED,WAASR,wBAA8B;AACrC,QAAIxN,SAASgJ,gBAAgB;AAC3BhJ,eAASgJ,eAAea,QAAxB;AACA7J,eAASgJ,iBAAiB;IAC3B;EACF;AAED,WAASoF,QAAc;AACrB,QAAOjL,WAAYnD,SAAS5C,MAArB+F;AAEP,QAAIyH;AAOJ,QAAMxD,OAAOuD,iBAAgB;AAE7B,QACG3K,SAAS5C,MAAMuG,eAAeR,aAAalL,2BAC5CkL,aAAa,UACb;AACAyH,mBAAaxD,KAAKwD;IACnB,OAAM;AACLA,mBAAaxR,uBAAuB+J,UAAU,CAACiE,IAAD,CAAX;IACpC;AAID,QAAI,CAACwD,WAAW3L,SAAS8H,OAApB,GAA6B;AAChC6D,iBAAWjE,YAAYI,OAAvB;IACD;AAED/G,aAAS1D,MAAM6M,YAAY;AAE3BoE,yBAAoB;AAGpB,QAAA,MAAa;AAEXlM,eACErB,SAAS5C,MAAMuG,eACbR,aAAaD,aAAaC,YAC1BiE,KAAKiH,uBAAuBtH,SAC9B,CACE,gEACA,qEACA,4BACA,QACA,oEACA,qDACA,QACA,sEACA,+DACA,wBACA,QACA,wEAZF,EAaEtG,KAAK,GAbP,CAJM;IAmBT;EACF;AAED,WAAS0M,sBAAuC;AAC9C,WAAOrS,UACLiM,QAAOhL,iBAAiB,mBAAxB,CADc;EAGjB;AAED,WAASuO,aAAaxN,OAAqB;AACzCkD,aAASqJ,mBAAT;AAEA,QAAIvM,OAAO;AACTuN,iBAAW,aAAa,CAACrK,UAAUlD,KAAX,CAAd;IACX;AAEDmP,qBAAgB;AAEhB,QAAI3I,QAAQwH,SAAS,IAAD;AACpB,QAAA,wBAAiCN,2BAA0B,GAApD8D,aAAP,sBAAA,CAAA,GAAmBC,aAAnB,sBAAA,CAAA;AAEA,QAAInP,aAAaC,WAAWiP,eAAe,UAAUC,YAAY;AAC/DjL,cAAQiL;IACT;AAED,QAAIjL,OAAO;AACT2E,oBAAcrO,WAAW,WAAM;AAC7BoG,iBAASuJ,KAAT;MACD,GAAEjG,KAFqB;IAGzB,OAAM;AACLtD,eAASuJ,KAAT;IACD;EACF;AAED,WAAS0D,aAAanQ,OAAoB;AACxCkD,aAASqJ,mBAAT;AAEAgB,eAAW,eAAe,CAACrK,UAAUlD,KAAX,CAAhB;AAEV,QAAI,CAACkD,SAAS1D,MAAM4D,WAAW;AAC7B4L,0BAAmB;AAEnB;IACD;AAMD,QACE9L,SAAS5C,MAAM2H,QAAQ5L,QAAQ,YAA/B,KAAgD,KAChD6G,SAAS5C,MAAM2H,QAAQ5L,QAAQ,OAA/B,KAA2C,KAC3C,CAAC,cAAc,WAAf,EAA4BA,QAAQ2D,MAAM9D,IAA1C,KAAmD,KACnDoP,oBACA;AACA;IACD;AAED,QAAM9E,QAAQwH,SAAS,KAAD;AAEtB,QAAIxH,OAAO;AACT4E,oBAActO,WAAW,WAAM;AAC7B,YAAIoG,SAAS1D,MAAM4D,WAAW;AAC5BF,mBAASwJ,KAAT;QACD;MACF,GAAElG,KAJqB;IAKzB,OAAM;AAGL6E,mCAA6BqG,sBAAsB,WAAM;AACvDxO,iBAASwJ,KAAT;MACD,CAFiD;IAGnD;EACF;AAKD,WAASE,SAAe;AACtB1J,aAAS1D,MAAM2M,YAAY;EAC5B;AAED,WAASU,UAAgB;AAGvB3J,aAASwJ,KAAT;AACAxJ,aAAS1D,MAAM2M,YAAY;EAC5B;AAED,WAASI,qBAA2B;AAClC1P,iBAAasO,WAAD;AACZtO,iBAAauO,WAAD;AACZuG,yBAAqBtG,0BAAD;EACrB;AAED,WAASmB,SAASnE,cAAoC;AAEpD,QAAA,MAAa;AACX9D,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,UAAD,CAApD;IACT;AAED,QAAIP,SAAS1D,MAAM4M,aAAa;AAC9B;IACD;AAEDmB,eAAW,kBAAkB,CAACrK,UAAUmF,YAAX,CAAnB;AAEV0H,oBAAe;AAEf,QAAMrF,YAAYxH,SAAS5C;AAC3B,QAAMqK,YAAYzB,cAAcnK,YAAD,OAAA,OAAA,CAAA,GAC1B2L,WACAxM,qBAAqBmK,YAAD,GAFM;MAG7BzB,kBAAkB;IAHW,CAAA,CAAA;AAM/B1D,aAAS5C,QAAQqK;AAEjByC,iBAAY;AAEZ,QAAI1C,UAAU5D,wBAAwB6D,UAAU7D,qBAAqB;AACnE8H,uCAAgC;AAChC9C,6BAAuBtP,UACrBuP,aACApB,UAAU7D,mBAFmB;IAIhC;AAGD,QAAI4D,UAAUxC,iBAAiB,CAACyC,UAAUzC,eAAe;AACvD3K,uBAAiBmN,UAAUxC,aAAX,EAA0BhL,QAAQ,SAACoN,MAAS;AAC1DA,aAAKM,gBAAgB,eAArB;MACD,CAFD;IAGD,WAAUD,UAAUzC,eAAe;AAClCnJ,MAAAA,WAAU6L,gBAAgB,eAA1B;IACD;AAEDyC,gCAA2B;AAC3BC,iBAAY;AAEZ,QAAI7C,UAAU;AACZA,eAASC,WAAWC,SAAZ;IACT;AAED,QAAIzH,SAASgJ,gBAAgB;AAC3BuE,2BAAoB;AAMpBJ,0BAAmB,EAAGnT,QAAQ,SAAC0U,cAAiB;AAG9CF,8BAAsBE,aAAa9S,OAAQoN,eAAgB2F,WAAtC;MACtB,CAJD;IAKD;AAEDtE,eAAW,iBAAiB,CAACrK,UAAUmF,YAAX,CAAlB;EACX;AAED,WAASyB,YAAWhE,SAAwB;AAC1C5C,aAASsJ,SAAS;MAAC1G;IAAD,CAAlB;EACD;AAED,WAAS2G,OAAa;AAEpB,QAAA,MAAa;AACXlI,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,MAAD,CAApD;IACT;AAGD,QAAMqO,mBAAmB5O,SAAS1D,MAAM4D;AACxC,QAAMgJ,cAAclJ,SAAS1D,MAAM4M;AACnC,QAAM2F,aAAa,CAAC7O,SAAS1D,MAAM2M;AACnC,QAAM6F,0BACJ1P,aAAaC,WAAW,CAACW,SAAS5C,MAAM0H;AAC1C,QAAMvB,WAAW/K,wBACfwH,SAAS5C,MAAMmG,UACf,GACAL,aAAaK,QAHyB;AAMxC,QACEqL,oBACA1F,eACA2F,cACAC,yBACA;AACA;IACD;AAKD,QAAInE,iBAAgB,EAAGV,aAAa,UAAhC,GAA6C;AAC/C;IACD;AAEDI,eAAW,UAAU,CAACrK,QAAD,GAAY,KAAvB;AACV,QAAIA,SAAS5C,MAAMiH,OAAOrE,QAAtB,MAAoC,OAAO;AAC7C;IACD;AAEDA,aAAS1D,MAAM4D,YAAY;AAE3B,QAAIwK,qBAAoB,GAAI;AAC1B3D,MAAAA,QAAO5K,MAAM4S,aAAa;IAC3B;AAED3E,iBAAY;AACZ6B,qBAAgB;AAEhB,QAAI,CAACjM,SAAS1D,MAAM6M,WAAW;AAC7BpC,MAAAA,QAAO5K,MAAM6S,aAAa;IAC3B;AAID,QAAItE,qBAAoB,GAAI;AAC1B,UAAA,yBAAuBG,2BAA0B,GAA1CpM,MAAP,uBAAOA,KAAKmE,UAAZ,uBAAYA;AACZ5G,4BAAsB,CAACyC,KAAKmE,OAAN,GAAgB,CAAjB;IACtB;AAED8F,oBAAgB,SAAAA,iBAAY;AAAA,UAAA;AAC1B,UAAI,CAAC1I,SAAS1D,MAAM4D,aAAaqI,qBAAqB;AACpD;MACD;AAEDA,4BAAsB;AAGtB,WAAKxB,QAAOkI;AAEZlI,MAAAA,QAAO5K,MAAM6S,aAAahP,SAAS5C,MAAMyG;AAEzC,UAAI6G,qBAAoB,KAAM1K,SAAS5C,MAAMsF,WAAW;AACtD,YAAA,yBAAuBmI,2BAA0B,GAA1CpM,OAAP,uBAAOA,KAAKmE,WAAZ,uBAAYA;AACZ5G,8BAAsB,CAACyC,MAAKmE,QAAN,GAAgBW,QAAjB;AACrBlH,2BAAmB,CAACoC,MAAKmE,QAAN,GAAgB,SAAjB;MACnB;AAEDyI,iCAA0B;AAC1BlB,kCAA2B;AAE3B5P,mBAAawN,kBAAkB/H,QAAnB;AAIZ,OAAA,yBAAAA,SAASgJ,mBAAT,OAAA,SAAA,uBAAyB2F,YAAzB;AAEAtE,iBAAW,WAAW,CAACrK,QAAD,CAAZ;AAEV,UAAIA,SAAS5C,MAAMsF,aAAagI,qBAAoB,GAAI;AACtD4B,yBAAiB/I,UAAU,WAAM;AAC/BvD,mBAAS1D,MAAM8M,UAAU;AACzBiB,qBAAW,WAAW,CAACrK,QAAD,CAAZ;QACX,CAHe;MAIjB;IACF;AAEDoO,UAAK;EACN;AAED,WAAS5E,QAAa;AAEpB,QAAA,MAAa;AACXnI,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,MAAD,CAApD;IACT;AAGD,QAAM2O,kBAAkB,CAAClP,SAAS1D,MAAM4D;AACxC,QAAMgJ,cAAclJ,SAAS1D,MAAM4M;AACnC,QAAM2F,aAAa,CAAC7O,SAAS1D,MAAM2M;AACnC,QAAM1F,WAAW/K,wBACfwH,SAAS5C,MAAMmG,UACf,GACAL,aAAaK,QAHyB;AAMxC,QAAI2L,mBAAmBhG,eAAe2F,YAAY;AAChD;IACD;AAEDxE,eAAW,UAAU,CAACrK,QAAD,GAAY,KAAvB;AACV,QAAIA,SAAS5C,MAAM+G,OAAOnE,QAAtB,MAAoC,OAAO;AAC7C;IACD;AAEDA,aAAS1D,MAAM4D,YAAY;AAC3BF,aAAS1D,MAAM8M,UAAU;AACzBb,0BAAsB;AACtBH,yBAAqB;AAErB,QAAIsC,qBAAoB,GAAI;AAC1B3D,MAAAA,QAAO5K,MAAM4S,aAAa;IAC3B;AAEDrD,qCAAgC;AAChCI,wBAAmB;AACnB1B,iBAAa,IAAD;AAEZ,QAAIM,qBAAoB,GAAI;AAC1B,UAAA,yBAAuBG,2BAA0B,GAA1CpM,MAAP,uBAAOA,KAAKmE,UAAZ,uBAAYA;AAEZ,UAAI5C,SAAS5C,MAAMsF,WAAW;AAC5B1G,8BAAsB,CAACyC,KAAKmE,OAAN,GAAgBW,QAAjB;AACrBlH,2BAAmB,CAACoC,KAAKmE,OAAN,GAAgB,QAAjB;MACnB;IACF;AAEDyI,+BAA0B;AAC1BlB,gCAA2B;AAE3B,QAAInK,SAAS5C,MAAMsF,WAAW;AAC5B,UAAIgI,qBAAoB,GAAI;AAC1ByB,0BAAkB5I,UAAUvD,SAAS4J,OAApB;MAClB;IACF,OAAM;AACL5J,eAAS4J,QAAT;IACD;EACF;AAED,WAASH,sBAAsB3M,OAAyB;AAEtD,QAAA,MAAa;AACXuE,eACErB,SAAS1D,MAAM4M,aACf3I,wBAAwB,uBAAD,CAFjB;IAIT;AAEDgK,gBAAW,EAAG7K,iBAAiB,aAAakJ,oBAA5C;AACArO,iBAAauN,oBAAoBc,oBAArB;AACZA,yBAAqB9L,KAAD;EACrB;AAED,WAAS8M,UAAgB;AAEvB,QAAA,MAAa;AACXvI,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,SAAD,CAApD;IACT;AAED,QAAIP,SAAS1D,MAAM4D,WAAW;AAC5BF,eAASwJ,KAAT;IACD;AAED,QAAI,CAACxJ,SAAS1D,MAAM6M,WAAW;AAC7B;IACD;AAEDqE,0BAAqB;AAKrBL,wBAAmB,EAAGnT,QAAQ,SAAC0U,cAAiB;AAC9CA,mBAAa9S,OAAQgO,QAArB;IACD,CAFD;AAIA,QAAI7C,QAAO6D,YAAY;AACrB7D,MAAAA,QAAO6D,WAAWjD,YAAYZ,OAA9B;IACD;AAEDgB,uBAAmBA,iBAAiB5N,OAAO,SAACgV,GAAD;AAAA,aAAOA,MAAMnP;IAAb,CAAxB;AAEnBA,aAAS1D,MAAM6M,YAAY;AAC3BkB,eAAW,YAAY,CAACrK,QAAD,CAAb;EACX;AAED,WAAS6J,UAAgB;AAEvB,QAAA,MAAa;AACXxI,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,SAAD,CAApD;IACT;AAED,QAAIP,SAAS1D,MAAM4M,aAAa;AAC9B;IACD;AAEDlJ,aAASqJ,mBAAT;AACArJ,aAAS4J,QAAT;AAEAiD,oBAAe;AAEf,WAAOhR,WAAUD;AAEjBoE,aAAS1D,MAAM4M,cAAc;AAE7BmB,eAAW,aAAa,CAACrK,QAAD,CAAd;EACX;AACF;AC/mCD,SAASoP,MACPtN,SACAuN,eACuB;AAAA,MADvBA,kBACuB,QAAA;AADvBA,oBAAgC,CAAA;EACT;AACvB,MAAM3K,UAAUxB,aAAawB,QAAQpK,OAAO+U,cAAc3K,WAAW,CAAA,CAArD;AAGhB,MAAA,MAAa;AACX7C,oBAAgBC,OAAD;AACfsD,kBAAciK,eAAe3K,OAAhB;EACd;AAEDvE,2BAAwB;AAExB,MAAMmF,cAA2B,OAAA,OAAA,CAAA,GAAO+J,eAAP;IAAsB3K;EAAtB,CAAA;AAEjC,MAAM4K,WAAWxT,mBAAmBgG,OAAD;AAGnC,MAAA,MAAa;AACX,QAAMyN,yBAAyBhU,WAAU+J,YAAY1C,OAAb;AACxC,QAAM4M,gCAAgCF,SAASjJ,SAAS;AACxDhF,aACEkO,0BAA0BC,+BAC1B,CACE,sEACA,qEACA,qEACA,QACA,uEACA,oDACA,QACA,mCACA,2CATF,EAUE/O,KAAK,GAVP,CAFM;EAcT;AAED,MAAMgP,YAAYH,SAASpU,OACzB,SAACC,KAAKU,YAA0B;AAC9B,QAAMmE,WAAWnE,cAAamM,YAAYnM,YAAWyJ,WAAZ;AAEzC,QAAItF,UAAU;AACZ7E,UAAIV,KAAKuF,QAAT;IACD;AAED,WAAO7E;EACR,GACD,CAAA,CAVgB;AAalB,SAAOI,WAAUuG,OAAD,IAAY2N,UAAU,CAAD,IAAMA;AAC5C;AAEDL,MAAMlM,eAAeA;AACrBkM,MAAMlK,kBAAkBA;AACxBkK,MAAMhQ,eAAeA;AC9CrB,IAAMsQ,sBAAqE,OAAA,OAAA,CAAA,GACtEC,qBADsE;EAEzEC,QAFyE,SAAAA,QAAA,MAEzD;AAAA,QAARC,QAAQ,KAARA;AACN,QAAMC,gBAAgB;MACpBC,QAAQ;QACNC,UAAUH,MAAMI,QAAQC;QACxBC,MAAM;QACNC,KAAK;QACLC,QAAQ;MAJF;MAMRC,OAAO;QACLN,UAAU;MADL;MAGPO,WAAW,CAAA;IAVS;AAatBC,WAAOC,OAAOZ,MAAMa,SAASX,OAAOY,OAAOb,cAAcC,MAAzD;AACAF,UAAMe,SAASd;AAEf,QAAID,MAAMa,SAASJ,OAAO;AACxBE,aAAOC,OAAOZ,MAAMa,SAASJ,MAAMK,OAAOb,cAAcQ,KAAxD;IACD;EAIF;AAzBwE,CAAA;AMhB3EO,MAAMC,gBAAgB;EAACC;AAAD,CAAtB;;;;IC2Da,uBAAc;EA6CzB,YAAY,EACV,QACA,SACA,MACA,eAAe,CAAA,GACf,cAAc,KACd,WAAU,GACU;AA7Cf,SAAW,cAAG;AAUd,SAAA,aAAiE,CAAC,EACvE,MAAAC,OACA,OACA,MACA,GAAE,MACC;AACH,YAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,YAAM,EAAE,MAAK,IAAK;AAKlB,YAAM,mBAAmB,CAAC,IAAI,YAAY,MAAM,EAAE,EAAE,UAAU,gBAAgB,MAAM,SAAS;AAK7F,YAAM,gBAAgB,KAAK,QAAQ,SAAS,SAAS,aAAa;AAElE,YAAM,iBAAiBA,MAAK,SAAQ,KAAM;AAE1C,UAAI,CAAC,kBAAkB,SAAS,oBAAoB,CAAC,KAAK,OAAO,YAAY;AAC3E,eAAO;;AAGT,aAAO;IACT;AA6BA,SAAgB,mBAAG,MAAK;AACtB,WAAK,cAAc;IACrB;AAEA,SAAgB,mBAAG,MAAK;AACtB,WAAK,KAAI;IACX;AAEA,SAAY,eAAG,MAAK;AAElB,iBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC;IAChD;AAEA,SAAA,cAAc,CAAC,EAAE,MAAK,MAA6B;;AACjD,UAAI,KAAK,aAAa;AACpB,aAAK,cAAc;AAEnB;;AAGF,WAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,oBAAiB,KAAA,KAAK,QAAQ,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,SAAS,MAAM,aAAqB,IAAG;AAC1F;;AAGF,WACE,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,mBAAkB,KAAK,OAAO,KAAK,KAC1C;AACA;;AAGF,WAAK,KAAI;IACX;AAEA,SAAA,mBAAmB,CAAC,UAAqB;AACvC,WAAK,YAAY,EAAE,MAAK,CAAE;IAC5B;AA0CA,SAAA,wBAAwB,CAACA,OAAkB,aAA0B;AACnE,YAAM,mBAAmB,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,UAAU,GAAGA,MAAK,MAAM,SAAS;AACrE,YAAM,aAAa,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,IAAI,GAAGA,MAAK,MAAM,GAAG;AAEnD,UAAI,CAAC,oBAAoB,CAAC,YAAY;AACpC;;AAGF,UAAI,KAAK,qBAAqB;AAC5B,qBAAa,KAAK,mBAAmB;;AAGvC,WAAK,sBAAsB,OAAO,WAAW,MAAK;AAChD,aAAK,cAAcA,OAAM,kBAAkB,YAAY,QAAQ;MACjE,GAAG,KAAK,WAAW;IACrB;AAEA,SAAa,gBAAG,CAACA,OAAkB,kBAA2B,YAAqB,aAA0B;;AAC3G,YAAM,EAAE,OAAO,UAAS,IAAKA;AAC7B,YAAM,EAAE,UAAS,IAAK;AAEtB,YAAM,SAAS,CAAC,oBAAoB,CAAC;AAErC,UAAI,aAAa,QAAQ;AACvB;;AAGF,WAAK,cAAa;AAGlB,YAAM,EAAE,OAAM,IAAK;AACnB,YAAM,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC;AAC7D,YAAM,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,IAAI,GAAG,CAAC;AAEzD,YAAMC,eAAa,KAAA,KAAK,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAA;QACnC,QAAQ,KAAK;QACb,SAAS,KAAK;QACd,MAAAD;QACA;QACA;QACA;QACA;MACD,CAAA;AAED,UAAI,CAACC,aAAY;AACf,aAAK,KAAI;AAET;;AAGF,OAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;QACnB,0BACE,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,4BACf,MAAK;AACP,cAAI,gBAAgB,MAAM,SAAS,GAAG;AACpC,gBAAI,OAAOD,MAAK,QAAQ,IAAI;AAE5B,gBAAI,MAAM;AACR,oBAAM,kBAAkB,KAAK,QAAQ,kBAAkB,OAAO,KAAK,cAAc,0BAA0B;AAE3G,kBAAI,iBAAiB;AACnB,uBAAO,gBAAgB;;AAGzB,kBAAI,MAAM;AACR,uBAAO,KAAK,sBAAqB;;;;AAKvC,iBAAO,aAAaA,OAAM,MAAM,EAAE;QACpC;MACH,CAAA;AAED,WAAK,KAAI;IACX;AA3KE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,cAAc;AAEnB,QAAI,YAAY;AACd,WAAK,aAAa;;AAGpB,SAAK,QAAQ,iBAAiB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACnF,SAAK,KAAK,IAAI,iBAAiB,aAAa,KAAK,gBAAgB;AACjE,SAAK,OAAO,GAAG,SAAS,KAAK,YAAY;AACzC,SAAK,OAAO,GAAG,QAAQ,KAAK,WAAW;AACvC,SAAK,eAAe;AAEpB,SAAK,QAAQ,OAAM;AACnB,SAAK,QAAQ,MAAM,aAAa;;EAwClC,gBAAa;AACX,UAAM,EAAE,SAAS,cAAa,IAAK,KAAK,OAAO;AAC/C,UAAM,mBAAmB,CAAC,CAAC,cAAc;AAEzC,QAAI,KAAK,SAAS,CAAC,kBAAkB;AACnC;;AAGF,SAAK,QAAQ,kBAAM,eAAe;MAChC,UAAU;MACV,wBAAwB;MACxB,SAAS,KAAK;MACd,aAAa;MACb,SAAS;MACT,WAAW;MACX,aAAa;MACb,GAAG,KAAK;IACT,CAAA;AAGD,QAAI,KAAK,MAAM,OAAO,YAAY;AAC/B,WAAK,MAAM,OAAO,WAA2B,iBAAiB,QAAQ,KAAK,gBAAgB;;;EAIhG,OAAO,MAAkB,UAAsB;AAC7C,UAAM,EAAE,MAAK,IAAK;AAClB,UAAM,oBAAoB,MAAM,UAAU,SAAS,MAAM,UAAU;AAEnE,QAAI,KAAK,cAAc,KAAK,mBAAmB;AAC7C,WAAK,sBAAsB,MAAM,QAAQ;AACzC;;AAGF,UAAM,mBAAmB,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,UAAU,GAAG,KAAK,MAAM,SAAS;AACrE,UAAM,aAAa,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,IAAI,GAAG,KAAK,MAAM,GAAG;AAEnD,SAAK,cAAc,MAAM,kBAAkB,YAAY,QAAQ;;EAgFjE,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,UAAO;;AACL,SAAI,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,YAAY;AAChC,WAAK,MAAM,OAAO,WAA2B,oBAC5C,QACA,KAAK,gBAAgB;;AAGzB,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO;AACnB,SAAK,QAAQ,oBAAoB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACtF,SAAK,KAAK,IAAI,oBAAoB,aAAa,KAAK,gBAAgB;AACpE,SAAK,OAAO,IAAI,SAAS,KAAK,YAAY;AAC1C,SAAK,OAAO,IAAI,QAAQ,KAAK,WAAW;;AAE3C;AAEY,IAAA,mBAAmB,CAAC,YAAkC;AACjE,SAAO,IAAI,OAAO;IAChB,KACE,OAAO,QAAQ,cAAc,WAAW,IAAI,UAAU,QAAQ,SAAS,IAAI,QAAQ;IACrF,MAAM,UAAQ,IAAI,eAAe,EAAE,MAAM,GAAG,QAAO,CAAE;EACtD,CAAA;AACH;AC5Sa,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,SAAS;MACT,cAAc,CAAA;MACd,WAAW;MACX,aAAa;MACb,YAAY;;;EAIhB,wBAAqB;AACnB,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAO,CAAA;;AAGT,WAAO;MACL,iBAAiB;QACf,WAAW,KAAK,QAAQ;QACxB,QAAQ,KAAK;QACb,SAAS,KAAK,QAAQ;QACtB,cAAc,KAAK,QAAQ;QAC3B,aAAa,KAAK,QAAQ;QAC1B,YAAY,KAAK,QAAQ;OAC1B;;;AAGN,CAAA;;;;;;;ICUY,yBAAgB;EAanB,eAAe,MAAoB;AACzC,WAAO,QAAQ,MAAM,EAAE,iBAAiB,6BAA6B,KAAK,OAAO,MAAM,EAAC,CAAE;;EAuB5F,YAAY,EACV,QAAQ,SAAS,MAAM,eAAe,CAAA,GAAI,WAAU,GAC9B;AAhCjB,SAAW,cAAG;AAUd,SAAU,aAAyD,CAAC,EAAE,MAAAE,OAAM,MAAK,MAAM;AAC5F,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,SAAS,MAAK,IAAK;AAC3B,YAAM,cAAc,QAAQ,UAAU;AAEtC,YAAM,mBAAmB,QAAQ,OAAO,eAAe,CAAC,QAAQ,OAAO,KAAK,KAAK,QAAQ,CAAC,QAAQ,OAAO,eAAe,QAAQ,OAAO,eAAe,KAAK,CAAC,KAAK,eAAe,QAAQ,MAAM;AAE9L,UACE,CAACA,MAAK,SAAQ,KACX,CAAC,SACD,CAAC,eACD,CAAC,oBACD,CAAC,KAAK,OAAO,YAChB;AACA,eAAO;;AAGT,aAAO;IACT;AAsBA,SAAgB,mBAAG,MAAK;AACtB,WAAK,cAAc;IACrB;AAEA,SAAY,eAAG,MAAK;AAElB,iBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC;IAChD;AAEA,SAAA,cAAc,CAAC,EAAE,MAAK,MAA6B;;AACjD,UAAI,KAAK,aAAa;AACpB,aAAK,cAAc;AAEnB;;AAGF,WAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,oBAAiB,KAAA,KAAK,QAAQ,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,SAAS,MAAM,aAAqB,IAAG;AAC1F;;AAGF,WACE,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,mBAAkB,KAAK,OAAO,KAAK,KAC1C;AACA;;AAGF,WAAK,KAAI;IACX;AAEA,SAAA,mBAAmB,CAAC,UAAqB;AACvC,WAAK,YAAY,EAAE,MAAK,CAAE;IAC5B;AAhDE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,QAAI,YAAY;AACd,WAAK,aAAa;;AAGpB,SAAK,QAAQ,iBAAiB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACnF,SAAK,OAAO,GAAG,SAAS,KAAK,YAAY;AACzC,SAAK,OAAO,GAAG,QAAQ,KAAK,WAAW;AACvC,SAAK,eAAe;AAEpB,SAAK,QAAQ,OAAM;AACnB,SAAK,QAAQ,MAAM,aAAa;;EAoClC,gBAAa;AACX,UAAM,EAAE,SAAS,cAAa,IAAK,KAAK,OAAO;AAC/C,UAAM,mBAAmB,CAAC,CAAC,cAAc;AAEzC,QAAI,KAAK,SAAS,CAAC,kBAAkB;AACnC;;AAGF,SAAK,QAAQ,kBAAM,eAAe;MAChC,UAAU;MACV,wBAAwB;MACxB,SAAS,KAAK;MACd,aAAa;MACb,SAAS;MACT,WAAW;MACX,aAAa;MACb,GAAG,KAAK;IACT,CAAA;AAGD,QAAI,KAAK,MAAM,OAAO,YAAY;AAC/B,WAAK,MAAM,OAAO,WAA2B,iBAAiB,QAAQ,KAAK,gBAAgB;;;EAIhG,OAAO,MAAkB,UAAsB;;AAC7C,UAAM,EAAE,MAAK,IAAK;AAClB,UAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,UAAM,EAAE,MAAM,GAAE,IAAK;AACrB,UAAM,SAAS,YAAY,SAAS,IAAI,GAAG,GAAG,KAAK,SAAS,UAAU,GAAG,SAAS;AAElF,QAAI,QAAQ;AACV;;AAGF,SAAK,cAAa;AAElB,UAAM,cAAa,KAAA,KAAK,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAA;MACnC,QAAQ,KAAK;MACb;MACA;MACA;IACD,CAAA;AAED,QAAI,CAAC,YAAY;AACf,WAAK,KAAI;AAET;;AAGF,KAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;MACnB,0BACE,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,4BAA2B,MAAM,aAAa,MAAM,MAAM,EAAE;IAClF,CAAA;AAED,SAAK,KAAI;;EAGX,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,UAAO;;AACL,SAAI,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,YAAY;AAChC,WAAK,MAAM,OAAO,WAA2B,oBAC5C,QACA,KAAK,gBAAgB;;AAGzB,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO;AACnB,SAAK,QAAQ,oBAAoB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACtF,SAAK,OAAO,IAAI,SAAS,KAAK,YAAY;AAC1C,SAAK,OAAO,IAAI,QAAQ,KAAK,WAAW;;AAE3C;AAEY,IAAA,qBAAqB,CAAC,YAAoC;AACrE,SAAO,IAAI,OAAO;IAChB,KACE,OAAO,QAAQ,cAAc,WAAW,IAAI,UAAU,QAAQ,SAAS,IAAI,QAAQ;IACrF,MAAM,UAAQ,IAAI,iBAAiB,EAAE,MAAM,GAAG,QAAO,CAAE;EACxD,CAAA;AACH;ACvNa,IAAA,eAAe,UAAU,OAA4B;EAChE,MAAM;EAEN,aAAU;AACR,WAAO;MACL,SAAS;MACT,cAAc,CAAA;MACd,WAAW;MACX,YAAY;;;EAIhB,wBAAqB;AACnB,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAO,CAAA;;AAGT,WAAO;MACL,mBAAmB;QACjB,WAAW,KAAK,QAAQ;QACxB,QAAQ,KAAK;QACb,SAAS,KAAK,QAAQ;QACtB,cAAc,KAAK,QAAQ;QAC3B,YAAY,KAAK,QAAQ;OAC1B;;;AAGN,CAAA;;;;;;;;;;;;AEhCD,MAAI,MAAuC;AACzC,KAAC,WAAW;AAKd,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,uCAA+B,4BAA4B,IAAI,MAAK,CAAE;;AAE9D,UAAIC,UAAQC,aAAAA;AAEtB,UAAI,uBAAuBD,QAAM;AAEjC,eAAS,MAAM,QAAQ;AACrB;AACE;AACE,qBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,mBAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;;AAGnC,yBAAa,SAAS,QAAQ,IAAI;;;;AAKxC,eAAS,aAAa,OAAO,QAAQ,MAAM;AAGzC;AACE,cAAI,yBAAyB,qBAAqB;AAClD,cAAI,QAAQ,uBAAuB,iBAAgB;AAEnD,cAAI,UAAU,IAAI;AAChB,sBAAU;AACV,mBAAO,KAAK,OAAO,CAAC,KAAK,CAAC;;AAI5B,cAAI,iBAAiB,KAAK,IAAI,SAAU,MAAM;AAC5C,mBAAO,OAAO,IAAI;UACxB,CAAK;AAED,yBAAe,QAAQ,cAAc,MAAM;AAI3C,mBAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,GAAG,SAAS,cAAc;;;AAQzE,eAAS,GAAG,GAAG,GAAG;AAChB,eAAO,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM;;AAIrE,UAAI,WAAW,OAAO,OAAO,OAAO,aAAa,OAAO,KAAK;AAI7D,UAAIE,YAAWF,QAAM,UACjBG,aAAYH,QAAM,WAClBI,mBAAkBJ,QAAM,iBACxBK,iBAAgBL,QAAM;AAC1B,UAAI,oBAAoB;AACxB,UAAI,6BAA6B;AAWjC,eAAS,qBAAqB,WAAW,aAIzC,mBAAmB;AACjB;AACE,cAAI,CAAC,mBAAmB;AACtB,gBAAIA,QAAM,oBAAoB,QAAW;AACvC,kCAAoB;AAEpB,oBAAM,gMAA+M;;;;AAS3N,YAAI,QAAQ,YAAW;AAEvB;AACE,cAAI,CAAC,4BAA4B;AAC/B,gBAAI,cAAc,YAAW;AAE7B,gBAAI,CAAC,SAAS,OAAO,WAAW,GAAG;AACjC,oBAAM,sEAAsE;AAE5E,2CAA6B;;;;AAmBnC,YAAI,YAAYE,UAAS;UACvB,MAAM;YACJ;YACA;;QAEN,CAAG,GACG,OAAO,UAAU,CAAC,EAAE,MACpB,cAAc,UAAU,CAAC;AAK7B,QAAAE,iBAAgB,WAAY;AAC1B,eAAK,QAAQ;AACb,eAAK,cAAc;AAKnB,cAAI,uBAAuB,IAAI,GAAG;AAEhC,wBAAY;cACV;YACR,CAAO;;WAEF,CAAC,WAAW,OAAO,WAAW,CAAC;AAClC,QAAAD,WAAU,WAAY;AAGpB,cAAI,uBAAuB,IAAI,GAAG;AAEhC,wBAAY;cACV;YACR,CAAO;;AAGH,cAAI,oBAAoB,WAAY;AAOlC,gBAAI,uBAAuB,IAAI,GAAG;AAEhC,0BAAY;gBACV;cACV,CAAS;;UAET;AAGI,iBAAO,UAAU,iBAAiB;QACtC,GAAK,CAAC,SAAS,CAAC;AACd,QAAAE,eAAc,KAAK;AACnB,eAAO;;AAGT,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,YAAI,YAAY,KAAK;AAErB,YAAI;AACF,cAAI,YAAY,kBAAiB;AACjC,iBAAO,CAAC,SAAS,WAAW,SAAS;iBAC9BC,QAAO;AACd,iBAAO;;;AAIX,eAAS,uBAAuB,WAAW,aAAa,mBAAmB;AAKzE,eAAO,YAAW;;AAGpB,UAAI,YAAY,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB;AAEvI,UAAI,sBAAsB,CAAC;AAE3B,UAAIC,QAAO,sBAAsB,yBAAyB;AAC1D,UAAI,yBAAyBP,QAAM,yBAAyB,SAAYA,QAAM,uBAAuBO;AAEzE,2CAAA,uBAAG;AAE/B,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,uCAA+B,2BAA2B,IAAI,MAAK,CAAE;;IAGvE,GAAG;EACH;;;AC5OA,IAAI,OAAuC;AACzCC,OAAA,UAAiBP,+CAAA;AACnB,OAAO;AACLO,OAAA,UAAiBC,4CAAA;AACnB;;ACIA,IAAM,YAAY,IACb,SACD;AACF,SAAO,CAAC,SAAW;AACjB,SAAK,QAAQ,SAAM;AACjB,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,IAAI;iBACC,KAAK;AACb,YAAmC,UAAU;;IAElD,CAAC;EACH;AACF;AAKA,IAAM,UAA4D,CAAC,EACjE,iBAAgB,MACb;AAEH,QAAM,YAAYC,YAAAA,qBAChB,iBAAiB,WACjB,iBAAiB,aACjB,iBAAiB,iBAAiB;AAIpC,SACE,aAAAV,QACG,cAAA,aAAAA,QAAA,UAAA,MAAA,OAAO,OAAO,SAAS,CAAC;AAG/B;AAOA,SAAS,cAAW;AAClB,QAAM,cAAc,oBAAI,IAAG;AAC3B,MAAI,YAA+C,CAAA;AAEnD,SAAO;;;;IAIL,UAAU,UAAoB;AAC5B,kBAAY,IAAI,QAAQ;AACxB,aAAO,MAAK;AACV,oBAAY,OAAO,QAAQ;MAC7B;;IAEF,cAAW;AACT,aAAO;;IAET,oBAAiB;AACf,aAAO;;;;;IAKT,YAAY,IAAY,UAAuB;AAC7C,kBAAY;QACV,GAAG;QACH,CAAC,EAAE,GAAG,iBAAAW,QAAS,aAAa,SAAS,cAAc,SAAS,SAAS,EAAE;;AAGzE,kBAAY,QAAQ,gBAAc,WAAU,CAAE;;;;;IAKhD,eAAe,IAAU;AACvB,YAAM,gBAAgB,EAAE,GAAG,UAAS;AAEpC,aAAO,cAAc,EAAE;AACvB,kBAAY;AACZ,kBAAY,QAAQ,gBAAc,WAAU,CAAE;;;AAGpD;AAEa,IAAA,oBAAA,cAA0B,aAAAX,QAAM,UAG5C;EAOC,YAAY,OAAyB;;AACnC,UAAM,KAAK;AACX,SAAK,mBAAmB,aAAAA,QAAM,UAAS;AACvC,SAAK,cAAc;AAEnB,SAAK,QAAQ;MACX,gCAAgC,SAAQ,KAAC,MAAM,YAA8C,QAAA,OAAA,SAAA,SAAA,GAAA,gBAAgB;;;EAIjH,oBAAiB;AACf,SAAK,KAAI;;EAGX,qBAAkB;AAChB,SAAK,KAAI;;EAGX,OAAI;AACF,UAAM,SAAS,KAAK,MAAM;AAE1B,QAAI,UAAU,CAAC,OAAO,eAAe,OAAO,QAAQ,SAAS;AAC3D,UAAI,OAAO,kBAAkB;AAC3B;;AAGF,YAAM,UAAU,KAAK,iBAAiB;AAEtC,cAAQ,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAEnD,aAAO,WAAW;QAChB;MACD,CAAA;AAED,aAAO,mBAAmB,YAAW;AAGrC,UAAI,CAAC,KAAK,MAAM,gCAAgC;AAE9C,aAAK,gCAAgC,OAAO,iBAAiB,UAAU,MAAK;AAC1E,eAAK,SAAS,eAAY;AACxB,gBAAI,CAAC,UAAU,gCAAgC;AAC7C,qBAAO;gBACL,gCAAgC;;;AAGpC,mBAAO;UACT,CAAC;AAGD,cAAI,KAAK,+BAA+B;AACtC,iBAAK,8BAA6B;;QAEtC,CAAC;;AAGH,aAAO,gBAAe;AAEtB,WAAK,cAAc;;;EAIvB,uBAAoB;AAClB,UAAM,SAAS,KAAK,MAAM;AAE1B,QAAI,CAAC,QAAQ;AACX;;AAGF,SAAK,cAAc;AAEnB,QAAI,CAAC,OAAO,aAAa;AACvB,aAAO,KAAK,SAAS;QACnB,WAAW,CAAA;MACZ,CAAA;;AAGH,QAAI,KAAK,+BAA+B;AACtC,WAAK,8BAA6B;;AAGpC,WAAO,mBAAmB;AAE1B,QAAI,CAAC,OAAO,QAAQ,QAAQ,YAAY;AACtC;;AAGF,UAAM,aAAa,SAAS,cAAc,KAAK;AAE/C,eAAW,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAEtD,WAAO,WAAW;MAChB,SAAS;IACV,CAAA;;EAGH,SAAM;AACJ,UAAM,EAAE,QAAQ,UAAU,GAAG,KAAI,IAAK,KAAK;AAE3C,WACE,aAAAA,QAAA;MAAA,aAAAA,QAAA;MAAA;MACE,aAAAA,QAAA,cAAA,OAAA,EAAK,KAAK,UAAU,UAAU,KAAK,gBAAgB,GAAO,GAAA,KAAI,CAAI;OAEjE,WAAA,QAAA,WAAM,SAAA,SAAN,OAAQ,qBAAoB,aAAAA,QAAA,cAAC,SAAQ,EAAA,kBAAkB,OAAO,iBAAgB,CAAI;IAAA;;AAI1F;AAGD,IAAM,2BAAuB,yBAC3B,CAAC,OAA6C,QAAO;AACnD,QAAM,MAAM,aAAAA,QAAM,QAAQ,MAAK;AAC7B,WAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAU,EAAE,SAAQ;EAExD,GAAG,CAAC,MAAM,MAAM,CAAC;AAGjB,SAAO,aAAAA,QAAM,cAAc,mBAAmB;IAC5C;IACA,UAAU;IACV,GAAG;EACJ,CAAA;AACH,CAAC;AAGU,IAAA,gBAAgB,aAAAA,QAAM,KAAK,oBAAoB;AC9N5D,IAAA,QAAiB,SAAS,MAAM,GAAG,GAAG;AACpC,MAAI,MAAM,EAAG,QAAO;AAEpB,MAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,QAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,QAAI,QAAQ,GAAG;AACf,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,WAAK,IAAI,QAAQ,QAAQ;AACvB,YAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,aAAO;IACb;AAGI,QAAK,aAAa,OAAS,aAAa,KAAM;AAC5C,UAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,WAAK,KAAK,EAAE,QAAO;AACjB,YAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAG,QAAO;AAC3B,WAAK,KAAK,EAAE,QAAO;AACjB,YAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAG,QAAO;AACxC,aAAO;IACb;AAEI,QAAK,aAAa,OAAS,aAAa,KAAM;AAC5C,UAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,WAAK,KAAK,EAAE,QAAO;AACjB,YAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAG,QAAO;AAC3B,aAAO;IACb;AAEI,QAAI,YAAY,OAAO,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG;AAClD,eAAS,EAAE;AACX,UAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,WAAK,IAAI,QAAQ,QAAQ;AACvB,YAAI,EAAE,CAAC,MAAM,EAAE,CAAC,EAAG,QAAO;AAC5B,aAAO;IACb;AAGI,QAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,QAAI,EAAE,YAAY,OAAO,UAAU,QAAS,QAAO,EAAE,QAAO,MAAO,EAAE,QAAO;AAC5E,QAAI,EAAE,aAAa,OAAO,UAAU,SAAU,QAAO,EAAE,SAAQ,MAAO,EAAE,SAAQ;AAEhF,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,SAAK,IAAI,QAAQ,QAAQ;AACvB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAEhE,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,MAAM,KAAK,CAAC;AAEhB,UAAI,QAAQ,YAAY,EAAE,UAAU;AAIlC;MACR;AAEM,UAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;IACzC;AAEI,WAAO;EACX;AAGE,SAAO,MAAI,KAAK,MAAI;AACtB;;;;;;;;AElEA,MAAI,MAAuC;AACzC,KAAC,WAAW;AAKd,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,uCAA+B,4BAA4B,IAAI,MAAK,CAAE;;AAE9D,UAAIY,UAAQC,aAAAA;AACtB,UAAIC,QAAOC;AAMX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAO,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM;;AAIrE,UAAI,WAAW,OAAO,OAAO,OAAO,aAAa,OAAO,KAAK;AAE7D,UAAI,uBAAuBD,MAAK;AAIhC,UAAIE,UAASJ,QAAM,QACfK,aAAYL,QAAM,WAClB,UAAUA,QAAM,SAChBM,iBAAgBN,QAAM;AAE1B,eAAS,iCAAiC,WAAW,aAAa,mBAAmB,UAAU,SAAS;AAEtG,YAAI,UAAUI,QAAO,IAAI;AACzB,YAAI;AAEJ,YAAI,QAAQ,YAAY,MAAM;AAC5B,iBAAO;YACL,UAAU;YACV,OAAO;UACb;AACI,kBAAQ,UAAU;QACtB,OAAS;AACL,iBAAO,QAAQ;;AAGjB,YAAI,WAAW,QAAQ,WAAY;AAKjC,cAAI,UAAU;AACd,cAAI;AACJ,cAAI;AAEJ,cAAI,mBAAmB,SAAU,cAAc;AAC7C,gBAAI,CAAC,SAAS;AAEZ,wBAAU;AACV,iCAAmB;AAEnB,kBAAI,iBAAiB,SAAS,YAAY;AAE1C,kBAAI,YAAY,QAAW;AAIzB,oBAAI,KAAK,UAAU;AACjB,sBAAI,mBAAmB,KAAK;AAE5B,sBAAI,QAAQ,kBAAkB,cAAc,GAAG;AAC7C,wCAAoB;AACpB,2BAAO;;;;AAKb,kCAAoB;AACpB,qBAAO;;AAKT,gBAAI,eAAe;AACnB,gBAAI,gBAAgB;AAEpB,gBAAI,SAAS,cAAc,YAAY,GAAG;AAExC,qBAAO;;AAKT,gBAAI,gBAAgB,SAAS,YAAY;AASzC,gBAAI,YAAY,UAAa,QAAQ,eAAe,aAAa,GAAG;AAClE,qBAAO;;AAGT,+BAAmB;AACnB,gCAAoB;AACpB,mBAAO;UACb;AAII,cAAI,yBAAyB,sBAAsB,SAAY,OAAO;AAEtE,cAAI,0BAA0B,WAAY;AACxC,mBAAO,iBAAiB,YAAW,CAAE;UAC3C;AAEI,cAAI,gCAAgC,2BAA2B,OAAO,SAAY,WAAY;AAC5F,mBAAO,iBAAiB,uBAAsB,CAAE;UACtD;AACI,iBAAO,CAAC,yBAAyB,6BAA6B;WAC7D,CAAC,aAAa,mBAAmB,UAAU,OAAO,CAAC,GAClD,eAAe,SAAS,CAAC,GACzB,qBAAqB,SAAS,CAAC;AAEnC,YAAI,QAAQ,qBAAqB,WAAW,cAAc,kBAAkB;AAC5E,QAAAC,WAAU,WAAY;AACpB,eAAK,WAAW;AAChB,eAAK,QAAQ;QACjB,GAAK,CAAC,KAAK,CAAC;AACV,QAAAC,eAAc,KAAK;AACnB,eAAO;;AAG+B,+BAAA,mCAAG;AAE3C,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,uCAA+B,2BAA2B,IAAI,MAAK,CAAE;;IAGvE,GAAG;EACH;;;AClKA,IAAI,OAAuC;AACzCC,eAAA,UAAiBN,mCAAA;AACnB,OAAO;AACLM,eAAA,UAAiBJ,gCAAA;AACnB;;ACCA,IAAM,4BAA4B,OAAO,WAAW,cAAc,+BAAkB;AA8BpF,IAAM,qBAAN,MAAwB;EAWtB,YAAY,eAAsB;AAV1B,SAAiB,oBAAG;AAEpB,SAAqB,wBAAG;AAMxB,SAAA,cAAc,oBAAI,IAAG;AAG3B,SAAK,SAAS;AACd,SAAK,eAAe,EAAE,QAAQ,eAAe,mBAAmB,EAAC;AAEjE,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;;;;;EAM3C,cAAW;AACT,QAAI,KAAK,sBAAsB,KAAK,uBAAuB;AACzD,aAAO,KAAK;;AAEd,SAAK,wBAAwB,KAAK;AAClC,SAAK,eAAe,EAAE,QAAQ,KAAK,QAAQ,mBAAmB,KAAK,kBAAiB;AACpF,WAAO,KAAK;;;;;EAMd,oBAAiB;AACf,WAAO,EAAE,QAAQ,MAAM,mBAAmB,EAAC;;;;;EAM7C,UAAU,UAAoB;AAC5B,SAAK,YAAY,IAAI,QAAQ;AAC7B,WAAO,MAAK;AACV,WAAK,YAAY,OAAO,QAAQ;IAClC;;;;;EAMF,MAAM,YAAyB;AAC7B,SAAK,SAAS;AAEd,QAAI,KAAK,QAAQ;AAMf,YAAMK,MAAK,MAAK;AACd,aAAK,qBAAqB;AAC1B,aAAK,YAAY,QAAQ,cAAY,SAAQ,CAAE;MACjD;AAEA,YAAM,gBAAgB,KAAK;AAE3B,oBAAc,GAAG,eAAeA,GAAE;AAClC,aAAO,MAAK;AACV,sBAAc,IAAI,eAAeA,GAAE;MACrC;;AAGF,WAAO;;AAEV;AA0CK,SAAU,eACd,SAA+G;;AAE/G,QAAM,CAAC,kBAAkB,QAAI,uBAAS,MAAM,IAAI,mBAAmB,QAAQ,MAAM,CAAC;AAGlF,QAAM,gBAAgBC,oBAAAA,iCACpB,mBAAmB,WACnB,mBAAmB,aACnB,mBAAmB,mBACnB,QAAQ,WACR,KAAA,QAAQ,gBAAU,QAAA,OAAA,SAAA,KAAI,SAAS;AAGjC,4BAA0B,MAAK;AAC7B,WAAO,mBAAmB,MAAM,QAAQ,MAAM;KAC7C,CAAC,QAAQ,QAAQ,kBAAkB,CAAC;AAEvC,kCAAc,aAAa;AAE3B,SAAO;AACT;ACpKA,IAAM,QAAQ;AACd,IAAM,QAAQ,OAAO,WAAW;AAChC,IAAM,SAAS,SAAS,QAAQ,OAAO,WAAW,eAAgB,OAAe,IAAI;AAwBrF,IAAM,wBAAN,MAAM,uBAAqB;EAqCzB,YAAY,SAA2C;AAjC/C,SAAM,SAAkB;AAWxB,SAAA,gBAAgB,oBAAI,IAAG;AAUvB,SAAkB,qBAAG;AAKrB,SAAY,eAA0B;AAKvC,SAAU,aAAG;AAGlB,SAAK,UAAU;AACf,SAAK,gBAAgB,oBAAI,IAAG;AAC5B,SAAK,UAAU,KAAK,iBAAgB,CAAE;AACtC,SAAK,gBAAe;AAEpB,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AACjE,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;;EAGzC,UAAU,QAAqB;AACrC,SAAK,SAAS;AACd,SAAK,aAAa,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC;AAGvD,SAAK,cAAc,QAAQ,QAAM,GAAE,CAAE;;EAG/B,mBAAgB;AACtB,QAAI,KAAK,QAAQ,QAAQ,sBAAsB,QAAW;AACxD,UAAI,SAAS,QAAQ;AAEnB,YAAI,OAAO;AAKT,kBAAQ,KACN,0HAA0H;;AAK9H,eAAO;;AAIT,aAAO,KAAK,aAAY;;AAG1B,QAAI,KAAK,QAAQ,QAAQ,qBAAqB,SAAS,OAAO;AAE5D,YAAM,IAAI,MACR,kOAAkO;;AAItO,QAAI,KAAK,QAAQ,QAAQ,mBAAmB;AAC1C,aAAO,KAAK,aAAY;;AAG1B,WAAO;;;;;EAMD,eAAY;AAClB,UAAM,iBAAyC;MAC7C,GAAG,KAAK,QAAQ;;MAEhB,gBAAgB,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,oBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC3E,QAAQ,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC3D,UAAU,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC/D,WAAW,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MACjE,SAAS,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC7D,mBAAmB,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MACjF,eAAe,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MACzE,UAAU,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC/D,gBAAgB,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,oBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC3E,QAAQ,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC3D,SAAS,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;;AAE/D,UAAM,SAAS,IAAI,OAAO,cAAc;AAIxC,WAAO;;;;;EAMT,YAAS;AACP,WAAO,KAAK;;;;;EAMd,oBAAiB;AACf,WAAO;;;;;EAMT,UAAU,eAAyB;AACjC,SAAK,cAAc,IAAI,aAAa;AAEpC,WAAO,MAAK;AACV,WAAK,cAAc,OAAO,aAAa;IACzC;;EAGF,OAAO,eAAe,GAAqB,GAAmB;AAC5D,WAAQ,OAAO,KAAK,CAAC,EAAiC,MAAM,SAAM;AAChE,UAAI,CAAC,YAAY,kBAAkB,aAAa,YAAY,iBAAiB,WAAW,UAAU,qBAAqB,kBAAkB,UAAU,SAAS,EAAE,SAAS,GAAG,GAAG;AAE3K,eAAO;;AAIT,UAAI,QAAQ,gBAAgB,EAAE,cAAc,EAAE,YAAY;AACxD,YAAI,EAAE,WAAW,WAAW,EAAE,WAAW,QAAQ;AAC/C,iBAAO;;AAET,eAAO,EAAE,WAAW,MAAM,CAAC,WAAWC,WAAS;;AAC7C,cAAI,gBAAc,KAAA,EAAE,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAGA,MAAK,IAAG;AACvC,mBAAO;;AAET,iBAAO;QACT,CAAC;;AAEH,UAAI,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AAErB,eAAO;;AAET,aAAO;IACT,CAAC;;;;;;;EAQH,SAAS,MAAoB;AAE3B,WAAO,MAAK;AACV,WAAK,qBAAqB;AAE1B,mBAAa,KAAK,2BAA2B;AAE7C,UAAI,KAAK,UAAU,CAAC,KAAK,OAAO,eAAe,KAAK,WAAW,GAAG;AAEhE,YAAI,CAAC,uBAAsB,eAAe,KAAK,QAAQ,SAAS,KAAK,OAAO,OAAO,GAAG;AAGpF,eAAK,OAAO,WAAW;YACrB,GAAG,KAAK,QAAQ;YAChB,UAAU,KAAK,OAAO;UACvB,CAAA;;aAEE;AAML,aAAK,sBAAsB,IAAI;;AAGjC,aAAO,MAAK;AACV,aAAK,qBAAqB;AAC1B,aAAK,gBAAe;MACtB;IACF;;;;;EAMM,sBAAsB,MAAoB;AAChD,QAAI,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa;AAE3C,UAAI,KAAK,iBAAiB,MAAM;AAE9B,aAAK,eAAe;AACpB;;AAEF,YAAM,eAAe,KAAK,aAAa,WAAW,KAAK,UAClD,KAAK,aAAa,MAAM,CAAC,KAAKA,WAAU,QAAQ,KAAKA,MAAK,CAAC;AAEhE,UAAI,cAAc;AAEhB;;;AAIJ,QAAI,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa;AAE3C,WAAK,OAAO,QAAO;;AAGrB,SAAK,UAAU,KAAK,aAAY,CAAE;AAGlC,SAAK,eAAe;;;;;;;EAQd,kBAAe;AACrB,UAAM,oBAAoB,KAAK;AAC/B,UAAM,gBAAgB,KAAK;AAG3B,SAAK,8BAA8B,WAAW,MAAK;AACjD,UAAI,KAAK,sBAAsB,KAAK,eAAe,mBAAmB;AAEpE,YAAI,eAAe;AAEjB,wBAAc,WAAW,KAAK,QAAQ,OAAO;;AAE/C;;AAEF,UAAI,iBAAiB,CAAC,cAAc,aAAa;AAC/C,sBAAc,QAAO;AACrB,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,UAAU,IAAI;;;OAKtB,CAAC;;AAEP;SAuBe,UACd,UAA4B,CAAA,GAC5B,OAAuB,CAAA,GAAE;AAEzB,QAAM,wBAAoB,qBAAO,OAAO;AAExC,oBAAkB,UAAU;AAE5B,QAAM,CAAC,eAAe,QAAI,uBAAS,MAAM,IAAI,sBAAsB,iBAAiB,CAAC;AAErF,QAAM,SAASC,YAAAA,qBACb,gBAAgB,WAChB,gBAAgB,WAChB,gBAAgB,iBAAiB;AAGnC,kCAAc,MAAM;AAIpB,8BAAU,gBAAgB,SAAS,IAAI,CAAC;AAIxC,iBAAe;IACb;IACA,UAAU,CAAC,EAAE,kBAAiB,MAAM;AAClC,UAAI,QAAQ,gCAAgC,OAAO;AAEjD,eAAO;;AAIT,UAAI,QAAQ,qBAAqB,sBAAsB,GAAG;AACxD,eAAO;;AAET,aAAO,oBAAoB;;EAE9B,CAAA;AAED,SAAO;AACT;AC3WO,IAAM,oBAAgB,4BAAkC;EAC7D,QAAQ;AACT,CAAA;AAEY,IAAA,iBAAiB,cAAc;AAK/B,IAAA,mBAAmB,UAAM,yBAAW,aAAa;SAc9C,eAAe,EAC7B,UAAU,WAAW,YAAY,uBAAuB,CAAA,GAAI,GAAG,cAAa,GACxD;AACpB,QAAM,SAAS,UAAU,aAAa;AAEtC,MAAI,CAAC,QAAQ;AACX,WAAO;;AAGT,SACE,aAAAX,QAAC;IAAA,cAAc;IAAQ,EAAC,OAAO,EAAE,OAAM,EAAE;IACtC;IACD,aAAAA,QAAC,cAAA,gBAAc,MACZ,CAAC,EAAE,QAAQ,cAAa,MACvB,aAAAA,QAAC,cAAA,eAAa,EAAC,QAAQ,eAAmB,GAAA,qBAAoB,CAAI,CACnE;IAEF;IACA;EAAS;AAGhB;AC1Ca,IAAAY,cAAa,CAAC,UAA0B;AACnD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAgC,IAAI;AAClE,QAAM,EAAE,QAAQ,cAAa,IAAK,iBAAgB;AAElD,8BAAU,MAAK;;AACb,QAAI,CAAC,SAAS;AACZ;;AAGF,UAAI,KAAA,MAAM,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAe,kBAAa,QAAb,kBAAA,SAAA,SAAA,cAAe,cAAa;AAC3D;;AAGF,UAAM,EACJ,YAAY,cAAc,QAAQ,eAAe,CAAA,GAAI,aAAa,aAAa,KAAI,IACjF;AAEJ,UAAM,aAAa,UAAU;AAE7B,QAAI,CAAC,YAAY;AACf,cAAQ,KAAK,kGAAkG;AAC/G;;AAGF,UAAM,SAAS,iBAAiB;MAC9B;MACA,QAAQ;MACR;MACA;MACA;MACA;IACD,CAAA;AAED,eAAW,eAAe,MAAM;AAChC,WAAO,MAAQ;AAAA,iBAAW,iBAAiB,SAAS;IAAC;KACpD,CAAC,MAAM,QAAQ,eAAe,OAAO,CAAC;AAEzC,SACE,aAAAZ,QAAK,cAAA,OAAA,EAAA,KAAK,YAAY,WAAW,MAAM,WAAW,OAAO,EAAE,YAAY,SAAQ,EAAE,GAC9E,MAAM,QAAQ;AAGrB;ACzCa,IAAAa,gBAAe,CAAC,UAA4B;AACvD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAgC,IAAI;AAClE,QAAM,EAAE,QAAQ,cAAa,IAAK,iBAAgB;AAElD,8BAAU,MAAK;;AACb,QAAI,CAAC,SAAS;AACZ;;AAGF,UAAI,KAAA,MAAM,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAe,kBAAa,QAAb,kBAAA,SAAA,SAAA,cAAe,cAAa;AAC3D;;AAGF,UAAM,EACJ,YAAY,gBACZ,QACA,eAAe,CAAA,GACf,aAAa,KAAI,IACf;AAEJ,UAAM,aAAa,UAAU;AAE7B,QAAI,CAAC,YAAY;AACf,cAAQ,KAAK,oGAAoG;AACjH;;AAGF,UAAM,SAAS,mBAAmB;MAChC;MACA,QAAQ;MACR;MACA;MACA;IACD,CAAA;AAED,eAAW,eAAe,MAAM;AAChC,WAAO,MAAQ;AAAA,iBAAW,iBAAiB,SAAS;IAAC;EACvD,GAAG;IACD,MAAM;IACN;IACA;EACD,CAAA;AAED,SACE,aAAAb,QAAK,cAAA,OAAA,EAAA,KAAK,YAAY,WAAW,MAAM,WAAW,OAAO,EAAE,YAAY,SAAQ,EAAE,GAC9E,MAAM,QAAQ;AAGrB;ACxDO,IAAM,2BAAuB,4BAAkD;EACpF,aAAa;AACd,CAAA;AAEY,IAAA,mBAAmB,UAAM,yBAAW,oBAAoB;ACFxD,IAAA,kBAAkD,WAAQ;AACrE,QAAM,MAAM,MAAM,MAAM;AACxB,QAAM,EAAE,mBAAkB,IAAK,iBAAgB;AAE/C;;IAEE,aAAAA,QAAC,cAAA,KACK,EAAA,GAAA,OACJ,KAAK,oBACkB,0BAAA,IACvB,OAAO;MACL,YAAY;MACZ,GAAG,MAAM;IACV,EAAA,CAAA;;AAGP;AChBO,IAAM,kBAAkD,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAO;AAC7F,QAAM,EAAE,YAAW,IAAK,iBAAgB;AACxC,QAAM,MAAM,MAAM,MAAM;AAExB;;IAEE,aAAAA,QAAA,cAAC,KAAG,EAAA,GACE,OACJ,KACuB,0BAAA,IACvB,aACA,OAAO;MACL,YAAY;MACZ,GAAG,MAAM;IACV,EAAA,CAAA;;AAGP,CAAC;ACfD,SAAS,iBAAiB,WAAc;AACtC,SAAO,CAAC,EACN,OAAO,cAAc,cAClB,UAAU,aACV,UAAU,UAAU;AAE3B;AAOA,SAAS,sBAAsB,WAAc;;AAC3C,SAAO,CAAC,EACN,OAAO,cAAc,cAClB,KAAA,UAAU,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ,OAAO;AAE1C;IAgDa,sBAAa;;;;EAkBxB,YAAY,WAAgC,EAC1C,QACA,QAAQ,CAAA,GACR,KAAK,OACL,YAAY,GAAE,GACO;AAVvB,SAAG,MAAa;AAWd,SAAK,KAAK,KAAK,MAAM,KAAK,OAAM,IAAK,UAAU,EAAE,SAAQ;AACzD,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,UAAU,SAAS,cAAc,EAAE;AACxC,SAAK,QAAQ,UAAU,IAAI,gBAAgB;AAE3C,QAAI,WAAW;AACb,WAAK,QAAQ,UAAU,IAAI,GAAG,UAAU,MAAM,GAAG,CAAC;;AAGpD,QAAI,KAAK,OAAO,eAAe;AAG7B,sCAAU,MAAK;AACb,aAAK,OAAM;MACb,CAAC;WACI;AACL,WAAK,OAAM;;;;;;EAOf,SAAM;;AACJ,UAAM,YAAY,KAAK;AACvB,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK;AAEpB,QAAI,iBAAiB,SAAS,KAAK,sBAAsB,SAAS,GAAG;AAEnE,YAAM,MAAM,CAAC,QAAU;AACrB,aAAK,MAAM;MACb;;AAGF,SAAK,eAAe,aAAAA,QAAA,cAAC,WAAc,EAAA,GAAA,MAAK,CAAA;AAExC,KAAA,KAAA,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,YAAY,KAAK,IAAI,IAAI;;;;;EAMrD,YAAY,QAA6B,CAAA,GAAE;AACzC,SAAK,QAAQ;MACX,GAAG,KAAK;MACR,GAAG;;AAGL,SAAK,OAAM;;;;;EAMb,UAAO;;AACL,UAAM,SAAS,KAAK;AAEpB,KAAA,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,sBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,eAAe,KAAK,EAAE;;;;;EAMlD,iBAAiB,YAAkC;AACjD,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAM;AACpC,WAAK,QAAQ,aAAa,KAAK,WAAW,GAAG,CAAC;IAChD,CAAC;;AAEJ;ACtHK,IAAO,gBAAP,cAII,SAAwC;;;;;EAehD,QAAK;AACH,UAAM,QAAQ;MACZ,QAAQ,KAAK;MACb,MAAM,KAAK;MACX,aAAa,KAAK;MAClB,kBAAkB,KAAK;MACvB,MAAM,KAAK;MACX,UAAU;MACV,WAAW,KAAK;MAChB,gBAAgB,KAAK;MACrB,QAAQ,MAAM,KAAK,OAAM;MACzB,kBAAkB,CAAC,aAAa,CAAA,MAAO,KAAK,iBAAiB,UAAU;MACvE,YAAY,MAAM,KAAK,WAAU;;AAGnC,QAAI,CAAE,KAAK,UAAkB,aAAa;AACxC,YAAM,sBAAsB,CAAC,WAA0B;AACrD,eAAO,OAAO,OAAO,CAAC,EAAE,YAAW,IAAK,OAAO,UAAU,CAAC;MAC5D;AAEA,WAAK,UAAU,cAAc,oBAAoB,KAAK,UAAU,IAAI;;AAGtE,UAAM,cAAc,KAAK,YAAY,KAAK,IAAI;AAC9C,UAAM,qBAAsE,aAAU;AACpF,UAAI,WAAW,KAAK,qBAAqB,QAAQ,eAAe,KAAK,mBAAmB;AACtF,gBAAQ,YAAY,KAAK,iBAAiB;;IAE9C;AACA,UAAM,UAAU,EAAE,aAAa,mBAAkB;AACjD,UAAM,YAAY,KAAK;AAGvB,UAAM,wBAAgE,aAAAA,QAAM,KAC1E,oBAAiB;AACf,aACE,aAAAA,QAAA,cAAC,qBAAqB,UAAS,EAAA,OAAO,QAAO,GAC1C,aAAAA,QAAM,cAAc,WAAW,cAAc,CAAC;IAGrD,CAAC;AAGH,0BAAsB,cAAc;AAEpC,QAAI,KAAK,KAAK,QAAQ;AACpB,WAAK,oBAAoB;eAChB,KAAK,QAAQ,sBAAsB;AAC5C,WAAK,oBAAoB,SAAS,cAAc,KAAK,QAAQ,oBAAoB;WAC5E;AACL,WAAK,oBAAoB,SAAS,cAAc,KAAK,KAAK,WAAW,SAAS,KAAK;;AAGrF,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,QAAQ,uBAAuB;AAItD,WAAK,kBAAkB,MAAM,aAAa;;AAG5C,QAAI,KAAK,KAAK,KAAK,WAAW,SAAS;AAEvC,QAAI,KAAK,QAAQ,IAAI;AACnB,WAAK,KAAK,QAAQ;;AAGpB,UAAM,EAAE,YAAY,GAAE,IAAK,KAAK;AAEhC,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AAEjE,SAAK,WAAW,IAAI,cAAc,uBAAuB;MACvD,QAAQ,KAAK;MACb;MACA;MACA,WAAW,QAAQ,KAAK,KAAK,KAAK,IAAI,IAAI,SAAS,GAAG,KAAI;IAC3D,CAAA;AAED,SAAK,OAAO,GAAG,mBAAmB,KAAK,qBAAqB;AAC5D,SAAK,wBAAuB;;;;;;EAO9B,IAAI,MAAG;;AACL,QACE,KAAK,SAAS,QAAQ,qBACnB,GAAC,KAAA,KAAK,SAAS,QAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAE,aAAa,wBAAwB,IAClF;AACA,YAAM,MAAM,8DAA8D;;AAG5E,WAAO,KAAK,SAAS;;;;;;EAOvB,IAAI,aAAU;AACZ,QAAI,KAAK,KAAK,QAAQ;AACpB,aAAO;;AAGT,WAAO,KAAK;;;;;;EAOd,wBAAqB;AACnB,UAAM,EAAE,MAAM,GAAE,IAAK,KAAK,OAAO,MAAM;AACvC,UAAM,MAAM,KAAK,OAAM;AAEvB,QAAI,OAAO,QAAQ,UAAU;AAC3B;;AAGF,QAAI,QAAQ,OAAO,MAAM,MAAM,KAAK,KAAK,UAAU;AACjD,UAAI,KAAK,SAAS,MAAM,UAAU;AAChC;;AAGF,WAAK,WAAU;WACV;AACL,UAAI,CAAC,KAAK,SAAS,MAAM,UAAU;AACjC;;AAGF,WAAK,aAAY;;;;;;;EAQrB,OACE,MACA,aACA,kBAAkC;AAElC,UAAM,oBAAoB,CAAC,UAA+B;AACxD,WAAK,SAAS,YAAY,KAAK;AAC/B,UAAI,OAAO,KAAK,QAAQ,UAAU,YAAY;AAC5C,aAAK,wBAAuB;;IAEhC;AAEA,QAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AAChC,aAAO;;AAGT,QAAI,OAAO,KAAK,QAAQ,WAAW,YAAY;AAC7C,YAAM,UAAU,KAAK;AACrB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,sBAAsB,KAAK;AAEjC,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,mBAAmB;AAExB,aAAO,KAAK,QAAQ,OAAO;QACzB;QACA;QACA,SAAS;QACT,gBAAgB;QAChB;QACA;QACA,aAAa,MAAM,kBAAkB,EAAE,MAAM,aAAa,iBAAgB,CAAE;MAC7E,CAAA;;AAGH,QACE,SAAS,KAAK,QACX,KAAK,gBAAgB,eACrB,KAAK,qBAAqB,kBAC7B;AACA,aAAO;;AAGT,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAExB,sBAAkB,EAAE,MAAM,aAAa,iBAAgB,CAAE;AAEzD,WAAO;;;;;;EAOT,aAAU;AACR,SAAK,SAAS,YAAY;MACxB,UAAU;IACX,CAAA;AACD,SAAK,SAAS,QAAQ,UAAU,IAAI,0BAA0B;;;;;;EAOhE,eAAY;AACV,SAAK,SAAS,YAAY;MACxB,UAAU;IACX,CAAA;AACD,SAAK,SAAS,QAAQ,UAAU,OAAO,0BAA0B;;;;;EAMnE,UAAO;AACL,SAAK,SAAS,QAAO;AACrB,SAAK,OAAO,IAAI,mBAAmB,KAAK,qBAAqB;AAC7D,SAAK,oBAAoB;;;;;;EAO3B,0BAAuB;AACrB,QAAI,KAAK,QAAQ,OAAO;AACtB,UAAI,WAAmC,CAAA;AAEvC,UAAI,OAAO,KAAK,QAAQ,UAAU,YAAY;AAC5C,cAAM,sBAAsB,KAAK,OAAO,iBAAiB;AACzD,cAAM,iBAAiB,sBAAsB,KAAK,MAAM,mBAAmB;AAE3E,mBAAW,KAAK,QAAQ,MAAM,EAAE,MAAM,KAAK,MAAM,eAAc,CAAE;aAC5D;AACL,mBAAW,KAAK,QAAQ;;AAG1B,WAAK,SAAS,iBAAiB,QAAQ;;;AAG5C;AAKe,SAAA,sBACd,WACA,SAA+C;AAE/C,SAAO,WAAQ;AAIb,QAAI,CAAE,MAAM,OAAsC,kBAAkB;AAClE,aAAO,CAAA;;AAGT,WAAO,IAAI,cAAc,WAAW,OAAO,OAAO;EACpD;AACF;", "names": ["name", "style", "window", "min", "max", "toPaddingObject", "popperOffsets", "min", "max", "offset", "effect", "popper", "effect", "window", "hash", "clippingParents", "reference", "popperOffsets", "offset", "placements", "placement", "placements", "placement", "_loop", "_i", "checks", "offset", "popperOffsets", "offset", "min", "max", "fn", "merged", "defaultModifiers", "createPopper", "reference", "popper", "options", "index", "fn", "state", "effect", "noopFn", "createPopper", "defaultModifiers", "createPopper", "BOX_CLASS", "CONTENT_CLASS", "BACKDROP_CLASS", "ARROW_CLASS", "SVG_ARROW_CLASS", "TOUCH_OPTIONS", "passive", "capture", "TIPPY_DEFAULT_APPEND_TO", "document", "body", "hasOwnProperty", "obj", "key", "call", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "timeout", "arg", "clearTimeout", "setTimeout", "removeProperties", "keys", "clone", "for<PERSON>ach", "splitBySpaces", "split", "filter", "Boolean", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "unique", "item", "getBasePlacement", "placement", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "createElement", "isElement", "some", "isNodeList", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "querySelectorAll", "setTransitionDuration", "els", "el", "style", "transitionDuration", "setVisibilityState", "state", "setAttribute", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "isCursorOutsideInteractiveBorder", "popperTreeData", "event", "clientX", "clientY", "every", "popperRect", "popperState", "props", "interactiveBorder", "basePlacement", "offsetData", "modifiersData", "offset", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "updateTransitionEndListener", "box", "action", "listener", "method", "actualContains", "parent", "child", "target", "contains", "getRootNode", "host", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "window", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "bindGlobalEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "isIE11", "msCrypto", "createMemoryLeakWarning", "txt", "join", "clean", "spacesAndTabs", "lineStartWithSpaces", "replace", "trim", "getDevMessage", "message", "getFormattedMessage", "visitedMessages", "resetVisitedMessages", "Set", "warn<PERSON><PERSON>", "condition", "has", "add", "console", "warn", "<PERSON><PERSON><PERSON>", "error", "validateTargets", "targets", "didPassFalsyValue", "didPassPlainObject", "prototype", "String", "pluginProps", "animateFill", "followCursor", "inlinePositioning", "sticky", "renderProps", "allowHTML", "animation", "arrow", "content", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultProps", "appendTo", "aria", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveDebounce", "moveTransition", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "defaultKeys", "setDefaultProps", "partialProps", "validateProps", "getExtendedPassedProps", "passedProps", "plugin", "name", "getDataAttributeProps", "propKeys", "valueAsString", "getAttribute", "JSON", "parse", "e", "evaluateProps", "out", "prop", "nonPluginProps", "didPassUnknownProp", "length", "innerHTML", "dangerouslySetInnerHTML", "html", "createArrowElement", "className", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "backdrop", "onUpdate", "prevProps", "nextProps", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "$$tippy", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "currentTarget", "id", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "setProps", "show", "hide", "hideWithInteractivity", "enable", "disable", "unmount", "destroy", "pluginsHooks", "map", "hasAriaExpanded", "hasAttribute", "addListeners", "handleAriaExpandedAttribute", "handleStyles", "invokeHook", "scheduleShow", "getDocument", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "getIsDefaultRenderFn", "getC<PERSON>rentTarget", "parentNode", "getDefaultTemplateChildren", "get<PERSON>elay", "isShow", "fromHide", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "handleAriaContentAttribute", "attr", "nodes", "currentValue", "nextValue", "cleanupInteractiveMouseListeners", "onDocumentPress", "actual<PERSON>arget", "<PERSON><PERSON><PERSON>", "removeDocumentPress", "onTouchMove", "onTouchStart", "addDocumentPress", "doc", "onTransitionedOut", "callback", "onTransitionEnd", "onTransitionedIn", "on", "eventType", "handler", "options", "onMouseLeave", "onBlurOrFocusOut", "removeListeners", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "getNestedPopperTree", "getBoundingClientRect", "shouldBail", "relatedTarget", "createPopperInstance", "destroyPopperInstance", "computedReference", "contextElement", "tippyModifier", "enabled", "phase", "requires", "attributes", "modifiers", "padding", "adaptive", "createPopper", "mount", "nextElement<PERSON><PERSON>ling", "touchValue", "touchDelay", "requestAnimationFrame", "cancelAnimationFrame", "nestedPopper", "forceUpdate", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "visibility", "transition", "offsetHeight", "isAlreadyHidden", "i", "tippy", "optionalProps", "elements", "isSingleContentElement", "isMoreThanOneReferenceElement", "instances", "applyStylesModifier", "applyStyles", "effect", "state", "initialStyles", "popper", "position", "options", "strategy", "left", "top", "margin", "arrow", "reference", "Object", "assign", "elements", "style", "styles", "tippy", "setDefaultProps", "render", "view", "shouldShow", "view", "React", "require$$0", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "error", "shim", "shimModule", "require$$1", "useSyncExternalStore", "ReactDOM", "React", "require$$0", "shim", "require$$1", "useRef", "useEffect", "useDebugValue", "withSelectorModule", "fn", "useSyncExternalStoreWithSelector", "index", "useSyncExternalStore", "BubbleMenu", "FloatingMenu"]}