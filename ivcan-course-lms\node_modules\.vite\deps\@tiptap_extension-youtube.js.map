{"version": 3, "sources": ["../../@tiptap/extension-youtube/src/utils.ts", "../../@tiptap/extension-youtube/src/youtube.ts"], "sourcesContent": ["export const YOUTUBE_REGEX = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/\nexport const YOUTUBE_REGEX_GLOBAL = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/g\n\nexport const isValidYoutubeUrl = (url: string) => {\n  return url.match(YOUTUBE_REGEX)\n}\n\nexport interface GetEmbedUrlOptions {\n  url: string;\n  allowFullscreen?: boolean;\n  autoplay?: boolean;\n  ccLanguage?:string;\n  ccLoadPolicy?:boolean;\n  controls?: boolean;\n  disableKBcontrols?: boolean,\n  enableIFrameApi?: boolean;\n  endTime?: number;\n  interfaceLanguage?: string;\n  ivLoadPolicy?: number;\n  loop?: boolean;\n  modestBranding?: boolean;\n  nocookie?: boolean;\n  origin?: string;\n  playlist?: string;\n  progressBarColor?: string;\n  startAt?: number;\n  rel?: number;\n}\n\nexport const getYoutubeEmbedUrl = (nocookie?: boolean, isPlaylist?:boolean) => {\n  if (isPlaylist) {\n    return 'https://www.youtube-nocookie.com/embed/videoseries?list='\n  }\n  return nocookie ? 'https://www.youtube-nocookie.com/embed/' : 'https://www.youtube.com/embed/'\n}\n\nexport const getEmbedUrlFromYoutubeUrl = (options: GetEmbedUrlOptions) => {\n  const {\n    url,\n    allowFullscreen,\n    autoplay,\n    ccLanguage,\n    ccLoadPolicy,\n    controls,\n    disableKBcontrols,\n    enableIFrameApi,\n    endTime,\n    interfaceLanguage,\n    ivLoadPolicy,\n    loop,\n    modestBranding,\n    nocookie,\n    origin,\n    playlist,\n    progressBarColor,\n    startAt,\n    rel,\n  } = options\n\n  if (!isValidYoutubeUrl(url)) {\n    return null\n  }\n\n  // if is already an embed url, return it\n  if (url.includes('/embed/')) {\n    return url\n  }\n\n  // if is a youtu.be url, get the id after the /\n  if (url.includes('youtu.be')) {\n    const id = url.split('/').pop()\n\n    if (!id) {\n      return null\n    }\n    return `${getYoutubeEmbedUrl(nocookie)}${id}`\n  }\n\n  const videoIdRegex = /(?:(v|list)=|shorts\\/)([-\\w]+)/gm\n  const matches = videoIdRegex.exec(url)\n\n  if (!matches || !matches[2]) {\n    return null\n  }\n\n  let outputUrl = `${getYoutubeEmbedUrl(nocookie, matches[1] === 'list')}${matches[2]}`\n\n  const params = []\n\n  if (allowFullscreen === false) {\n    params.push('fs=0')\n  }\n\n  if (autoplay) {\n    params.push('autoplay=1')\n  }\n\n  if (ccLanguage) {\n    params.push(`cc_lang_pref=${ccLanguage}`)\n  }\n\n  if (ccLoadPolicy) {\n    params.push('cc_load_policy=1')\n  }\n\n  if (!controls) {\n    params.push('controls=0')\n  }\n\n  if (disableKBcontrols) {\n    params.push('disablekb=1')\n  }\n\n  if (enableIFrameApi) {\n    params.push('enablejsapi=1')\n  }\n\n  if (endTime) {\n    params.push(`end=${endTime}`)\n  }\n\n  if (interfaceLanguage) {\n    params.push(`hl=${interfaceLanguage}`)\n  }\n\n  if (ivLoadPolicy) {\n    params.push(`iv_load_policy=${ivLoadPolicy}`)\n  }\n\n  if (loop) {\n    params.push('loop=1')\n  }\n\n  if (modestBranding) {\n    params.push('modestbranding=1')\n  }\n\n  if (origin) {\n    params.push(`origin=${origin}`)\n  }\n\n  if (playlist) {\n    params.push(`playlist=${playlist}`)\n  }\n\n  if (startAt) {\n    params.push(`start=${startAt}`)\n  }\n\n  if (progressBarColor) {\n    params.push(`color=${progressBarColor}`)\n  }\n\n  if (rel !== undefined) {\n    params.push(`rel=${rel}`)\n  }\n\n  if (params.length) {\n    outputUrl += `${matches[1] === 'v' ? '?' : '&'}${params.join('&')}`\n  }\n\n  return outputUrl\n}\n", "import { mergeAttributes, Node, nodePasteRule } from '@tiptap/core'\n\nimport { getEmbedUrlFromYoutubeUrl, isValidYoutubeUrl, YOUTUBE_REGEX_GLOBAL } from './utils.js'\n\nexport interface YoutubeOptions {\n  /**\n   * Controls if the paste handler for youtube videos should be added.\n   * @default true\n   * @example false\n   */\n  addPasteHandler: boolean;\n\n  /**\n   * Controls if the youtube video should be allowed to go fullscreen.\n   * @default true\n   * @example false\n   */\n  allowFullscreen: boolean;\n\n  /**\n   * Controls if the youtube video should autoplay.\n   * @default false\n   * @example true\n   */\n  autoplay: boolean;\n\n  /**\n   * The language of the captions shown in the youtube video.\n   * @default undefined\n   * @example 'en'\n   */\n  ccLanguage?: string;\n\n  /**\n   * Controls if the captions should be shown in the youtube video.\n   * @default undefined\n   * @example true\n   */\n  ccLoadPolicy?: boolean;\n\n  /**\n   * Controls if the controls should be shown in the youtube video.\n   * @default true\n   * @example false\n   */\n  controls: boolean;\n\n  /**\n   * Controls if the keyboard controls should be disabled in the youtube video.\n   * @default false\n   * @example true\n   */\n  disableKBcontrols: boolean;\n\n  /**\n   * Controls if the iframe api should be enabled in the youtube video.\n   * @default false\n   * @example true\n   */\n  enableIFrameApi: boolean;\n\n  /**\n   * The end time of the youtube video.\n   * @default 0\n   * @example 120\n   */\n  endTime: number;\n\n  /**\n   * The height of the youtube video.\n   * @default 480\n   * @example 720\n   */\n  height: number;\n\n  /**\n   * The language of the youtube video.\n   * @default undefined\n   * @example 'en'\n   */\n  interfaceLanguage?: string;\n\n  /**\n   * Controls if the video annotations should be shown in the youtube video.\n   * @default 0\n   * @example 1\n   */\n  ivLoadPolicy: number;\n\n  /**\n   * Controls if the youtube video should loop.\n   * @default false\n   * @example true\n   */\n  loop: boolean;\n\n  /**\n   * Controls if the youtube video should show a small youtube logo.\n   * @default false\n   * @example true\n   */\n  modestBranding: boolean;\n\n  /**\n   * The HTML attributes for a youtube video node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>;\n\n  /**\n   * Controls if the youtube node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean;\n\n  /**\n   * Controls if the youtube video should be loaded from youtube-nocookie.com.\n   * @default false\n   * @example true\n   */\n  nocookie: boolean;\n\n  /**\n   * The origin of the youtube video.\n   * @default ''\n   * @example 'https://tiptap.dev'\n   */\n  origin: string;\n\n  /**\n   * The playlist of the youtube video.\n   * @default ''\n   * @example 'PLQg6GaokU5CwiVmsZ0dZm6VeIg0V5z1tK'\n   */\n  playlist: string;\n\n  /**\n   * The color of the youtube video progress bar.\n   * @default undefined\n   * @example 'red'\n   */\n  progressBarColor?: string;\n\n  /**\n   * The width of the youtube video.\n   * @default 640\n   * @example 1280\n   */\n  width: number;\n\n  /**\n   * Controls if the related youtube videos at the end are from the same channel.\n   * @default 1\n   * @example 0\n   */\n  rel: number;\n}\n\n/**\n * The options for setting a youtube video.\n */\ntype SetYoutubeVideoOptions = { src: string, width?: number, height?: number, start?: number }\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    youtube: {\n      /**\n       * Insert a youtube video\n       * @param options The youtube video attributes\n       * @example editor.commands.setYoutubeVideo({ src: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ' })\n       */\n      setYoutubeVideo: (options: SetYoutubeVideoOptions) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension adds support for youtube videos.\n * @see https://www.tiptap.dev/api/nodes/youtube\n */\nexport const Youtube = Node.create<YoutubeOptions>({\n  name: 'youtube',\n\n  addOptions() {\n    return {\n      addPasteHandler: true,\n      allowFullscreen: true,\n      autoplay: false,\n      ccLanguage: undefined,\n      ccLoadPolicy: undefined,\n      controls: true,\n      disableKBcontrols: false,\n      enableIFrameApi: false,\n      endTime: 0,\n      height: 480,\n      interfaceLanguage: undefined,\n      ivLoadPolicy: 0,\n      loop: false,\n      modestBranding: false,\n      HTMLAttributes: {},\n      inline: false,\n      nocookie: false,\n      origin: '',\n      playlist: '',\n      progressBarColor: undefined,\n      width: 640,\n      rel: 1,\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      start: {\n        default: 0,\n      },\n      width: {\n        default: this.options.width,\n      },\n      height: {\n        default: this.options.height,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'div[data-youtube-video] iframe',\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setYoutubeVideo: (options: SetYoutubeVideoOptions) => ({ commands }) => {\n        if (!isValidYoutubeUrl(options.src)) {\n          return false\n        }\n\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addPasteRules() {\n    if (!this.options.addPasteHandler) {\n      return []\n    }\n\n    return [\n      nodePasteRule({\n        find: YOUTUBE_REGEX_GLOBAL,\n        type: this.type,\n        getAttributes: match => {\n          return { src: match.input }\n        },\n      }),\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const embedUrl = getEmbedUrlFromYoutubeUrl({\n      url: HTMLAttributes.src,\n      allowFullscreen: this.options.allowFullscreen,\n      autoplay: this.options.autoplay,\n      ccLanguage: this.options.ccLanguage,\n      ccLoadPolicy: this.options.ccLoadPolicy,\n      controls: this.options.controls,\n      disableKBcontrols: this.options.disableKBcontrols,\n      enableIFrameApi: this.options.enableIFrameApi,\n      endTime: this.options.endTime,\n      interfaceLanguage: this.options.interfaceLanguage,\n      ivLoadPolicy: this.options.ivLoadPolicy,\n      loop: this.options.loop,\n      modestBranding: this.options.modestBranding,\n      nocookie: this.options.nocookie,\n      origin: this.options.origin,\n      playlist: this.options.playlist,\n      progressBarColor: this.options.progressBarColor,\n      startAt: HTMLAttributes.start || 0,\n      rel: this.options.rel,\n    })\n\n    HTMLAttributes.src = embedUrl\n\n    return [\n      'div',\n      { 'data-youtube-video': '' },\n      [\n        'iframe',\n        mergeAttributes(\n          this.options.HTMLAttributes,\n          {\n            width: this.options.width,\n            height: this.options.height,\n            allowfullscreen: this.options.allowFullscreen,\n            autoplay: this.options.autoplay,\n            ccLanguage: this.options.ccLanguage,\n            ccLoadPolicy: this.options.ccLoadPolicy,\n            disableKBcontrols: this.options.disableKBcontrols,\n            enableIFrameApi: this.options.enableIFrameApi,\n            endTime: this.options.endTime,\n            interfaceLanguage: this.options.interfaceLanguage,\n            ivLoadPolicy: this.options.ivLoadPolicy,\n            loop: this.options.loop,\n            modestBranding: this.options.modestBranding,\n            origin: this.options.origin,\n            playlist: this.options.playlist,\n            progressBarColor: this.options.progressBarColor,\n            rel: this.options.rel,\n          },\n          HTMLAttributes,\n        ),\n      ],\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;AAAO,IAAM,gBAAgB;AACtB,IAAM,uBAAuB;AAE7B,IAAM,oBAAoB,CAAC,QAAe;AAC/C,SAAO,IAAI,MAAM,aAAa;AAChC;AAwBO,IAAM,qBAAqB,CAAC,UAAoB,eAAuB;AAC5E,MAAI,YAAY;AACd,WAAO;;AAET,SAAO,WAAW,4CAA4C;AAChE;AAEO,IAAM,4BAA4B,CAAC,YAA+B;AACvE,QAAM,EACJ,KACA,iBACA,UACA,YACA,cACA,UACA,mBACA,iBACA,SACA,mBACA,cACA,MACA,gBACA,UACA,QACA,UACA,kBACA,SACA,IAAG,IACD;AAEJ,MAAI,CAAC,kBAAkB,GAAG,GAAG;AAC3B,WAAO;;AAIT,MAAI,IAAI,SAAS,SAAS,GAAG;AAC3B,WAAO;;AAIT,MAAI,IAAI,SAAS,UAAU,GAAG;AAC5B,UAAM,KAAK,IAAI,MAAM,GAAG,EAAE,IAAG;AAE7B,QAAI,CAAC,IAAI;AACP,aAAO;;AAET,WAAO,GAAG,mBAAmB,QAAQ,CAAC,GAAG,EAAE;;AAG7C,QAAM,eAAe;AACrB,QAAM,UAAU,aAAa,KAAK,GAAG;AAErC,MAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG;AAC3B,WAAO;;AAGT,MAAI,YAAY,GAAG,mBAAmB,UAAU,QAAQ,CAAC,MAAM,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC;AAEnF,QAAM,SAAS,CAAA;AAEf,MAAI,oBAAoB,OAAO;AAC7B,WAAO,KAAK,MAAM;;AAGpB,MAAI,UAAU;AACZ,WAAO,KAAK,YAAY;;AAG1B,MAAI,YAAY;AACd,WAAO,KAAK,gBAAgB,UAAU,EAAE;;AAG1C,MAAI,cAAc;AAChB,WAAO,KAAK,kBAAkB;;AAGhC,MAAI,CAAC,UAAU;AACb,WAAO,KAAK,YAAY;;AAG1B,MAAI,mBAAmB;AACrB,WAAO,KAAK,aAAa;;AAG3B,MAAI,iBAAiB;AACnB,WAAO,KAAK,eAAe;;AAG7B,MAAI,SAAS;AACX,WAAO,KAAK,OAAO,OAAO,EAAE;;AAG9B,MAAI,mBAAmB;AACrB,WAAO,KAAK,MAAM,iBAAiB,EAAE;;AAGvC,MAAI,cAAc;AAChB,WAAO,KAAK,kBAAkB,YAAY,EAAE;;AAG9C,MAAI,MAAM;AACR,WAAO,KAAK,QAAQ;;AAGtB,MAAI,gBAAgB;AAClB,WAAO,KAAK,kBAAkB;;AAGhC,MAAI,QAAQ;AACV,WAAO,KAAK,UAAU,MAAM,EAAE;;AAGhC,MAAI,UAAU;AACZ,WAAO,KAAK,YAAY,QAAQ,EAAE;;AAGpC,MAAI,SAAS;AACX,WAAO,KAAK,SAAS,OAAO,EAAE;;AAGhC,MAAI,kBAAkB;AACpB,WAAO,KAAK,SAAS,gBAAgB,EAAE;;AAGzC,MAAI,QAAQ,QAAW;AACrB,WAAO,KAAK,OAAO,GAAG,EAAE;;AAG1B,MAAI,OAAO,QAAQ;AACjB,iBAAa,GAAG,QAAQ,CAAC,MAAM,MAAM,MAAM,GAAG,GAAG,OAAO,KAAK,GAAG,CAAC;;AAGnE,SAAO;AACT;ACoBa,IAAA,UAAU,KAAK,OAAuB;EACjD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,iBAAiB;MACjB,iBAAiB;MACjB,UAAU;MACV,YAAY;MACZ,cAAc;MACd,UAAU;MACV,mBAAmB;MACnB,iBAAiB;MACjB,SAAS;MACT,QAAQ;MACR,mBAAmB;MACnB,cAAc;MACd,MAAM;MACN,gBAAgB;MAChB,gBAAgB,CAAA;MAChB,QAAQ;MACR,UAAU;MACV,QAAQ;MACR,UAAU;MACV,kBAAkB;MAClB,OAAO;MACP,KAAK;;;EAIT,SAAM;AACJ,WAAO,KAAK,QAAQ;;EAGtB,QAAK;AACH,WAAO,KAAK,QAAQ,SAAS,WAAW;;EAG1C,WAAW;EAEX,gBAAa;AACX,WAAO;MACL,KAAK;QACH,SAAS;MACV;MACD,OAAO;QACL,SAAS;MACV;MACD,OAAO;QACL,SAAS,KAAK,QAAQ;MACvB;MACD,QAAQ;QACN,SAAS,KAAK,QAAQ;MACvB;;;EAIL,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;;;EAIL,cAAW;AACT,WAAO;MACL,iBAAiB,CAAC,YAAoC,CAAC,EAAE,SAAQ,MAAM;AACrE,YAAI,CAAC,kBAAkB,QAAQ,GAAG,GAAG;AACnC,iBAAO;;AAGT,eAAO,SAAS,cAAc;UAC5B,MAAM,KAAK;UACX,OAAO;QACR,CAAA;;;;EAKP,gBAAa;AACX,QAAI,CAAC,KAAK,QAAQ,iBAAiB;AACjC,aAAO,CAAA;;AAGT,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;QACX,eAAe,WAAQ;AACrB,iBAAO,EAAE,KAAK,MAAM,MAAK;;OAE5B;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,UAAM,WAAW,0BAA0B;MACzC,KAAK,eAAe;MACpB,iBAAiB,KAAK,QAAQ;MAC9B,UAAU,KAAK,QAAQ;MACvB,YAAY,KAAK,QAAQ;MACzB,cAAc,KAAK,QAAQ;MAC3B,UAAU,KAAK,QAAQ;MACvB,mBAAmB,KAAK,QAAQ;MAChC,iBAAiB,KAAK,QAAQ;MAC9B,SAAS,KAAK,QAAQ;MACtB,mBAAmB,KAAK,QAAQ;MAChC,cAAc,KAAK,QAAQ;MAC3B,MAAM,KAAK,QAAQ;MACnB,gBAAgB,KAAK,QAAQ;MAC7B,UAAU,KAAK,QAAQ;MACvB,QAAQ,KAAK,QAAQ;MACrB,UAAU,KAAK,QAAQ;MACvB,kBAAkB,KAAK,QAAQ;MAC/B,SAAS,eAAe,SAAS;MACjC,KAAK,KAAK,QAAQ;IACnB,CAAA;AAED,mBAAe,MAAM;AAErB,WAAO;MACL;MACA,EAAE,sBAAsB,GAAE;MAC1B;QACE;QACA,gBACE,KAAK,QAAQ,gBACb;UACE,OAAO,KAAK,QAAQ;UACpB,QAAQ,KAAK,QAAQ;UACrB,iBAAiB,KAAK,QAAQ;UAC9B,UAAU,KAAK,QAAQ;UACvB,YAAY,KAAK,QAAQ;UACzB,cAAc,KAAK,QAAQ;UAC3B,mBAAmB,KAAK,QAAQ;UAChC,iBAAiB,KAAK,QAAQ;UAC9B,SAAS,KAAK,QAAQ;UACtB,mBAAmB,KAAK,QAAQ;UAChC,cAAc,KAAK,QAAQ;UAC3B,MAAM,KAAK,QAAQ;UACnB,gBAAgB,KAAK,QAAQ;UAC7B,QAAQ,KAAK,QAAQ;UACrB,UAAU,KAAK,QAAQ;UACvB,kBAAkB,KAAK,QAAQ;UAC/B,KAAK,KAAK,QAAQ;QACnB,GACD,cAAc;MAEjB;;;AAGN,CAAA;", "names": []}