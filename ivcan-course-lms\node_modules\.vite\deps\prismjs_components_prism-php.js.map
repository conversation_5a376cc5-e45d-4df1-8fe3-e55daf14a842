{"version": 3, "sources": ["../../prismjs/components/prism-php.js"], "sourcesContent": ["/**\n * Original by <PERSON>: http://aahacreative.com/2012/07/31/php-syntax-highlighting-prism/\n * Modified by <PERSON>: http://milesj.me\n * Rewritten by <PERSON>\n *\n * Supports PHP 5.3 - 8.0\n */\n(function (Prism) {\n\tvar comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/;\n\tvar constant = [\n\t\t{\n\t\t\tpattern: /\\b(?:false|true)\\b/i,\n\t\t\talias: 'boolean'\n\t\t},\n\t\t{\n\t\t\tpattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n\t\t\tgreedy: true,\n\t\t\tlookbehind: true,\n\t\t},\n\t\t{\n\t\t\tpattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n\t\t\tgreedy: true,\n\t\t\tlookbehind: true,\n\t\t},\n\t\t/\\b(?:null)\\b/i,\n\t\t/\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/,\n\t];\n\tvar number = /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i;\n\tvar operator = /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/;\n\tvar punctuation = /[{}\\[\\](),:;]/;\n\n\tPrism.languages.php = {\n\t\t'delimiter': {\n\t\t\tpattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n\t\t\talias: 'important'\n\t\t},\n\t\t'comment': comment,\n\t\t'variable': /\\$+(?:\\w+\\b|(?=\\{))/,\n\t\t'package': {\n\t\t\tpattern: /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'punctuation': /\\\\/\n\t\t\t}\n\t\t},\n\t\t'class-name-definition': {\n\t\t\tpattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\tlookbehind: true,\n\t\t\talias: 'class-name'\n\t\t},\n\t\t'function-definition': {\n\t\t\tpattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n\t\t\tlookbehind: true,\n\t\t\talias: 'function'\n\t\t},\n\t\t'keyword': [\n\t\t\t{\n\t\t\t\tpattern: /(\\(\\s*)\\b(?:array|bool|boolean|float|int|integer|object|string)\\b(?=\\s*\\))/i,\n\t\t\t\talias: 'type-casting',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /([(,?]\\s*)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string)\\b(?=\\s*\\$)/i,\n\t\t\t\talias: 'type-hint',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|never|object|self|static|string|void)\\b/i,\n\t\t\t\talias: 'return-type',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b(?:array(?!\\s*\\()|bool|float|int|iterable|mixed|object|string|void)\\b/i,\n\t\t\t\talias: 'type-declaration',\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\|\\s*)(?:false|null)\\b|\\b(?:false|null)(?=\\s*\\|)/i,\n\t\t\t\talias: 'type-declaration',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n\t\t\t\talias: 'static-context',\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\t// yield from\n\t\t\t\tpattern: /(\\byield\\s+)from\\b/i,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t// `class` is always a keyword unlike other keywords\n\t\t\t/\\bclass\\b/i,\n\t\t\t{\n\t\t\t\t// https://www.php.net/manual/en/reserved.keywords.php\n\t\t\t\t//\n\t\t\t\t// keywords cannot be preceded by \"->\"\n\t\t\t\t// the complex lookbehind means `(?<!(?:->|::)\\s*)`\n\t\t\t\tpattern: /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|never|new|or|parent|print|private|protected|public|readonly|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\\b/i,\n\t\t\t\tlookbehind: true\n\t\t\t}\n\t\t],\n\t\t'argument-name': {\n\t\t\tpattern: /([(,]\\s*)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'class-name': [\n\t\t\t{\n\t\t\t\tpattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n\t\t\t\talias: 'class-name-fully-qualified',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n\t\t\t\talias: 'class-name-fully-qualified',\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n\t\t\t\talias: 'class-name-fully-qualified',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n\t\t\t\talias: 'type-declaration',\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n\t\t\t\talias: ['class-name-fully-qualified', 'type-declaration'],\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n\t\t\t\talias: 'static-context',\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n\t\t\t\talias: ['class-name-fully-qualified', 'static-context'],\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n\t\t\t\talias: 'type-hint',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n\t\t\t\talias: ['class-name-fully-qualified', 'type-hint'],\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\t\talias: 'return-type',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n\t\t\t\talias: ['class-name-fully-qualified', 'return-type'],\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'constant': constant,\n\t\t'function': {\n\t\t\tpattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'punctuation': /\\\\/\n\t\t\t}\n\t\t},\n\t\t'property': {\n\t\t\tpattern: /(->\\s*)\\w+/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'number': number,\n\t\t'operator': operator,\n\t\t'punctuation': punctuation\n\t};\n\n\tvar string_interpolation = {\n\t\tpattern: /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n\t\tlookbehind: true,\n\t\tinside: Prism.languages.php\n\t};\n\n\tvar string = [\n\t\t{\n\t\t\tpattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n\t\t\talias: 'nowdoc-string',\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'delimiter': {\n\t\t\t\t\tpattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n\t\t\t\t\talias: 'symbol',\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'punctuation': /^<<<'?|[';]$/\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\tpattern: /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n\t\t\talias: 'heredoc-string',\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'delimiter': {\n\t\t\t\t\tpattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n\t\t\t\t\talias: 'symbol',\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'punctuation': /^<<<\"?|[\";]$/\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t'interpolation': string_interpolation\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\tpattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n\t\t\talias: 'backtick-quoted-string',\n\t\t\tgreedy: true\n\t\t},\n\t\t{\n\t\t\tpattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n\t\t\talias: 'single-quoted-string',\n\t\t\tgreedy: true\n\t\t},\n\t\t{\n\t\t\tpattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n\t\t\talias: 'double-quoted-string',\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'interpolation': string_interpolation\n\t\t\t}\n\t\t}\n\t];\n\n\tPrism.languages.insertBefore('php', 'variable', {\n\t\t'string': string,\n\t\t'attribute': {\n\t\t\tpattern: /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'attribute-content': {\n\t\t\t\t\tpattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t// inside can appear subset of php\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'comment': comment,\n\t\t\t\t\t\t'string': string,\n\t\t\t\t\t\t'attribute-class-name': [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\t\t\t\t\t\talias: 'class-name',\n\t\t\t\t\t\t\t\tgreedy: true,\n\t\t\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n\t\t\t\t\t\t\t\talias: [\n\t\t\t\t\t\t\t\t\t'class-name',\n\t\t\t\t\t\t\t\t\t'class-name-fully-qualified'\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\tgreedy: true,\n\t\t\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\t\t\tinside: {\n\t\t\t\t\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\t'constant': constant,\n\t\t\t\t\t\t'number': number,\n\t\t\t\t\t\t'operator': operator,\n\t\t\t\t\t\t'punctuation': punctuation\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t'delimiter': {\n\t\t\t\t\tpattern: /^#\\[|\\]$/,\n\t\t\t\t\talias: 'punctuation'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t});\n\n\tPrism.hooks.add('before-tokenize', function (env) {\n\t\tif (!/<\\?/.test(env.code)) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar phpPattern = /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/g;\n\t\tPrism.languages['markup-templating'].buildPlaceholders(env, 'php', phpPattern);\n\t});\n\n\tPrism.hooks.add('after-tokenize', function (env) {\n\t\tPrism.languages['markup-templating'].tokenizePlaceholders(env, 'php');\n\t});\n\n}(Prism));\n"], "mappings": ";CAOC,SAAUA,QAAO;AACjB,MAAI,UAAU;AACd,MAAI,WAAW;AAAA,IACd;AAAA,MACC,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,IACb;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,MAAI,SAAS;AACb,MAAI,WAAW;AACf,MAAI,cAAc;AAElB,EAAAA,OAAM,UAAU,MAAM;AAAA,IACrB,aAAa;AAAA,MACZ,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,IACA,yBAAyB;AAAA,MACxB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,uBAAuB;AAAA,MACtB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACV;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA,QAEC,SAAS;AAAA,QACT,YAAY;AAAA,MACb;AAAA;AAAA,MAEA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA,QAKC,SAAS;AAAA,QACT,YAAY;AAAA,MACb;AAAA,IACD;AAAA,IACA,iBAAiB;AAAA,MAChB,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,cAAc;AAAA,MACb;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO,CAAC,8BAA8B,kBAAkB;AAAA,QACxD,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO,CAAC,8BAA8B,gBAAgB;AAAA,QACtD,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO,CAAC,8BAA8B,WAAW;AAAA,QACjD,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO,CAAC,8BAA8B,aAAa;AAAA,QACnD,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAAA,IACA,YAAY;AAAA,IACZ,YAAY;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,IACA,YAAY;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,eAAe;AAAA,EAChB;AAEA,MAAI,uBAAuB;AAAA,IAC1B,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQA,OAAM,UAAU;AAAA,EACzB;AAEA,MAAI,SAAS;AAAA,IACZ;AAAA,MACC,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,aAAa;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,YACP,eAAe;AAAA,UAChB;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,aAAa;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,YACP,eAAe;AAAA,UAChB;AAAA,QACD;AAAA,QACA,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAEA,EAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,IAC/C,UAAU;AAAA,IACV,aAAa;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,qBAAqB;AAAA,UACpB,SAAS;AAAA,UACT,YAAY;AAAA;AAAA,UAEZ,QAAQ;AAAA,YACP,WAAW;AAAA,YACX,UAAU;AAAA,YACV,wBAAwB;AAAA,cACvB;AAAA,gBACC,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,cACb;AAAA,cACA;AAAA,gBACC,SAAS;AAAA,gBACT,OAAO;AAAA,kBACN;AAAA,kBACA;AAAA,gBACD;AAAA,gBACA,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACP,eAAe;AAAA,gBAChB;AAAA,cACD;AAAA,YACD;AAAA,YACA,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,eAAe;AAAA,UAChB;AAAA,QACD;AAAA,QACA,aAAa;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AACjD,QAAI,CAAC,MAAM,KAAK,IAAI,IAAI,GAAG;AAC1B;AAAA,IACD;AAEA,QAAI,aAAa;AACjB,IAAAA,OAAM,UAAU,mBAAmB,EAAE,kBAAkB,KAAK,OAAO,UAAU;AAAA,EAC9E,CAAC;AAED,EAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAChD,IAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,KAAK;AAAA,EACrE,CAAC;AAEF,GAAE,KAAK;", "names": ["Prism"]}