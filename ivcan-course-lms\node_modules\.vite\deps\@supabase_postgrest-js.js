import {
  PostgrestBuilder,
  PostgrestClient,
  PostgrestError,
  PostgrestFilterBuilder,
  PostgrestQueryBuilder,
  PostgrestTransformBuilder,
  wrapper_default
} from "./chunk-XHGBCJER.js";
import "./chunk-3AA2TT25.js";
import "./chunk-OL46QLBJ.js";
export {
  PostgrestBuilder,
  PostgrestClient,
  PostgrestError,
  PostgrestFilterBuilder,
  PostgrestQueryBuilder,
  PostgrestTransformBuilder,
  wrapper_default as default
};
//# sourceMappingURL=@supabase_postgrest-js.js.map
