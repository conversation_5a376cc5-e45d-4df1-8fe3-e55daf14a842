{"version": 3, "sources": ["../../prismjs/plugins/toolbar/prism-toolbar.js"], "sourcesContent": ["(function () {\n\n\tif (typeof Prism === 'undefined' || typeof document === 'undefined') {\n\t\treturn;\n\t}\n\n\tvar callbacks = [];\n\tvar map = {};\n\tvar noop = function () {};\n\n\tPrism.plugins.toolbar = {};\n\n\t/**\n\t * @typedef ButtonOptions\n\t * @property {string} text The text displayed.\n\t * @property {string} [url] The URL of the link which will be created.\n\t * @property {Function} [onClick] The event listener for the `click` event of the created button.\n\t * @property {string} [className] The class attribute to include with element.\n\t */\n\n\t/**\n\t * Register a button callback with the toolbar.\n\t *\n\t * @param {string} key\n\t * @param {ButtonOptions|Function} opts\n\t */\n\tvar registerButton = Prism.plugins.toolbar.registerButton = function (key, opts) {\n\t\tvar callback;\n\n\t\tif (typeof opts === 'function') {\n\t\t\tcallback = opts;\n\t\t} else {\n\t\t\tcallback = function (env) {\n\t\t\t\tvar element;\n\n\t\t\t\tif (typeof opts.onClick === 'function') {\n\t\t\t\t\telement = document.createElement('button');\n\t\t\t\t\telement.type = 'button';\n\t\t\t\t\telement.addEventListener('click', function () {\n\t\t\t\t\t\topts.onClick.call(this, env);\n\t\t\t\t\t});\n\t\t\t\t} else if (typeof opts.url === 'string') {\n\t\t\t\t\telement = document.createElement('a');\n\t\t\t\t\telement.href = opts.url;\n\t\t\t\t} else {\n\t\t\t\t\telement = document.createElement('span');\n\t\t\t\t}\n\n\t\t\t\tif (opts.className) {\n\t\t\t\t\telement.classList.add(opts.className);\n\t\t\t\t}\n\n\t\t\t\telement.textContent = opts.text;\n\n\t\t\t\treturn element;\n\t\t\t};\n\t\t}\n\n\t\tif (key in map) {\n\t\t\tconsole.warn('There is a button with the key \"' + key + '\" registered already.');\n\t\t\treturn;\n\t\t}\n\n\t\tcallbacks.push(map[key] = callback);\n\t};\n\n\t/**\n\t * Returns the callback order of the given element.\n\t *\n\t * @param {HTMLElement} element\n\t * @returns {string[] | undefined}\n\t */\n\tfunction getOrder(element) {\n\t\twhile (element) {\n\t\t\tvar order = element.getAttribute('data-toolbar-order');\n\t\t\tif (order != null) {\n\t\t\t\torder = order.trim();\n\t\t\t\tif (order.length) {\n\t\t\t\t\treturn order.split(/\\s*,\\s*/g);\n\t\t\t\t} else {\n\t\t\t\t\treturn [];\n\t\t\t\t}\n\t\t\t}\n\t\t\telement = element.parentElement;\n\t\t}\n\t}\n\n\t/**\n\t * Post-highlight Prism hook callback.\n\t *\n\t * @param env\n\t */\n\tvar hook = Prism.plugins.toolbar.hook = function (env) {\n\t\t// Check if inline or actual code block (credit to line-numbers plugin)\n\t\tvar pre = env.element.parentNode;\n\t\tif (!pre || !/pre/i.test(pre.nodeName)) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Autoloader rehighlights, so only do this once.\n\t\tif (pre.parentNode.classList.contains('code-toolbar')) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Create wrapper for <pre> to prevent scrolling toolbar with content\n\t\tvar wrapper = document.createElement('div');\n\t\twrapper.classList.add('code-toolbar');\n\t\tpre.parentNode.insertBefore(wrapper, pre);\n\t\twrapper.appendChild(pre);\n\n\t\t// Setup the toolbar\n\t\tvar toolbar = document.createElement('div');\n\t\ttoolbar.classList.add('toolbar');\n\n\t\t// order callbacks\n\t\tvar elementCallbacks = callbacks;\n\t\tvar order = getOrder(env.element);\n\t\tif (order) {\n\t\t\telementCallbacks = order.map(function (key) {\n\t\t\t\treturn map[key] || noop;\n\t\t\t});\n\t\t}\n\n\t\telementCallbacks.forEach(function (callback) {\n\t\t\tvar element = callback(env);\n\n\t\t\tif (!element) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar item = document.createElement('div');\n\t\t\titem.classList.add('toolbar-item');\n\n\t\t\titem.appendChild(element);\n\t\t\ttoolbar.appendChild(item);\n\t\t});\n\n\t\t// Add our toolbar to the currently created wrapper of <pre> tag\n\t\twrapper.appendChild(toolbar);\n\t};\n\n\tregisterButton('label', function (env) {\n\t\tvar pre = env.element.parentNode;\n\t\tif (!pre || !/pre/i.test(pre.nodeName)) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!pre.hasAttribute('data-label')) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar element; var template;\n\t\tvar text = pre.getAttribute('data-label');\n\t\ttry {\n\t\t\t// Any normal text will blow up this selector.\n\t\t\ttemplate = document.querySelector('template#' + text);\n\t\t} catch (e) { /* noop */ }\n\n\t\tif (template) {\n\t\t\telement = template.content;\n\t\t} else {\n\t\t\tif (pre.hasAttribute('data-url')) {\n\t\t\t\telement = document.createElement('a');\n\t\t\t\telement.href = pre.getAttribute('data-url');\n\t\t\t} else {\n\t\t\t\telement = document.createElement('span');\n\t\t\t}\n\n\t\t\telement.textContent = text;\n\t\t}\n\n\t\treturn element;\n\t});\n\n\t/**\n\t * Register the toolbar with Prism.\n\t */\n\tPrism.hooks.add('complete', hook);\n}());\n"], "mappings": ";CAAC,WAAY;AAEZ,MAAI,OAAO,UAAU,eAAe,OAAO,aAAa,aAAa;AACpE;AAAA,EACD;AAEA,MAAI,YAAY,CAAC;AACjB,MAAI,MAAM,CAAC;AACX,MAAI,OAAO,WAAY;AAAA,EAAC;AAExB,QAAM,QAAQ,UAAU,CAAC;AAgBzB,MAAI,iBAAiB,MAAM,QAAQ,QAAQ,iBAAiB,SAAU,KAAK,MAAM;AAChF,QAAI;AAEJ,QAAI,OAAO,SAAS,YAAY;AAC/B,iBAAW;AAAA,IACZ,OAAO;AACN,iBAAW,SAAU,KAAK;AACzB,YAAI;AAEJ,YAAI,OAAO,KAAK,YAAY,YAAY;AACvC,oBAAU,SAAS,cAAc,QAAQ;AACzC,kBAAQ,OAAO;AACf,kBAAQ,iBAAiB,SAAS,WAAY;AAC7C,iBAAK,QAAQ,KAAK,MAAM,GAAG;AAAA,UAC5B,CAAC;AAAA,QACF,WAAW,OAAO,KAAK,QAAQ,UAAU;AACxC,oBAAU,SAAS,cAAc,GAAG;AACpC,kBAAQ,OAAO,KAAK;AAAA,QACrB,OAAO;AACN,oBAAU,SAAS,cAAc,MAAM;AAAA,QACxC;AAEA,YAAI,KAAK,WAAW;AACnB,kBAAQ,UAAU,IAAI,KAAK,SAAS;AAAA,QACrC;AAEA,gBAAQ,cAAc,KAAK;AAE3B,eAAO;AAAA,MACR;AAAA,IACD;AAEA,QAAI,OAAO,KAAK;AACf,cAAQ,KAAK,qCAAqC,MAAM,uBAAuB;AAC/E;AAAA,IACD;AAEA,cAAU,KAAK,IAAI,GAAG,IAAI,QAAQ;AAAA,EACnC;AAQA,WAAS,SAAS,SAAS;AAC1B,WAAO,SAAS;AACf,UAAI,QAAQ,QAAQ,aAAa,oBAAoB;AACrD,UAAI,SAAS,MAAM;AAClB,gBAAQ,MAAM,KAAK;AACnB,YAAI,MAAM,QAAQ;AACjB,iBAAO,MAAM,MAAM,UAAU;AAAA,QAC9B,OAAO;AACN,iBAAO,CAAC;AAAA,QACT;AAAA,MACD;AACA,gBAAU,QAAQ;AAAA,IACnB;AAAA,EACD;AAOA,MAAI,OAAO,MAAM,QAAQ,QAAQ,OAAO,SAAU,KAAK;AAEtD,QAAI,MAAM,IAAI,QAAQ;AACtB,QAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,QAAQ,GAAG;AACvC;AAAA,IACD;AAGA,QAAI,IAAI,WAAW,UAAU,SAAS,cAAc,GAAG;AACtD;AAAA,IACD;AAGA,QAAI,UAAU,SAAS,cAAc,KAAK;AAC1C,YAAQ,UAAU,IAAI,cAAc;AACpC,QAAI,WAAW,aAAa,SAAS,GAAG;AACxC,YAAQ,YAAY,GAAG;AAGvB,QAAI,UAAU,SAAS,cAAc,KAAK;AAC1C,YAAQ,UAAU,IAAI,SAAS;AAG/B,QAAI,mBAAmB;AACvB,QAAI,QAAQ,SAAS,IAAI,OAAO;AAChC,QAAI,OAAO;AACV,yBAAmB,MAAM,IAAI,SAAU,KAAK;AAC3C,eAAO,IAAI,GAAG,KAAK;AAAA,MACpB,CAAC;AAAA,IACF;AAEA,qBAAiB,QAAQ,SAAU,UAAU;AAC5C,UAAI,UAAU,SAAS,GAAG;AAE1B,UAAI,CAAC,SAAS;AACb;AAAA,MACD;AAEA,UAAI,OAAO,SAAS,cAAc,KAAK;AACvC,WAAK,UAAU,IAAI,cAAc;AAEjC,WAAK,YAAY,OAAO;AACxB,cAAQ,YAAY,IAAI;AAAA,IACzB,CAAC;AAGD,YAAQ,YAAY,OAAO;AAAA,EAC5B;AAEA,iBAAe,SAAS,SAAU,KAAK;AACtC,QAAI,MAAM,IAAI,QAAQ;AACtB,QAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,QAAQ,GAAG;AACvC;AAAA,IACD;AAEA,QAAI,CAAC,IAAI,aAAa,YAAY,GAAG;AACpC;AAAA,IACD;AAEA,QAAI;AAAS,QAAI;AACjB,QAAI,OAAO,IAAI,aAAa,YAAY;AACxC,QAAI;AAEH,iBAAW,SAAS,cAAc,cAAc,IAAI;AAAA,IACrD,SAAS,GAAG;AAAA,IAAa;AAEzB,QAAI,UAAU;AACb,gBAAU,SAAS;AAAA,IACpB,OAAO;AACN,UAAI,IAAI,aAAa,UAAU,GAAG;AACjC,kBAAU,SAAS,cAAc,GAAG;AACpC,gBAAQ,OAAO,IAAI,aAAa,UAAU;AAAA,MAC3C,OAAO;AACN,kBAAU,SAAS,cAAc,MAAM;AAAA,MACxC;AAEA,cAAQ,cAAc;AAAA,IACvB;AAEA,WAAO;AAAA,EACR,CAAC;AAKD,QAAM,MAAM,IAAI,YAAY,IAAI;AACjC,GAAE;", "names": []}