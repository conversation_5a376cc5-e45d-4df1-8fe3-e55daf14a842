{"version": 3, "sources": ["../../prismjs/components/prism-csharp.js"], "sourcesContent": ["(function (Prism) {\n\n\t/**\n\t * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n\t *\n\t * Note: This is a simple text based replacement. Be careful when using backreferences!\n\t *\n\t * @param {string} pattern the given pattern.\n\t * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n\t * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n\t * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n\t */\n\tfunction replace(pattern, replacements) {\n\t\treturn pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n\t\t\treturn '(?:' + replacements[+index] + ')';\n\t\t});\n\t}\n\t/**\n\t * @param {string} pattern\n\t * @param {string[]} replacements\n\t * @param {string} [flags]\n\t * @returns {RegExp}\n\t */\n\tfunction re(pattern, replacements, flags) {\n\t\treturn RegExp(replace(pattern, replacements), flags || '');\n\t}\n\n\t/**\n\t * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n\t *\n\t * @param {string} pattern\n\t * @param {number} depthLog2\n\t * @returns {string}\n\t */\n\tfunction nested(pattern, depthLog2) {\n\t\tfor (var i = 0; i < depthLog2; i++) {\n\t\t\tpattern = pattern.replace(/<<self>>/g, function () { return '(?:' + pattern + ')'; });\n\t\t}\n\t\treturn pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]');\n\t}\n\n\t// https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/\n\tvar keywordKinds = {\n\t\t// keywords which represent a return or variable type\n\t\ttype: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n\t\t// keywords which are used to declare a type\n\t\ttypeDeclaration: 'class enum interface record struct',\n\t\t// contextual keywords\n\t\t// (\"var\" and \"dynamic\" are missing because they are used like types)\n\t\tcontextual: 'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n\t\t// all other keywords\n\t\tother: 'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n\t};\n\n\t// keywords\n\tfunction keywordsToPattern(words) {\n\t\treturn '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b';\n\t}\n\tvar typeDeclarationKeywords = keywordsToPattern(keywordKinds.typeDeclaration);\n\tvar keywords = RegExp(keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other));\n\tvar nonTypeKeywords = keywordsToPattern(keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other);\n\tvar nonContextualKeywords = keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.other);\n\n\t// types\n\tvar generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2); // the idea behind the other forbidden characters is to prevent false positives. Same for tupleElement.\n\tvar nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2);\n\tvar name = /@?\\b[A-Za-z_]\\w*\\b/.source;\n\tvar genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [name, generic]);\n\tvar identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [nonTypeKeywords, genericName]);\n\tvar array = /\\[\\s*(?:,\\s*)*\\]/.source;\n\tvar typeExpressionWithoutTuple = replace(/<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source, [identifier, array]);\n\tvar tupleElement = replace(/[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source, [generic, nestedRound, array]);\n\tvar tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement]);\n\tvar typeExpression = replace(/(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source, [tuple, identifier, array]);\n\n\tvar typeInside = {\n\t\t'keyword': keywords,\n\t\t'punctuation': /[<>()?,.:[\\]]/\n\t};\n\n\t// strings & characters\n\t// https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#character-literals\n\t// https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#string-literals\n\tvar character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source; // simplified pattern\n\tvar regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source;\n\tvar verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source;\n\n\n\tPrism.languages.csharp = Prism.languages.extend('clike', {\n\t\t'string': [\n\t\t\t{\n\t\t\t\tpattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t}\n\t\t],\n\t\t'class-name': [\n\t\t\t{\n\t\t\t\t// Using static\n\t\t\t\t// using static System.Math;\n\t\t\t\tpattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [identifier]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: typeInside\n\t\t\t},\n\t\t\t{\n\t\t\t\t// Using alias (type)\n\t\t\t\t// using Project = PC.MyCompany.Project;\n\t\t\t\tpattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [name, typeExpression]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: typeInside\n\t\t\t},\n\t\t\t{\n\t\t\t\t// Using alias (alias)\n\t\t\t\t// using Project = PC.MyCompany.Project;\n\t\t\t\tpattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\t// Type declarations\n\t\t\t\t// class Foo<A, B>\n\t\t\t\t// interface Foo<out A, B>\n\t\t\t\tpattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [typeDeclarationKeywords, genericName]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: typeInside\n\t\t\t},\n\t\t\t{\n\t\t\t\t// Single catch exception declaration\n\t\t\t\t// catch(Foo)\n\t\t\t\t// (things like catch(Foo e) is covered by variable declaration)\n\t\t\t\tpattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: typeInside\n\t\t\t},\n\t\t\t{\n\t\t\t\t// Name of the type parameter of generic constraints\n\t\t\t\t// where Foo : class\n\t\t\t\tpattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\t// Casts and checks via as and is.\n\t\t\t\t// as Foo<A>, is Bar<B>\n\t\t\t\t// (things like if(a is Foo b) is covered by variable declaration)\n\t\t\t\tpattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [typeExpressionWithoutTuple]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: typeInside\n\t\t\t},\n\t\t\t{\n\t\t\t\t// Variable, field and parameter declaration\n\t\t\t\t// (Foo bar, Bar baz, Foo[,,] bay, Foo<Bar, FooBar<Bar>> bax)\n\t\t\t\tpattern: re(/\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/.source, [typeExpression, nonContextualKeywords, name]),\n\t\t\t\tinside: typeInside\n\t\t\t}\n\t\t],\n\t\t'keyword': keywords,\n\t\t// https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#literals\n\t\t'number': /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n\t\t'operator': />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n\t\t'punctuation': /\\?\\.?|::|[{}[\\];(),.:]/\n\t});\n\n\tPrism.languages.insertBefore('csharp', 'number', {\n\t\t'range': {\n\t\t\tpattern: /\\.\\./,\n\t\t\talias: 'operator'\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('csharp', 'punctuation', {\n\t\t'named-parameter': {\n\t\t\tpattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n\t\t\tlookbehind: true,\n\t\t\talias: 'punctuation'\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('csharp', 'class-name', {\n\t\t'namespace': {\n\t\t\t// namespace Foo.Bar {}\n\t\t\t// using Foo.Bar;\n\t\t\tpattern: re(/(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source, [name]),\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'punctuation': /\\./\n\t\t\t}\n\t\t},\n\t\t'type-expression': {\n\t\t\t// default(Foo), typeof(Foo<Bar>), sizeof(int)\n\t\t\tpattern: re(/(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/.source, [nestedRound]),\n\t\t\tlookbehind: true,\n\t\t\talias: 'class-name',\n\t\t\tinside: typeInside\n\t\t},\n\t\t'return-type': {\n\t\t\t// Foo<Bar> ForBar(); Foo IFoo.Bar() => 0\n\t\t\t// int this[int index] => 0; T IReadOnlyList<T>.this[int index] => this[index];\n\t\t\t// int Foo => 0; int Foo { get; set } = 0;\n\t\t\tpattern: re(/<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source, [typeExpression, identifier]),\n\t\t\tinside: typeInside,\n\t\t\talias: 'class-name'\n\t\t},\n\t\t'constructor-invocation': {\n\t\t\t// new List<Foo<Bar[]>> { }\n\t\t\tpattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n\t\t\tlookbehind: true,\n\t\t\tinside: typeInside,\n\t\t\talias: 'class-name'\n\t\t},\n\t\t/*'explicit-implementation': {\n\t\t\t// int IFoo<Foo>.Bar => 0; void IFoo<Foo<Foo>>.Foo<T>();\n\t\t\tpattern: replace(/\\b<<0>>(?=\\.<<1>>)/, className, methodOrPropertyDeclaration),\n\t\t\tinside: classNameInside,\n\t\t\talias: 'class-name'\n\t\t},*/\n\t\t'generic-method': {\n\t\t\t// foo<Bar>()\n\t\t\tpattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [name, generic]),\n\t\t\tinside: {\n\t\t\t\t'function': re(/^<<0>>/.source, [name]),\n\t\t\t\t'generic': {\n\t\t\t\t\tpattern: RegExp(generic),\n\t\t\t\t\talias: 'class-name',\n\t\t\t\t\tinside: typeInside\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'type-list': {\n\t\t\t// The list of types inherited or of generic constraints\n\t\t\t// class Foo<F> : Bar, IList<FooBar>\n\t\t\t// where F : Bar, IList<int>\n\t\t\tpattern: re(\n\t\t\t\t/\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/.source,\n\t\t\t\t[typeDeclarationKeywords, genericName, name, typeExpression, keywords.source, nestedRound, /\\bnew\\s*\\(\\s*\\)/.source]\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'record-arguments': {\n\t\t\t\t\tpattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [genericName, nestedRound]),\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\tgreedy: true,\n\t\t\t\t\tinside: Prism.languages.csharp\n\t\t\t\t},\n\t\t\t\t'keyword': keywords,\n\t\t\t\t'class-name': {\n\t\t\t\t\tpattern: RegExp(typeExpression),\n\t\t\t\t\tgreedy: true,\n\t\t\t\t\tinside: typeInside\n\t\t\t\t},\n\t\t\t\t'punctuation': /[,()]/\n\t\t\t}\n\t\t},\n\t\t'preprocessor': {\n\t\t\tpattern: /(^[\\t ]*)#.*/m,\n\t\t\tlookbehind: true,\n\t\t\talias: 'property',\n\t\t\tinside: {\n\t\t\t\t// highlight preprocessor directives as keywords\n\t\t\t\t'directive': {\n\t\t\t\t\tpattern: /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'keyword'\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\t// attributes\n\tvar regularStringOrCharacter = regularString + '|' + character;\n\tvar regularStringCharacterOrComment = replace(/\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source, [regularStringOrCharacter]);\n\tvar roundExpression = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n\n\t// https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/attributes/#attribute-targets\n\tvar attrTarget = /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/.source;\n\tvar attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [identifier, roundExpression]);\n\n\tPrism.languages.insertBefore('csharp', 'class-name', {\n\t\t'attribute': {\n\t\t\t// Attributes\n\t\t\t// [Foo], [Foo(1), Bar(2, Prop = \"foo\")], [return: Foo(1), Bar(2)], [assembly: Foo(Bar)]\n\t\t\tpattern: re(/((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/.source, [attrTarget, attr]),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'target': {\n\t\t\t\t\tpattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n\t\t\t\t\talias: 'keyword'\n\t\t\t\t},\n\t\t\t\t'attribute-arguments': {\n\t\t\t\t\tpattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n\t\t\t\t\tinside: Prism.languages.csharp\n\t\t\t\t},\n\t\t\t\t'class-name': {\n\t\t\t\t\tpattern: RegExp(identifier),\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'punctuation': /\\./\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t'punctuation': /[:,]/\n\t\t\t}\n\t\t}\n\t});\n\n\n\t// string interpolation\n\tvar formatString = /:[^}\\r\\n]+/.source;\n\t// multi line\n\tvar mInterpolationRound = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n\tvar mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [mInterpolationRound, formatString]);\n\t// single line\n\tvar sInterpolationRound = nested(replace(/[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/.source, [regularStringOrCharacter]), 2);\n\tvar sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [sInterpolationRound, formatString]);\n\n\tfunction createInterpolationInside(interpolation, interpolationRound) {\n\t\treturn {\n\t\t\t'interpolation': {\n\t\t\t\tpattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'format-string': {\n\t\t\t\t\t\tpattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [interpolationRound, formatString]),\n\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\tinside: {\n\t\t\t\t\t\t\t'punctuation': /^:/\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\t'punctuation': /^\\{|\\}$/,\n\t\t\t\t\t'expression': {\n\t\t\t\t\t\tpattern: /[\\s\\S]+/,\n\t\t\t\t\t\talias: 'language-csharp',\n\t\t\t\t\t\tinside: Prism.languages.csharp\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t'string': /[\\s\\S]+/\n\t\t};\n\t}\n\n\tPrism.languages.insertBefore('csharp', 'string', {\n\t\t'interpolation-string': [\n\t\t\t{\n\t\t\t\tpattern: re(/(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source, [mInterpolation]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: createInterpolationInside(mInterpolation, mInterpolationRound),\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [sInterpolation]),\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: createInterpolationInside(sInterpolation, sInterpolationRound),\n\t\t\t}\n\t\t],\n\t\t'char': {\n\t\t\tpattern: RegExp(character),\n\t\t\tgreedy: true\n\t\t}\n\t});\n\n\tPrism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp;\n\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAYjB,WAAS,QAAQ,SAAS,cAAc;AACvC,WAAO,QAAQ,QAAQ,cAAc,SAAU,GAAG,OAAO;AACxD,aAAO,QAAQ,aAAa,CAAC,KAAK,IAAI;AAAA,IACvC,CAAC;AAAA,EACF;AAOA,WAAS,GAAG,SAAS,cAAc,OAAO;AACzC,WAAO,OAAO,QAAQ,SAAS,YAAY,GAAG,SAAS,EAAE;AAAA,EAC1D;AASA,WAAS,OAAO,SAAS,WAAW;AACnC,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AACnC,gBAAU,QAAQ,QAAQ,aAAa,WAAY;AAAE,eAAO,QAAQ,UAAU;AAAA,MAAK,CAAC;AAAA,IACrF;AACA,WAAO,QAAQ,QAAQ,aAAa,WAAW;AAAA,EAChD;AAGA,MAAI,eAAe;AAAA;AAAA,IAElB,MAAM;AAAA;AAAA,IAEN,iBAAiB;AAAA;AAAA;AAAA,IAGjB,YAAY;AAAA;AAAA,IAEZ,OAAO;AAAA,EACR;AAGA,WAAS,kBAAkB,OAAO;AACjC,WAAO,WAAW,MAAM,KAAK,EAAE,QAAQ,MAAM,GAAG,IAAI;AAAA,EACrD;AACA,MAAI,0BAA0B,kBAAkB,aAAa,eAAe;AAC5E,MAAI,WAAW,OAAO,kBAAkB,aAAa,OAAO,MAAM,aAAa,kBAAkB,MAAM,aAAa,aAAa,MAAM,aAAa,KAAK,CAAC;AAC1J,MAAI,kBAAkB,kBAAkB,aAAa,kBAAkB,MAAM,aAAa,aAAa,MAAM,aAAa,KAAK;AAC/H,MAAI,wBAAwB,kBAAkB,aAAa,OAAO,MAAM,aAAa,kBAAkB,MAAM,aAAa,KAAK;AAG/H,MAAI,UAAU,OAAO,mCAAmC,QAAQ,CAAC;AACjE,MAAI,cAAc,OAAO,0BAA0B,QAAQ,CAAC;AAC5D,MAAI,OAAO,qBAAqB;AAChC,MAAI,cAAc,QAAQ,qBAAqB,QAAQ,CAAC,MAAM,OAAO,CAAC;AACtE,MAAI,aAAa,QAAQ,mCAAmC,QAAQ,CAAC,iBAAiB,WAAW,CAAC;AAClG,MAAI,QAAQ,mBAAmB;AAC/B,MAAI,6BAA6B,QAAQ,yCAAyC,QAAQ,CAAC,YAAY,KAAK,CAAC;AAC7G,MAAI,eAAe,QAAQ,2CAA2C,QAAQ,CAAC,SAAS,aAAa,KAAK,CAAC;AAC3G,MAAI,QAAQ,QAAQ,yBAAyB,QAAQ,CAAC,YAAY,CAAC;AACnE,MAAI,iBAAiB,QAAQ,mDAAmD,QAAQ,CAAC,OAAO,YAAY,KAAK,CAAC;AAElH,MAAI,aAAa;AAAA,IAChB,WAAW;AAAA,IACX,eAAe;AAAA,EAChB;AAKA,MAAI,YAAY,8CAA8C;AAC9D,MAAI,gBAAgB,wBAAwB;AAC5C,MAAI,iBAAiB,kCAAkC;AAGvD,EAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,SAAS;AAAA,IACxD,UAAU;AAAA,MACT;AAAA,QACC,SAAS,GAAG,kBAAkB,QAAQ,CAAC,cAAc,CAAC;AAAA,QACtD,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,SAAS,GAAG,mBAAmB,QAAQ,CAAC,aAAa,CAAC;AAAA,QACtD,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,cAAc;AAAA,MACb;AAAA;AAAA;AAAA,QAGC,SAAS,GAAG,qCAAqC,QAAQ,CAAC,UAAU,CAAC;AAAA,QACrE,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA;AAAA,QAGC,SAAS,GAAG,wCAAwC,QAAQ,CAAC,MAAM,cAAc,CAAC;AAAA,QAClF,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA;AAAA,QAGC,SAAS,GAAG,4BAA4B,QAAQ,CAAC,IAAI,CAAC;AAAA,QACtD,YAAY;AAAA,MACb;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,QAIC,SAAS,GAAG,oBAAoB,QAAQ,CAAC,yBAAyB,WAAW,CAAC;AAAA,QAC9E,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,QAIC,SAAS,GAAG,yBAAyB,QAAQ,CAAC,UAAU,CAAC;AAAA,QACzD,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA;AAAA,QAGC,SAAS,GAAG,oBAAoB,QAAQ,CAAC,IAAI,CAAC;AAAA,QAC9C,YAAY;AAAA,MACb;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,QAIC,SAAS,GAAG,mCAAmC,QAAQ,CAAC,0BAA0B,CAAC;AAAA,QACnF,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA;AAAA,QAGC,SAAS,GAAG,2EAA2E,QAAQ,CAAC,gBAAgB,uBAAuB,IAAI,CAAC;AAAA,QAC5I,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,WAAW;AAAA;AAAA,IAEX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,eAAe;AAAA,EAChB,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA,IAChD,SAAS;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,UAAU,eAAe;AAAA,IACrD,mBAAmB;AAAA,MAClB,SAAS,GAAG,yBAAyB,QAAQ,CAAC,IAAI,CAAC;AAAA,MACnD,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,UAAU,cAAc;AAAA,IACpD,aAAa;AAAA;AAAA;AAAA,MAGZ,SAAS,GAAG,+DAA+D,QAAQ,CAAC,IAAI,CAAC;AAAA,MACzF,YAAY;AAAA,MACZ,QAAQ;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,IACA,mBAAmB;AAAA;AAAA,MAElB,SAAS,GAAG,kFAAkF,QAAQ,CAAC,WAAW,CAAC;AAAA,MACnH,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,IACA,eAAe;AAAA;AAAA;AAAA;AAAA,MAId,SAAS,GAAG,+DAA+D,QAAQ,CAAC,gBAAgB,UAAU,CAAC;AAAA,MAC/G,QAAQ;AAAA,MACR,OAAO;AAAA,IACR;AAAA,IACA,0BAA0B;AAAA;AAAA,MAEzB,SAAS,GAAG,8BAA8B,QAAQ,CAAC,cAAc,CAAC;AAAA,MAClE,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,kBAAkB;AAAA;AAAA,MAEjB,SAAS,GAAG,yBAAyB,QAAQ,CAAC,MAAM,OAAO,CAAC;AAAA,MAC5D,QAAQ;AAAA,QACP,YAAY,GAAG,SAAS,QAAQ,CAAC,IAAI,CAAC;AAAA,QACtC,WAAW;AAAA,UACV,SAAS,OAAO,OAAO;AAAA,UACvB,OAAO;AAAA,UACP,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA,IACA,aAAa;AAAA;AAAA;AAAA;AAAA,MAIZ,SAAS;AAAA,QACR,kKAAkK;AAAA,QAClK,CAAC,yBAAyB,aAAa,MAAM,gBAAgB,SAAS,QAAQ,aAAa,kBAAkB,MAAM;AAAA,MACpH;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ;AAAA,QACP,oBAAoB;AAAA,UACnB,SAAS,GAAG,+BAA+B,QAAQ,CAAC,aAAa,WAAW,CAAC;AAAA,UAC7E,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQA,OAAM,UAAU;AAAA,QACzB;AAAA,QACA,WAAW;AAAA,QACX,cAAc;AAAA,UACb,SAAS,OAAO,cAAc;AAAA,UAC9B,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACA,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,IACA,gBAAgB;AAAA,MACf,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA;AAAA,QAEP,aAAa;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,EACD,CAAC;AAGD,MAAI,2BAA2B,gBAAgB,MAAM;AACrD,MAAI,kCAAkC,QAAQ,iEAAiE,QAAQ,CAAC,wBAAwB,CAAC;AACjJ,MAAI,kBAAkB,OAAO,QAAQ,+BAA+B,QAAQ,CAAC,+BAA+B,CAAC,GAAG,CAAC;AAGjH,MAAI,aAAa,wEAAwE;AACzF,MAAI,OAAO,QAAQ,0BAA0B,QAAQ,CAAC,YAAY,eAAe,CAAC;AAElF,EAAAA,OAAM,UAAU,aAAa,UAAU,cAAc;AAAA,IACpD,aAAa;AAAA;AAAA;AAAA,MAGZ,SAAS,GAAG,6EAA6E,QAAQ,CAAC,YAAY,IAAI,CAAC;AAAA,MACnH,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,UAAU;AAAA,UACT,SAAS,GAAG,iBAAiB,QAAQ,CAAC,UAAU,CAAC;AAAA,UACjD,OAAO;AAAA,QACR;AAAA,QACA,uBAAuB;AAAA,UACtB,SAAS,GAAG,aAAa,QAAQ,CAAC,eAAe,CAAC;AAAA,UAClD,QAAQA,OAAM,UAAU;AAAA,QACzB;AAAA,QACA,cAAc;AAAA,UACb,SAAS,OAAO,UAAU;AAAA,UAC1B,QAAQ;AAAA,YACP,eAAe;AAAA,UAChB;AAAA,QACD;AAAA,QACA,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,EACD,CAAC;AAID,MAAI,eAAe,aAAa;AAEhC,MAAI,sBAAsB,OAAO,QAAQ,+BAA+B,QAAQ,CAAC,+BAA+B,CAAC,GAAG,CAAC;AACrH,MAAI,iBAAiB,QAAQ,qCAAqC,QAAQ,CAAC,qBAAqB,YAAY,CAAC;AAE7G,MAAI,sBAAsB,OAAO,QAAQ,mEAAmE,QAAQ,CAAC,wBAAwB,CAAC,GAAG,CAAC;AAClJ,MAAI,iBAAiB,QAAQ,qCAAqC,QAAQ,CAAC,qBAAqB,YAAY,CAAC;AAE7G,WAAS,0BAA0B,eAAe,oBAAoB;AACrE,WAAO;AAAA,MACN,iBAAiB;AAAA,QAChB,SAAS,GAAG,6BAA6B,QAAQ,CAAC,aAAa,CAAC;AAAA,QAChE,YAAY;AAAA,QACZ,QAAQ;AAAA,UACP,iBAAiB;AAAA,YAChB,SAAS,GAAG,sCAAsC,QAAQ,CAAC,oBAAoB,YAAY,CAAC;AAAA,YAC5F,YAAY;AAAA,YACZ,QAAQ;AAAA,cACP,eAAe;AAAA,YAChB;AAAA,UACD;AAAA,UACA,eAAe;AAAA,UACf,cAAc;AAAA,YACb,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQA,OAAM,UAAU;AAAA,UACzB;AAAA,QACD;AAAA,MACD;AAAA,MACA,UAAU;AAAA,IACX;AAAA,EACD;AAEA,EAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA,IAChD,wBAAwB;AAAA,MACvB;AAAA,QACC,SAAS,GAAG,4DAA4D,QAAQ,CAAC,cAAc,CAAC;AAAA,QAChG,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ,0BAA0B,gBAAgB,mBAAmB;AAAA,MACtE;AAAA,MACA;AAAA,QACC,SAAS,GAAG,4CAA4C,QAAQ,CAAC,cAAc,CAAC;AAAA,QAChF,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ,0BAA0B,gBAAgB,mBAAmB;AAAA,MACtE;AAAA,IACD;AAAA,IACA,QAAQ;AAAA,MACP,SAAS,OAAO,SAAS;AAAA,MACzB,QAAQ;AAAA,IACT;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAE/D,GAAE,KAAK;", "names": ["Prism"]}