{"version": 3, "sources": ["../../prismjs/components/prism-java.js"], "sourcesContent": ["(function (Prism) {\n\n\tvar keywords = /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\\s*[(){}[\\]<>=%~.:,;?+\\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/;\n\n\t// full package (optional) + parent classes (optional)\n\tvar classNamePrefix = /(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/.source;\n\n\t// based on the java naming conventions\n\tvar className = {\n\t\tpattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n\t\tlookbehind: true,\n\t\tinside: {\n\t\t\t'namespace': {\n\t\t\t\tpattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\./\n\t\t\t\t}\n\t\t\t},\n\t\t\t'punctuation': /\\./\n\t\t}\n\t};\n\n\tPrism.languages.java = Prism.languages.extend('clike', {\n\t\t'string': {\n\t\t\tpattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true\n\t\t},\n\t\t'class-name': [\n\t\t\tclassName,\n\t\t\t{\n\t\t\t\t// variables, parameters, and constructor references\n\t\t\t\t// this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n\t\t\t\tpattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()]|\\s*(?:\\[[\\s,]*\\]\\s*)?::\\s*new\\b)/.source),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: className.inside\n\t\t\t},\n\t\t\t{\n\t\t\t\t// class names based on keyword\n\t\t\t\t// this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n\t\t\t\tpattern: RegExp(/(\\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\\s+)/.source + classNamePrefix + /[A-Z]\\w*\\b/.source),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: className.inside\n\t\t\t}\n\t\t],\n\t\t'keyword': keywords,\n\t\t'function': [\n\t\t\tPrism.languages.clike.function,\n\t\t\t{\n\t\t\t\tpattern: /(::\\s*)[a-z_]\\w*/,\n\t\t\t\tlookbehind: true\n\t\t\t}\n\t\t],\n\t\t'number': /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n\t\t'operator': {\n\t\t\tpattern: /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'constant': /\\b[A-Z][A-Z_\\d]+\\b/\n\t});\n\n\tPrism.languages.insertBefore('java', 'string', {\n\t\t'triple-quoted-string': {\n\t\t\t// http://openjdk.java.net/jeps/355#Description\n\t\t\tpattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n\t\t\tgreedy: true,\n\t\t\talias: 'string'\n\t\t},\n\t\t'char': {\n\t\t\tpattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n\t\t\tgreedy: true\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('java', 'class-name', {\n\t\t'annotation': {\n\t\t\tpattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n\t\t\tlookbehind: true,\n\t\t\talias: 'punctuation'\n\t\t},\n\t\t'generics': {\n\t\t\tpattern: /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n\t\t\tinside: {\n\t\t\t\t'class-name': className,\n\t\t\t\t'keyword': keywords,\n\t\t\t\t'punctuation': /[<>(),.:]/,\n\t\t\t\t'operator': /[?&|]/\n\t\t\t}\n\t\t},\n\t\t'import': [\n\t\t\t{\n\t\t\t\tpattern: RegExp(/(\\bimport\\s+)/.source + classNamePrefix + /(?:[A-Z]\\w*|\\*)(?=\\s*;)/.source),\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'namespace': className.inside.namespace,\n\t\t\t\t\t'punctuation': /\\./,\n\t\t\t\t\t'operator': /\\*/,\n\t\t\t\t\t'class-name': /\\w+/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: RegExp(/(\\bimport\\s+static\\s+)/.source + classNamePrefix + /(?:\\w+|\\*)(?=\\s*;)/.source),\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'static',\n\t\t\t\tinside: {\n\t\t\t\t\t'namespace': className.inside.namespace,\n\t\t\t\t\t'static': /\\b\\w+$/,\n\t\t\t\t\t'punctuation': /\\./,\n\t\t\t\t\t'operator': /\\*/,\n\t\t\t\t\t'class-name': /\\w+/\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'namespace': {\n\t\t\tpattern: RegExp(\n\t\t\t\t/(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/\n\t\t\t\t\t.source.replace(/<keyword>/g, function () { return keywords.source; })),\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'punctuation': /\\./,\n\t\t\t}\n\t\t}\n\t});\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAEjB,MAAI,WAAW;AAGf,MAAI,kBAAkB,6CAA6C;AAGnE,MAAI,YAAY;AAAA,IACf,SAAS,OAAO,aAAa,SAAS,kBAAkB,gCAAgC,MAAM;AAAA,IAC9F,YAAY;AAAA,IACZ,QAAQ;AAAA,MACP,aAAa;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,MACA,eAAe;AAAA,IAChB;AAAA,EACD;AAEA,EAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,SAAS;AAAA,IACtD,UAAU;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACb;AAAA,MACA;AAAA;AAAA;AAAA,QAGC,SAAS,OAAO,aAAa,SAAS,kBAAkB,+DAA+D,MAAM;AAAA,QAC7H,YAAY;AAAA,QACZ,QAAQ,UAAU;AAAA,MACnB;AAAA,MACA;AAAA;AAAA;AAAA,QAGC,SAAS,OAAO,kFAAkF,SAAS,kBAAkB,aAAa,MAAM;AAAA,QAChJ,YAAY;AAAA,QACZ,QAAQ,UAAU;AAAA,MACnB;AAAA,IACD;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,MACXA,OAAM,UAAU,MAAM;AAAA,MACtB;AAAA,QACC,SAAS;AAAA,QACT,YAAY;AAAA,MACb;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,YAAY;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,YAAY;AAAA,EACb,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,IAC9C,wBAAwB;AAAA;AAAA,MAEvB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,IACR;AAAA,IACA,QAAQ;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,IACT;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,QAAQ,cAAc;AAAA,IAClD,cAAc;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,YAAY;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,QACP,cAAc;AAAA,QACd,WAAW;AAAA,QACX,eAAe;AAAA,QACf,YAAY;AAAA,MACb;AAAA,IACD;AAAA,IACA,UAAU;AAAA,MACT;AAAA,QACC,SAAS,OAAO,gBAAgB,SAAS,kBAAkB,0BAA0B,MAAM;AAAA,QAC3F,YAAY;AAAA,QACZ,QAAQ;AAAA,UACP,aAAa,UAAU,OAAO;AAAA,UAC9B,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,cAAc;AAAA,QACf;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS,OAAO,yBAAyB,SAAS,kBAAkB,qBAAqB,MAAM;AAAA,QAC/F,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,UACP,aAAa,UAAU,OAAO;AAAA,UAC9B,UAAU;AAAA,UACV,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,cAAc;AAAA,QACf;AAAA,MACD;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,SAAS;AAAA,QACR,qJACE,OAAO,QAAQ,cAAc,WAAY;AAAE,iBAAO,SAAS;AAAA,QAAQ,CAAC;AAAA,MAAC;AAAA,MACxE,YAAY;AAAA,MACZ,QAAQ;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,EACD,CAAC;AACF,GAAE,KAAK;", "names": ["Prism"]}