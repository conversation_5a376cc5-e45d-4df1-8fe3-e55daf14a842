{"version": 3, "sources": ["../../prismjs/components/prism-go.js"], "sourcesContent": ["Prism.languages.go = Prism.languages.extend('clike', {\n\t'string': {\n\t\tpattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`[^`]*`/,\n\t\tlookbehind: true,\n\t\tgreedy: true\n\t},\n\t'keyword': /\\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\\b/,\n\t'boolean': /\\b(?:_|false|iota|nil|true)\\b/,\n\t'number': [\n\t\t// binary and octal integers\n\t\t/\\b0(?:b[01_]+|o[0-7_]+)i?\\b/i,\n\t\t// hexadecimal integers and floats\n\t\t/\\b0x(?:[a-f\\d_]+(?:\\.[a-f\\d_]*)?|\\.[a-f\\d_]+)(?:p[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i,\n\t\t// decimal integers and floats\n\t\t/(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?[\\d_]+)?i?(?!\\w)/i\n\t],\n\t'operator': /[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\./,\n\t'builtin': /\\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\\b/\n});\n\nPrism.languages.insertBefore('go', 'string', {\n\t'char': {\n\t\tpattern: /'(?:\\\\.|[^'\\\\\\r\\n]){0,10}'/,\n\t\tgreedy: true\n\t}\n});\n\ndelete Prism.languages.go['class-name'];\n"], "mappings": ";AAAA,MAAM,UAAU,KAAK,MAAM,UAAU,OAAO,SAAS;AAAA,EACpD,UAAU;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACT;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA;AAAA,IAET;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,EACD;AAAA,EACA,YAAY;AAAA,EACZ,WAAW;AACZ,CAAC;AAED,MAAM,UAAU,aAAa,MAAM,UAAU;AAAA,EAC5C,QAAQ;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,EACT;AACD,CAAC;AAED,OAAO,MAAM,UAAU,GAAG,YAAY;", "names": []}