{"version": 3, "sources": ["../../prismjs/components/prism-css.js"], "sourcesContent": ["(function (Prism) {\n\n\tvar string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n\n\tPrism.languages.css = {\n\t\t'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n\t\t'atrule': {\n\t\t\tpattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n\t\t\tinside: {\n\t\t\t\t'rule': /^@[\\w-]+/,\n\t\t\t\t'selector-function-argument': {\n\t\t\t\t\tpattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'selector'\n\t\t\t\t},\n\t\t\t\t'keyword': {\n\t\t\t\t\tpattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n\t\t\t\t\tlookbehind: true\n\t\t\t\t}\n\t\t\t\t// See rest below\n\t\t\t}\n\t\t},\n\t\t'url': {\n\t\t\t// https://drafts.csswg.org/css-values-3/#urls\n\t\t\tpattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^url/i,\n\t\t\t\t'punctuation': /^\\(|\\)$/,\n\t\t\t\t'string': {\n\t\t\t\t\tpattern: RegExp('^' + string.source + '$'),\n\t\t\t\t\talias: 'url'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'selector': {\n\t\t\tpattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': {\n\t\t\tpattern: string,\n\t\t\tgreedy: true\n\t\t},\n\t\t'property': {\n\t\t\tpattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'important': /!important\\b/i,\n\t\t'function': {\n\t\t\tpattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'punctuation': /[(){};:,]/\n\t};\n\n\tPrism.languages.css['atrule'].inside.rest = Prism.languages.css;\n\n\tvar markup = Prism.languages.markup;\n\tif (markup) {\n\t\tmarkup.tag.addInlined('style', 'css');\n\t\tmarkup.tag.addAttribute('style', 'css');\n\t}\n\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAEjB,MAAI,SAAS;AAEb,EAAAA,OAAM,UAAU,MAAM;AAAA,IACrB,WAAW;AAAA,IACX,UAAU;AAAA,MACT,SAAS,OAAO,eAAe,sBAAsB,SAAS,MAAM,OAAO,SAAS,QAAQ,kBAAkB,MAAM;AAAA,MACpH,QAAQ;AAAA,QACP,QAAQ;AAAA,QACR,8BAA8B;AAAA,UAC7B,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACR;AAAA,QACA,WAAW;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,QACb;AAAA;AAAA,MAED;AAAA,IACD;AAAA,IACA,OAAO;AAAA;AAAA,MAEN,SAAS,OAAO,iBAAiB,OAAO,SAAS,MAAM,8BAA8B,SAAS,QAAQ,GAAG;AAAA,MACzG,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,UAAU;AAAA,UACT,SAAS,OAAO,MAAM,OAAO,SAAS,GAAG;AAAA,UACzC,OAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,IACA,YAAY;AAAA,MACX,SAAS,OAAO,sDAAuD,OAAO,SAAS,eAAe;AAAA,MACtG,YAAY;AAAA,IACb;AAAA,IACA,UAAU;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,eAAe;AAAA,EAChB;AAEA,EAAAA,OAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,OAAOA,OAAM,UAAU;AAE5D,MAAI,SAASA,OAAM,UAAU;AAC7B,MAAI,QAAQ;AACX,WAAO,IAAI,WAAW,SAAS,KAAK;AACpC,WAAO,IAAI,aAAa,SAAS,KAAK;AAAA,EACvC;AAED,GAAE,KAAK;", "names": ["Prism"]}