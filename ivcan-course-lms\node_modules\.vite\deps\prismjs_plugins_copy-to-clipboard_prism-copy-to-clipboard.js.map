{"version": 3, "sources": ["../../prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.js"], "sourcesContent": ["(function () {\n\n\tif (typeof Prism === 'undefined' || typeof document === 'undefined') {\n\t\treturn;\n\t}\n\n\tif (!Prism.plugins.toolbar) {\n\t\tconsole.warn('Copy to Clipboard plugin loaded before Toolbar plugin.');\n\n\t\treturn;\n\t}\n\n\t/**\n\t * When the given elements is clicked by the user, the given text will be copied to clipboard.\n\t *\n\t * @param {HTMLElement} element\n\t * @param {CopyInfo} copyInfo\n\t *\n\t * @typedef CopyInfo\n\t * @property {() => string} getText\n\t * @property {() => void} success\n\t * @property {(reason: unknown) => void} error\n\t */\n\tfunction registerClipboard(element, copyInfo) {\n\t\telement.addEventListener('click', function () {\n\t\t\tcopyTextToClipboard(copyInfo);\n\t\t});\n\t}\n\n\t// https://stackoverflow.com/a/30810322/7595472\n\n\t/** @param {CopyInfo} copyInfo */\n\tfunction fallbackCopyTextToClipboard(copyInfo) {\n\t\tvar textArea = document.createElement('textarea');\n\t\ttextArea.value = copyInfo.getText();\n\n\t\t// Avoid scrolling to bottom\n\t\ttextArea.style.top = '0';\n\t\ttextArea.style.left = '0';\n\t\ttextArea.style.position = 'fixed';\n\n\t\tdocument.body.appendChild(textArea);\n\t\ttextArea.focus();\n\t\ttextArea.select();\n\n\t\ttry {\n\t\t\tvar successful = document.execCommand('copy');\n\t\t\tsetTimeout(function () {\n\t\t\t\tif (successful) {\n\t\t\t\t\tcopyInfo.success();\n\t\t\t\t} else {\n\t\t\t\t\tcopyInfo.error();\n\t\t\t\t}\n\t\t\t}, 1);\n\t\t} catch (err) {\n\t\t\tsetTimeout(function () {\n\t\t\t\tcopyInfo.error(err);\n\t\t\t}, 1);\n\t\t}\n\n\t\tdocument.body.removeChild(textArea);\n\t}\n\t/** @param {CopyInfo} copyInfo */\n\tfunction copyTextToClipboard(copyInfo) {\n\t\tif (navigator.clipboard) {\n\t\t\tnavigator.clipboard.writeText(copyInfo.getText()).then(copyInfo.success, function () {\n\t\t\t\t// try the fallback in case `writeText` didn't work\n\t\t\t\tfallbackCopyTextToClipboard(copyInfo);\n\t\t\t});\n\t\t} else {\n\t\t\tfallbackCopyTextToClipboard(copyInfo);\n\t\t}\n\t}\n\n\t/**\n\t * Selects the text content of the given element.\n\t *\n\t * @param {Element} element\n\t */\n\tfunction selectElementText(element) {\n\t\t// https://stackoverflow.com/a/20079910/7595472\n\t\twindow.getSelection().selectAllChildren(element);\n\t}\n\n\t/**\n\t * Traverses up the DOM tree to find data attributes that override the default plugin settings.\n\t *\n\t * @param {Element} startElement An element to start from.\n\t * @returns {Settings} The plugin settings.\n\t * @typedef {Record<\"copy\" | \"copy-error\" | \"copy-success\" | \"copy-timeout\", string | number>} Settings\n\t */\n\tfunction getSettings(startElement) {\n\t\t/** @type {Settings} */\n\t\tvar settings = {\n\t\t\t'copy': 'Copy',\n\t\t\t'copy-error': 'Press Ctrl+C to copy',\n\t\t\t'copy-success': 'Copied!',\n\t\t\t'copy-timeout': 5000\n\t\t};\n\n\t\tvar prefix = 'data-prismjs-';\n\t\tfor (var key in settings) {\n\t\t\tvar attr = prefix + key;\n\t\t\tvar element = startElement;\n\t\t\twhile (element && !element.hasAttribute(attr)) {\n\t\t\t\telement = element.parentElement;\n\t\t\t}\n\t\t\tif (element) {\n\t\t\t\tsettings[key] = element.getAttribute(attr);\n\t\t\t}\n\t\t}\n\t\treturn settings;\n\t}\n\n\tPrism.plugins.toolbar.registerButton('copy-to-clipboard', function (env) {\n\t\tvar element = env.element;\n\n\t\tvar settings = getSettings(element);\n\n\t\tvar linkCopy = document.createElement('button');\n\t\tlinkCopy.className = 'copy-to-clipboard-button';\n\t\tlinkCopy.setAttribute('type', 'button');\n\t\tvar linkSpan = document.createElement('span');\n\t\tlinkCopy.appendChild(linkSpan);\n\n\t\tsetState('copy');\n\n\t\tregisterClipboard(linkCopy, {\n\t\t\tgetText: function () {\n\t\t\t\treturn element.textContent;\n\t\t\t},\n\t\t\tsuccess: function () {\n\t\t\t\tsetState('copy-success');\n\n\t\t\t\tresetText();\n\t\t\t},\n\t\t\terror: function () {\n\t\t\t\tsetState('copy-error');\n\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tselectElementText(element);\n\t\t\t\t}, 1);\n\n\t\t\t\tresetText();\n\t\t\t}\n\t\t});\n\n\t\treturn linkCopy;\n\n\t\tfunction resetText() {\n\t\t\tsetTimeout(function () { setState('copy'); }, settings['copy-timeout']);\n\t\t}\n\n\t\t/** @param {\"copy\" | \"copy-error\" | \"copy-success\"} state */\n\t\tfunction setState(state) {\n\t\t\tlinkSpan.textContent = settings[state];\n\t\t\tlinkCopy.setAttribute('data-copy-state', state);\n\t\t}\n\t});\n}());\n"], "mappings": ";CAAC,WAAY;AAEZ,MAAI,OAAO,UAAU,eAAe,OAAO,aAAa,aAAa;AACpE;AAAA,EACD;AAEA,MAAI,CAAC,MAAM,QAAQ,SAAS;AAC3B,YAAQ,KAAK,wDAAwD;AAErE;AAAA,EACD;AAaA,WAAS,kBAAkB,SAAS,UAAU;AAC7C,YAAQ,iBAAiB,SAAS,WAAY;AAC7C,0BAAoB,QAAQ;AAAA,IAC7B,CAAC;AAAA,EACF;AAKA,WAAS,4BAA4B,UAAU;AAC9C,QAAI,WAAW,SAAS,cAAc,UAAU;AAChD,aAAS,QAAQ,SAAS,QAAQ;AAGlC,aAAS,MAAM,MAAM;AACrB,aAAS,MAAM,OAAO;AACtB,aAAS,MAAM,WAAW;AAE1B,aAAS,KAAK,YAAY,QAAQ;AAClC,aAAS,MAAM;AACf,aAAS,OAAO;AAEhB,QAAI;AACH,UAAI,aAAa,SAAS,YAAY,MAAM;AAC5C,iBAAW,WAAY;AACtB,YAAI,YAAY;AACf,mBAAS,QAAQ;AAAA,QAClB,OAAO;AACN,mBAAS,MAAM;AAAA,QAChB;AAAA,MACD,GAAG,CAAC;AAAA,IACL,SAAS,KAAK;AACb,iBAAW,WAAY;AACtB,iBAAS,MAAM,GAAG;AAAA,MACnB,GAAG,CAAC;AAAA,IACL;AAEA,aAAS,KAAK,YAAY,QAAQ;AAAA,EACnC;AAEA,WAAS,oBAAoB,UAAU;AACtC,QAAI,UAAU,WAAW;AACxB,gBAAU,UAAU,UAAU,SAAS,QAAQ,CAAC,EAAE,KAAK,SAAS,SAAS,WAAY;AAEpF,oCAA4B,QAAQ;AAAA,MACrC,CAAC;AAAA,IACF,OAAO;AACN,kCAA4B,QAAQ;AAAA,IACrC;AAAA,EACD;AAOA,WAAS,kBAAkB,SAAS;AAEnC,WAAO,aAAa,EAAE,kBAAkB,OAAO;AAAA,EAChD;AASA,WAAS,YAAY,cAAc;AAElC,QAAI,WAAW;AAAA,MACd,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,IACjB;AAEA,QAAI,SAAS;AACb,aAAS,OAAO,UAAU;AACzB,UAAI,OAAO,SAAS;AACpB,UAAI,UAAU;AACd,aAAO,WAAW,CAAC,QAAQ,aAAa,IAAI,GAAG;AAC9C,kBAAU,QAAQ;AAAA,MACnB;AACA,UAAI,SAAS;AACZ,iBAAS,GAAG,IAAI,QAAQ,aAAa,IAAI;AAAA,MAC1C;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAEA,QAAM,QAAQ,QAAQ,eAAe,qBAAqB,SAAU,KAAK;AACxE,QAAI,UAAU,IAAI;AAElB,QAAI,WAAW,YAAY,OAAO;AAElC,QAAI,WAAW,SAAS,cAAc,QAAQ;AAC9C,aAAS,YAAY;AACrB,aAAS,aAAa,QAAQ,QAAQ;AACtC,QAAI,WAAW,SAAS,cAAc,MAAM;AAC5C,aAAS,YAAY,QAAQ;AAE7B,aAAS,MAAM;AAEf,sBAAkB,UAAU;AAAA,MAC3B,SAAS,WAAY;AACpB,eAAO,QAAQ;AAAA,MAChB;AAAA,MACA,SAAS,WAAY;AACpB,iBAAS,cAAc;AAEvB,kBAAU;AAAA,MACX;AAAA,MACA,OAAO,WAAY;AAClB,iBAAS,YAAY;AAErB,mBAAW,WAAY;AACtB,4BAAkB,OAAO;AAAA,QAC1B,GAAG,CAAC;AAEJ,kBAAU;AAAA,MACX;AAAA,IACD,CAAC;AAED,WAAO;AAEP,aAAS,YAAY;AACpB,iBAAW,WAAY;AAAE,iBAAS,MAAM;AAAA,MAAG,GAAG,SAAS,cAAc,CAAC;AAAA,IACvE;AAGA,aAAS,SAAS,OAAO;AACxB,eAAS,cAAc,SAAS,KAAK;AACrC,eAAS,aAAa,mBAAmB,KAAK;AAAA,IAC/C;AAAA,EACD,CAAC;AACF,GAAE;", "names": []}