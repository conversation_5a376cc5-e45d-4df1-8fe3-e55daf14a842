{"version": 3, "sources": ["../../prismjs/components/prism-yaml.js"], "sourcesContent": ["(function (Prism) {\n\n\t// https://yaml.org/spec/1.2/spec.html#c-ns-anchor-property\n\t// https://yaml.org/spec/1.2/spec.html#c-ns-alias-node\n\tvar anchorOrAlias = /[*&][^\\s[\\]{},]+/;\n\t// https://yaml.org/spec/1.2/spec.html#c-ns-tag-property\n\tvar tag = /!(?:<[\\w\\-%#;/?:@&=+$,.!~*'()[\\]]+>|(?:[a-zA-Z\\d-]*!)?[\\w\\-%#;/?:@&=+$.~*'()]+)?/;\n\t// https://yaml.org/spec/1.2/spec.html#c-ns-properties(n,c)\n\tvar properties = '(?:' + tag.source + '(?:[ \\t]+' + anchorOrAlias.source + ')?|'\n\t\t+ anchorOrAlias.source + '(?:[ \\t]+' + tag.source + ')?)';\n\t// https://yaml.org/spec/1.2/spec.html#ns-plain(n,c)\n\t// This is a simplified version that doesn't support \"#\" and multiline keys\n\t// All these long scarry character classes are simplified versions of YAML's characters\n\tvar plainKey = /(?:[^\\s\\x00-\\x08\\x0e-\\x1f!\"#%&'*,\\-:>?@[\\]`{|}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]|[?:-]<PLAIN>)(?:[ \\t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source\n\t\t.replace(/<PLAIN>/g, function () { return /[^\\s\\x00-\\x08\\x0e-\\x1f,[\\]{}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]/.source; });\n\tvar string = /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/.source;\n\n\t/**\n\t *\n\t * @param {string} value\n\t * @param {string} [flags]\n\t * @returns {RegExp}\n\t */\n\tfunction createValuePattern(value, flags) {\n\t\tflags = (flags || '').replace(/m/g, '') + 'm'; // add m flag\n\t\tvar pattern = /([:\\-,[{]\\s*(?:\\s<<prop>>[ \\t]+)?)(?:<<value>>)(?=[ \\t]*(?:$|,|\\]|\\}|(?:[\\r\\n]\\s*)?#))/.source\n\t\t\t.replace(/<<prop>>/g, function () { return properties; }).replace(/<<value>>/g, function () { return value; });\n\t\treturn RegExp(pattern, flags);\n\t}\n\n\tPrism.languages.yaml = {\n\t\t'scalar': {\n\t\t\tpattern: RegExp(/([\\-:]\\s*(?:\\s<<prop>>[ \\t]+)?[|>])[ \\t]*(?:((?:\\r?\\n|\\r)[ \\t]+)\\S[^\\r\\n]*(?:\\2[^\\r\\n]+)*)/.source\n\t\t\t\t.replace(/<<prop>>/g, function () { return properties; })),\n\t\t\tlookbehind: true,\n\t\t\talias: 'string'\n\t\t},\n\t\t'comment': /#.*/,\n\t\t'key': {\n\t\t\tpattern: RegExp(/((?:^|[:\\-,[{\\r\\n?])[ \\t]*(?:<<prop>>[ \\t]+)?)<<key>>(?=\\s*:\\s)/.source\n\t\t\t\t.replace(/<<prop>>/g, function () { return properties; })\n\t\t\t\t.replace(/<<key>>/g, function () { return '(?:' + plainKey + '|' + string + ')'; })),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\talias: 'atrule'\n\t\t},\n\t\t'directive': {\n\t\t\tpattern: /(^[ \\t]*)%.+/m,\n\t\t\tlookbehind: true,\n\t\t\talias: 'important'\n\t\t},\n\t\t'datetime': {\n\t\t\tpattern: createValuePattern(/\\d{4}-\\d\\d?-\\d\\d?(?:[tT]|[ \\t]+)\\d\\d?:\\d{2}:\\d{2}(?:\\.\\d*)?(?:[ \\t]*(?:Z|[-+]\\d\\d?(?::\\d{2})?))?|\\d{4}-\\d{2}-\\d{2}|\\d\\d?:\\d{2}(?::\\d{2}(?:\\.\\d*)?)?/.source),\n\t\t\tlookbehind: true,\n\t\t\talias: 'number'\n\t\t},\n\t\t'boolean': {\n\t\t\tpattern: createValuePattern(/false|true/.source, 'i'),\n\t\t\tlookbehind: true,\n\t\t\talias: 'important'\n\t\t},\n\t\t'null': {\n\t\t\tpattern: createValuePattern(/null|~/.source, 'i'),\n\t\t\tlookbehind: true,\n\t\t\talias: 'important'\n\t\t},\n\t\t'string': {\n\t\t\tpattern: createValuePattern(string),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true\n\t\t},\n\t\t'number': {\n\t\t\tpattern: createValuePattern(/[+-]?(?:0x[\\da-f]+|0o[0-7]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|\\.inf|\\.nan)/.source, 'i'),\n\t\t\tlookbehind: true\n\t\t},\n\t\t'tag': tag,\n\t\t'important': anchorOrAlias,\n\t\t'punctuation': /---|[:[\\]{}\\-,|>?]|\\.\\.\\./\n\t};\n\n\tPrism.languages.yml = Prism.languages.yaml;\n\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAIjB,MAAI,gBAAgB;AAEpB,MAAI,MAAM;AAEV,MAAI,aAAa,QAAQ,IAAI,SAAS,aAAc,cAAc,SAAS,QACxE,cAAc,SAAS,aAAc,IAAI,SAAS;AAIrD,MAAI,WAAW,kJAAkJ,OAC/J,QAAQ,YAAY,WAAY;AAAE,WAAO,2EAA2E;AAAA,EAAQ,CAAC;AAC/H,MAAI,SAAS,8CAA8C;AAQ3D,WAAS,mBAAmB,OAAO,OAAO;AACzC,aAAS,SAAS,IAAI,QAAQ,MAAM,EAAE,IAAI;AAC1C,QAAI,UAAU,yFAAyF,OACrG,QAAQ,aAAa,WAAY;AAAE,aAAO;AAAA,IAAY,CAAC,EAAE,QAAQ,cAAc,WAAY;AAAE,aAAO;AAAA,IAAO,CAAC;AAC9G,WAAO,OAAO,SAAS,KAAK;AAAA,EAC7B;AAEA,EAAAA,OAAM,UAAU,OAAO;AAAA,IACtB,UAAU;AAAA,MACT,SAAS,OAAO,6FAA6F,OAC3G,QAAQ,aAAa,WAAY;AAAE,eAAO;AAAA,MAAY,CAAC,CAAC;AAAA,MAC1D,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,WAAW;AAAA,IACX,OAAO;AAAA,MACN,SAAS,OAAO,kEAAkE,OAChF,QAAQ,aAAa,WAAY;AAAE,eAAO;AAAA,MAAY,CAAC,EACvD,QAAQ,YAAY,WAAY;AAAE,eAAO,QAAQ,WAAW,MAAM,SAAS;AAAA,MAAK,CAAC,CAAC;AAAA,MACpF,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,IACR;AAAA,IACA,aAAa;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,YAAY;AAAA,MACX,SAAS,mBAAmB,sJAAsJ,MAAM;AAAA,MACxL,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACV,SAAS,mBAAmB,aAAa,QAAQ,GAAG;AAAA,MACpD,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,QAAQ;AAAA,MACP,SAAS,mBAAmB,SAAS,QAAQ,GAAG;AAAA,MAChD,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,UAAU;AAAA,MACT,SAAS,mBAAmB,MAAM;AAAA,MAClC,YAAY;AAAA,MACZ,QAAQ;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACT,SAAS,mBAAmB,iFAAiF,QAAQ,GAAG;AAAA,MACxH,YAAY;AAAA,IACb;AAAA,IACA,OAAO;AAAA,IACP,aAAa;AAAA,IACb,eAAe;AAAA,EAChB;AAEA,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AAEvC,GAAE,KAAK;", "names": ["Prism"]}