import { supabase } from '@/integrations/supabase/client';
import { Course, Module, Lesson, RawLesson, CourseCreateInput, ModuleCreateInput, LessonCreateInput } from './types';
import { convertLesson } from './utils';
import { toast } from 'sonner';
import { markCourseAsCompleted } from './enrollmentApi';
import { Lesson as LessonType } from '@/types/lesson';

// Update the table references
const TABLES = {
  MODULES: 'modules',
  LESSONS: 'lessons',
  COURSES: 'courses',
  MODULE_PROGRESS: 'user_module_progress',
  LESSON_PROGRESS: 'user_lesson_progress',
  COURSE_ENROLLMENT: 'user_course_enrollment'
} as const;

// Fetch a course by slug
export const fetchCourseBySlug = async (slug: string): Promise<Course | null> => {
  console.log('Fetching course by slug:', slug);

  if (!slug) {
    console.error('No slug provided to fetchCourseBySlug');
    return null;
  }

  try {
    // First try to fetch by slug
    const { data, error } = await supabase
      .from(TABLES.COURSES)
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) {
      console.error('Error fetching course by slug:', error);

      // If not found by slug, try by ID (in case the slug is actually an ID)
      if (error.code === 'PGRST116') {
        console.log('Course not found by slug, trying as ID');
        const { data: idData, error: idError } = await supabase
          .from(TABLES.COURSES)
          .select('*')
          .eq('id', slug)
          .single();

        if (idError) {
          console.error('Error fetching course by ID:', idError);
          return null;
        }

        console.log('Course found by ID:', idData);
        return idData;
      }

      return null;
    }

    console.log('Course found by slug:', data);
    return data;
  } catch (error) {
    console.error('Unexpected error in fetchCourseBySlug:', error);
    return null;
  }
};

// Fetch a course by ID
export const fetchCourseById = async (id: string): Promise<Course | null> => {
  if (!id) return null;

  const { data, error } = await supabase
    .from(TABLES.COURSES)
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching course by ID:', error);
    return null;
  }

  return data;
};

// Fetch modules for a course with their lessons, including user progress
export const fetchCourseModules = async (courseId: string, userId?: string): Promise<Module[]> => {
  console.log(`Fetching modules for course ${courseId}, user: ${userId || 'none'}`);

  if (!courseId) {
    console.error('No courseId provided to fetchCourseModules');
    return [];
  }

  let moduleData: any[] = [];

  try {
    // Log the query we're about to make
    console.log(`Making Supabase query: SELECT * FROM modules WHERE course_id = '${courseId}' ORDER BY module_number ASC`);

    // We'll explicitly include the is_published condition to ensure we get all modules
    // that are either published or not published (since we're using RLS policies)
    const { data, error } = await supabase
      .from(TABLES.MODULES)
      .select('*')
      .eq('course_id', courseId)
      .order('module_number', { ascending: true });

    if (error) {
      console.error('Error fetching modules:', error);
      return [];
    }

    moduleData = data || [];

    // For each module, fetch its lessons and any user progress
    const modulesWithLessons = await Promise.all(moduleData.map(async (module) => {
      // First fetch the lessons for this module
      const { data: lessonData, error: lessonError } = await supabase
        .from(TABLES.LESSONS)
        .select('*')
        .eq('module_id', module.id)
        .order('created_at', { ascending: true }); // Order lessons by creation time, oldest first

      if (lessonError) {
        console.error('Error fetching lessons:', lessonError);
        return { ...module, lessons: [] };
      }

      // Convert raw lessons to typed lessons
      let typedLessons = lessonData ? lessonData.map(convertLesson) : [];

      // Reset completion status to ensure new users start with no completed lessons
      let moduleIsCompleted = false;

      // If a user is logged in, fetch their progress for these lessons and module
      if (userId) {
        // First, check if the user has already completed this module
        const { data: moduleProgressData, error: moduleProgressError } = await supabase
          .from(TABLES.MODULE_PROGRESS)
          .select('is_completed')
          .eq('user_id', userId)
          .eq('module_id', module.id)
          .maybeSingle();

        if (moduleProgressError) {
          console.error('Error fetching module progress:', moduleProgressError);
        } else if (moduleProgressData) {
          // If we have a record, use that to determine if the module is completed
          moduleIsCompleted = moduleProgressData.is_completed;
        }

        // Fetch lesson progress
        const { data: progressData } = await supabase
          .from(TABLES.LESSON_PROGRESS)
          .select('*')
          .eq('user_id', userId)
          .in('lesson_id', typedLessons.map(lesson => lesson.id));

        if (progressData && progressData.length > 0) {
          // Update the completed status based on user progress
          typedLessons = typedLessons.map(lesson => {
            const progress = progressData.find(p => p.lesson_id === lesson.id);
            return {
              ...lesson,
              completed: progress ? progress.is_completed : false
            };
          });

          // Check if all lessons are completed to mark module as completed
          const allCompleted = typedLessons.length > 0 && typedLessons.every(lesson => lesson.completed);

          // If all lessons are completed but module is not marked as completed, update it
          if (allCompleted && !moduleIsCompleted) {
            await updateModuleCompletion(module.id, userId, true);
            moduleIsCompleted = true;
          }
        } else {
          // No progress data found, ensure all lessons are marked as not completed
          typedLessons = typedLessons.map(lesson => ({
            ...lesson,
            completed: false
          }));
        }
      } else {
        // No user logged in, ensure all lessons are marked as not completed
        typedLessons = typedLessons.map(lesson => ({
          ...lesson,
          completed: false
        }));
      }

      // Check module access based on user progress and role
      let moduleIsLocked = module.is_locked;

      if (userId) {
        try {
          console.log(`[COURSE API] Checking access for module ${module.id} (${module.title}) for user ${userId}`);
          // Import access control service
          const { checkModuleAccess } = await import('./accessControlService');
          const accessResult = await checkModuleAccess(userId, module.id);
          moduleIsLocked = !accessResult.hasAccess;
          console.log(`[COURSE API] Module ${module.id} access result: ${accessResult.hasAccess}, locked: ${moduleIsLocked}`);
        } catch (accessError) {
          console.error('[COURSE API] Error checking module access, defaulting to unlocked:', accessError);
          // If access check fails, default to the original module lock status
          moduleIsLocked = module.is_locked;
        }
      } else {
        // For non-authenticated users, only module 1 is accessible
        moduleIsLocked = module.module_number !== 1;
        console.log(`[COURSE API] Non-authenticated user, module ${module.module_number} locked: ${moduleIsLocked}`);
      }

      return {
        id: module.id,
        course_id: module.course_id,
        slug: module.slug,
        module_number: module.module_number,
        title: module.title,
        is_locked: moduleIsLocked, // Use the calculated lock status
        is_completed: moduleIsCompleted, // Use the module progress status
        lessons: typedLessons
      };
    }));

    // Update course completion count if user is logged in
    if (userId) {
      const completedModulesCount = modulesWithLessons.filter(m => m.is_completed).length;
      const totalModulesCount = modulesWithLessons.length;

      // Update the course's completed modules count
      await updateCourseProgress(courseId, completedModulesCount, totalModulesCount);
    }

    return modulesWithLessons;
  } catch (error) {
    console.error('Unexpected error in fetchCourseModules:', error);
    return [];
  }
};

// Fetch a single lesson by slug with user progress
export const fetchLessonBySlug = async (
  slug: string,
  userId?: string | null
): Promise<LessonType> => {
  console.log(`[COURSE API] Fetching lesson by slug: ${slug}, userId: ${userId}`);

  if (!slug) {
    throw new Error("No lesson slug provided");
  }

  try {
    // Check if the slug is actually a UUID
    const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(slug);

    // If it's a UUID, try to fetch directly by ID first
    if (isUuid) {
      console.log(`[COURSE API] Slug appears to be a UUID, trying direct ID fetch`);
      const { data: idLesson, error: idError } = await supabase
        .from('lessons')
        .select(`
          id,
          title,
          slug,
          content,
          type,
          module_id,
          modules:module_id (title),
          order,
          created_at,
          updated_at,
          image_url,
          video_url,
          external_url,
          duration_minutes,
          description
        `)
        .eq('id', slug)
        .single();

      if (!idError && idLesson) {
        console.log(`[COURSE API] Lesson found by ID: ${idLesson.id}`);
        // Format and return the lesson
        const formattedLesson = await formatLessonWithNavigation(idLesson, userId);
        return formattedLesson;
      }

      // If not found by ID, continue to slug lookup
      console.log(`[COURSE API] Lesson not found by ID, trying by slug`);
    }

    // Standard query by slug
    const { data: lesson, error } = await supabase
      .from('lessons')
      .select(`
        id,
        title,
        slug,
        content,
        type,
        module_id,
        modules:module_id (title),
        order,
        created_at,
        updated_at,
        image_url,
        video_url,
        external_url,
        duration_minutes,
        description
      `)
      .eq('slug', slug)
      .single();

    if (error) {
      console.error('[COURSE API] Error fetching lesson by slug:', error);
      console.error('[COURSE API] Error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });
      throw new Error(`Failed to fetch lesson: ${error.message} (Code: ${error.code})`);
    }

    if (!lesson) {
      console.error('[COURSE API] No lesson data returned');
      throw new Error('Lesson not found');
    }

    console.log(`[COURSE API] Lesson found by slug: ${lesson.id} - ${lesson.title}`);

    // Format and return the lesson with navigation info
    const formattedLesson = await formatLessonWithNavigation(lesson, userId);
    return formattedLesson;
  } catch (error: any) {
    console.error('[COURSE API] Unexpected error in fetchLessonBySlug:', error);

    // Check if it's an AbortError
    if (error.name === 'AbortError') {
      throw new Error('Request was cancelled - this might be due to a network timeout or connection issue');
    }

    // Check if it's a network error
    if (error.message?.includes('fetch')) {
      throw new Error('Network error - please check your internet connection and try again');
    }

    throw new Error(`Error fetching lesson: ${error.message}`);
  }
};

// Helper function to format a lesson with navigation and completion status
async function formatLessonWithNavigation(lesson: any, userId?: string | null): Promise<LessonType> {
  // Format the lesson with proper structure
  const formattedLesson: LessonType = {
    ...lesson,
    module_title: lesson.modules?.title,
    completed: false,
    previous_lesson_slug: null,
    next_lesson_slug: null,
  };

  // Check completion status and access if userId provided
  if (userId) {
    try {
      const { data: progressData } = await supabase
        .from('user_lesson_progress')
        .select('is_completed')
        .eq('user_id', userId)
        .eq('lesson_id', lesson.id)
        .single();

      formattedLesson.completed = progressData?.is_completed || false;

      // Check lesson access
      const { checkLessonAccess } = await import('./accessControlService');
      const accessResult = await checkLessonAccess(userId, lesson.id);
      formattedLesson.is_locked = !accessResult.hasAccess;
    } catch (progressError) {
      console.error('[COURSE API] Error checking lesson completion:', progressError);
      // Don't fail the whole request if completion status check fails
    }
  } else {
    // For non-authenticated users, check if this is lesson 1 in module 1
    try {
      const { data: moduleData } = await supabase
        .from('modules')
        .select('module_number')
        .eq('id', lesson.module_id)
        .single();

      const { data: lessonData } = await supabase
        .from('lessons')
        .select('lesson_number')
        .eq('id', lesson.id)
        .single();

      // Only first lesson of first module is accessible to non-authenticated users
      formattedLesson.is_locked = !(moduleData?.module_number === 1 && lessonData?.lesson_number === 1);
    } catch (error) {
      console.error('[COURSE API] Error checking lesson access for non-authenticated user:', error);
      formattedLesson.is_locked = true; // Default to locked if we can't determine
    }
  }

  // Fetch navigation (previous and next lessons)
  try {
    const { data: navData } = await supabase.rpc('get_lesson_navigation', {
      p_current_lesson_id: lesson.id,
      p_module_id: lesson.module_id
    });

    if (navData) {
      formattedLesson.previous_lesson_slug = navData.previous_slug;
      formattedLesson.next_lesson_slug = navData.next_slug;
    }
  } catch (navError) {
    console.error('[COURSE API] Error fetching lesson navigation:', navError);
    // Don't fail the whole request if navigation fails
  }

  return formattedLesson;
}

// Fallback method to update module completion directly in the modules table
const updateModuleCompletionFallback = async (moduleId: string, completed: boolean): Promise<boolean> => {
  try {
    console.log(`Using fallback method to update module ${moduleId} completion to ${completed}`);

    const { error } = await supabase
      .from(TABLES.MODULES)
      .update({
        is_completed: completed,
        updated_at: new Date().toISOString()
      })
      .eq('id', moduleId);

    if (error) {
      console.error('Error in fallback module update:', error);
      return false;
    }

    console.log(`Fallback method successfully updated module ${moduleId}`);
    return true;
  } catch (error) {
    console.error('Unexpected error in fallback update:', error);
    return false;
  }
};

// Update module completion status for a specific user
export const updateModuleCompletion = async (moduleId: string, userId: string, completed: boolean): Promise<boolean> => {
  try {
    console.log(`Updating module completion for module ${moduleId} and user ${userId} to ${completed}`);

    if (!moduleId || !userId) {
      console.error('Missing moduleId or userId in updateModuleCompletion');
      return false;
    }

    // Update the user_module_progress table
    const now = new Date().toISOString();
    const { error: upsertError } = await supabase
      .from(TABLES.MODULE_PROGRESS)
      .upsert({
        user_id: userId,
        module_id: moduleId,
        is_completed: completed,
        completed_at: completed ? now : null,
        updated_at: now
      }, {
        onConflict: 'user_id,module_id',
        ignoreDuplicates: false
      });

    if (upsertError) {
      console.error('Error updating module completion:', upsertError);
      return false;
    }

    // Get the course ID for this module to update course completion
    const { data: moduleData, error: moduleError } = await supabase
      .from(TABLES.MODULES)
      .select('course_id')
      .eq('id', moduleId)
      .single();

    if (moduleError || !moduleData) {
      console.error('Error getting course ID for module:', moduleError);
      return false;
    }

    // Check if all modules in the course are completed
    const { data: moduleProgress, error: progressError } = await supabase
      .from(TABLES.MODULE_PROGRESS)
      .select('is_completed')
      .eq('user_id', userId)
      .eq('course_id', moduleData.course_id);

    if (!progressError && moduleProgress) {
      const allModulesCompleted = moduleProgress.every(m => m.is_completed);
      if (allModulesCompleted) {
        // Update course enrollment status
        await supabase
          .from(TABLES.COURSE_ENROLLMENT)
          .upsert({
            user_id: userId,
            course_id: moduleData.course_id,
            status: 'completed',
            completed_at: now,
            updated_at: now
          }, {
            onConflict: 'user_id,course_id',
            ignoreDuplicates: false
          });
      }
    }

    return true;
  } catch (error) {
    console.error('Unexpected error in updateModuleCompletion:', error);
    return false;
  }
};

// Update course progress
export const updateCourseProgress = async (
  courseId: string,
  completedModules: number,
  totalModules: number
): Promise<boolean> => {
  try {
    console.log(`Updating course progress for course ${courseId}: ${completedModules}/${totalModules} modules completed`);

    if (!courseId) {
      console.error('Missing courseId in updateCourseProgress');
      return false;
    }

    const { error } = await supabase
      .from(TABLES.COURSES)
      .update({
        completed_modules: completedModules,
        total_modules: totalModules,
        updated_at: new Date().toISOString()
      })
      .eq('id', courseId);

    if (error) {
      console.error('Error updating course progress:', error);
      toast.error('Failed to update course progress');
      return false;
    }

    console.log(`Successfully updated course ${courseId} progress: ${completedModules}/${totalModules}`);

    // If all modules are completed, mark the course as completed
    if (completedModules > 0 && completedModules === totalModules) {
      console.log(`All modules completed for course ${courseId}, marking course as completed`);
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await markCourseAsCompleted(courseId, user.id);
      }
    }

    return true;
  } catch (error) {
    console.error('Unexpected error in updateCourseProgress:', error);
    toast.error('An unexpected error occurred while updating course progress');
    return false;
  }
};

// Fetch all courses
export const fetchAllCourses = async (): Promise<Course[]> => {
  const { data, error } = await supabase
    .from(TABLES.COURSES)
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching all courses:', error);
    return [];
  }

  return data || [];
};

// Fetch courses for a specific user (enrolled courses)
export const fetchUserCourses = async (userId: string): Promise<Course[]> => {
  const { data, error } = await supabase
    .from('user_course_enrollment')
    .select('course_id, status, courses(*)')
    .eq('user_id', userId);

  if (error) {
    console.error('Error fetching user courses:', error);
    return [];
  }

  // Extract the course data from the joined query
  return data?.map(item => ({
    ...item.courses,
    enrollment_status: item.status
  })) || [];
};

// Create a new course
export const createCourse = async (courseData: CourseCreateInput): Promise<Course | null> => {
  try {
    console.log('Creating course with data:', courseData);

    const { data, error } = await supabase
      .from(TABLES.COURSES)
      .insert([{
        title: courseData.title,
        slug: courseData.slug,
        description: courseData.description,
        instructor: courseData.instructor,
        image_url: courseData.image_url || null,
        total_modules: 0,
        completed_modules: 0
      }])
      .select();

    if (error) {
      console.error('Error creating course:', error);
      toast.error(`Failed to create course: ${error.message}`);
      return null;
    }

    console.log('Course created successfully:', data[0]);
    toast.success('Course created successfully');
    return data[0];
  } catch (error: any) {
    console.error('Unexpected error creating course:', error);
    toast.error(`Failed to create course: ${error.message}`);
    return null;
  }
};

// Update an existing course
export const updateCourse = async (courseId: string, courseData: Partial<CourseCreateInput>): Promise<Course | null> => {
  try {
    console.log('Updating course with ID:', courseId, 'Data:', courseData);

    // First, check if the course exists
    const { data: existingCourse, error: fetchError } = await supabase
      .from(TABLES.COURSES)
      .select('*')
      .eq('id', courseId)
      .single();

    if (fetchError) {
      console.error('Error fetching course for update:', fetchError);
      toast.error(`Failed to update course: ${fetchError.message}`);
      return null;
    }

    if (!existingCourse) {
      console.error('Course not found for update:', courseId);
      toast.error('Course not found');
      return null;
    }

    // Prepare update data
    const updateData = {
      ...courseData,
      updated_at: new Date().toISOString()
    };

    // Make sure image_url is properly handled
    if (courseData.image_url === undefined) {
      // Keep existing image_url if not provided
      updateData.image_url = existingCourse.image_url;
    } else if (courseData.image_url === null) {
      // Allow explicitly setting to null
      updateData.image_url = null;
    }

    const { data, error } = await supabase
      .from(TABLES.COURSES)
      .update(updateData)
      .eq('id', courseId)
      .select();

    if (error) {
      console.error('Error updating course:', error);
      toast.error(`Failed to update course: ${error.message}`);
      return null;
    }

    console.log('Course updated successfully:', data[0]);
    toast.success('Course updated successfully');
    return data[0];
  } catch (error: any) {
    console.error('Unexpected error updating course:', error);
    toast.error(`Failed to update course: ${error.message}`);
    return null;
  }
};

// Create a new module
export const createModule = async (moduleData: ModuleCreateInput): Promise<Module | null> => {
  try {
    console.log('Creating module:', moduleData);

    const { data, error } = await supabase
      .from(TABLES.MODULES)
      .insert([{
        course_id: moduleData.course_id,
        title: moduleData.title,
        slug: moduleData.slug,
        module_number: moduleData.module_number,
        is_locked: moduleData.is_locked || false,
        is_completed: false
        // Removed is_published field as it doesn't exist in the schema
      }])
      .select();

    if (error) {
      console.error('Error creating module:', error);
      toast.error(`Failed to create module: ${error.message}`);
      return null;
    }

    // Update the course's total_modules count
    try {
      console.log('Updating course total_modules count');

      // First, get the current count of modules for this course
      const { data: moduleCount, error: countError } = await supabase
        .from(TABLES.MODULES)
        .select('id')
        .eq('course_id', moduleData.course_id);

      if (countError) {
        console.error('Error counting modules:', countError);
      } else {
        // Update the course with the new count
        const totalModules = moduleCount.length;
        console.log(`Updating course ${moduleData.course_id} with ${totalModules} total modules`);

        const { error: updateError } = await supabase
          .from(TABLES.COURSES)
          .update({ total_modules: totalModules })
          .eq('id', moduleData.course_id);

        if (updateError) {
          console.error('Error updating course total_modules:', updateError);
        } else {
          console.log('Course total_modules updated successfully');
        }
      }
    } catch (updateError) {
      console.error('Error updating course total_modules:', updateError);
      // Continue anyway as this is not critical
    }

    toast.success('Module created successfully');
    return data[0];
  } catch (error: any) {
    console.error('Unexpected error creating module:', error);
    toast.error(`Failed to create module: ${error.message}`);
    return null;
  }
};

// Update an existing module
export const updateModule = async (moduleId: string, moduleData: Partial<ModuleCreateInput>): Promise<Module | null> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.MODULES)
      .update({
        ...moduleData,
        updated_at: new Date().toISOString()
      })
      .eq('id', moduleId)
      .select();

    if (error) {
      console.error('Error updating module:', error);
      toast.error(`Failed to update module: ${error.message}`);
      return null;
    }

    toast.success('Module updated successfully');
    return data[0];
  } catch (error: any) {
    console.error('Unexpected error updating module:', error);
    toast.error(`Failed to update module: ${error.message}`);
    return null;
  }
};

// Create a new lesson
export const createLesson = async (lessonData: LessonCreateInput): Promise<Lesson | null> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LESSONS)
      .insert([{
        module_id: lessonData.module_id,
        title: lessonData.title,
        slug: lessonData.slug,
        duration: lessonData.duration,
        type: lessonData.type || 'lesson',
        content: lessonData.content || null,
        requirement: lessonData.requirement || null,
        completed: false
      }])
      .select();

    if (error) {
      console.error('Error creating lesson:', error);
      toast.error(`Failed to create lesson: ${error.message}`);
      return null;
    }

    toast.success('Lesson created successfully');
    return convertLesson(data[0] as RawLesson);
  } catch (error: any) {
    console.error('Unexpected error creating lesson:', error);
    toast.error(`Failed to create lesson: ${error.message}`);
    return null;
  }
};

// Update an existing lesson
export const updateLesson = async (lessonId: string, lessonData: Partial<LessonCreateInput>): Promise<Lesson | null> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LESSONS)
      .update({
        ...lessonData,
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId)
      .select();

    if (error) {
      console.error('Error updating lesson:', error);
      toast.error(`Failed to update lesson: ${error.message}`);
      return null;
    }

    toast.success('Lesson updated successfully');
    return convertLesson(data[0] as RawLesson);
  } catch (error: any) {
    console.error('Unexpected error updating lesson:', error);
    toast.error(`Failed to update lesson: ${error.message}`);
    return null;
  }
};

// Get the first lesson of a module
export const getFirstLessonOfModule = async (
  moduleId: string
): Promise<{ firstLessonSlug: string | null }> => {
  try {
    console.log('[GET FIRST LESSON] Getting first lesson for module:', moduleId);

    const { data: firstLesson, error } = await supabase
      .from('lessons')
      .select('slug')
      .eq('module_id', moduleId)
      .order('created_at', { ascending: true })
      .limit(1)
      .single();

    if (error || !firstLesson) {
      console.error('[GET FIRST LESSON] No lessons found for module:', moduleId, error);
      return { firstLessonSlug: null };
    }

    console.log('[GET FIRST LESSON] First lesson found:', firstLesson.slug);
    return { firstLessonSlug: firstLesson.slug };
  } catch (error: any) {
    console.error('[GET FIRST LESSON] Error getting first lesson:', error);
    return { firstLessonSlug: null };
  }
};

// Simple function to find next lesson by getting all lessons in order
export const findNextLessonSimple = async (
  currentLessonSlug: string
): Promise<{ nextLessonSlug: string | null; isLastLesson: boolean }> => {
  try {
    console.log('[FIND NEXT LESSON SIMPLE] Starting search for lesson:', currentLessonSlug);

    // Get the current lesson to find its course - using a simpler approach
    const { data: currentLesson, error: lessonError } = await supabase
      .from('lessons')
      .select('id, slug, module_id')
      .eq('slug', currentLessonSlug)
      .single();

    if (lessonError || !currentLesson) {
      console.error('[FIND NEXT LESSON SIMPLE] Failed to find current lesson:', lessonError);
      throw new Error(`Could not find current lesson: ${lessonError?.message || 'Unknown error'}`);
    }

    console.log('[FIND NEXT LESSON SIMPLE] Current lesson found:', currentLesson);

    // Get the module to find the course
    const { data: currentModule, error: moduleError } = await supabase
      .from('modules')
      .select('course_id, module_number')
      .eq('id', currentLesson.module_id)
      .single();

    if (moduleError || !currentModule) {
      console.error('[FIND NEXT LESSON SIMPLE] Failed to find current module:', moduleError);
      throw new Error(`Could not find current module: ${moduleError?.message || 'Unknown error'}`);
    }

    console.log('[FIND NEXT LESSON SIMPLE] Current module found:', currentModule);

    // Get all modules for this course first
    const { data: allModules, error: modulesError } = await supabase
      .from('modules')
      .select('id, module_number')
      .eq('course_id', currentModule.course_id)
      .order('module_number', { ascending: true });

    if (modulesError || !allModules) {
      console.error('[FIND NEXT LESSON SIMPLE] Failed to get modules:', modulesError);
      throw new Error(`Could not get course modules: ${modulesError?.message || 'Unknown error'}`);
    }

    console.log('[FIND NEXT LESSON SIMPLE] Modules found:', allModules.length);

    // Get all lessons for each module and build the ordered list
    const allLessons: { slug: string; created_at: string }[] = [];

    for (const module of allModules) {
      const { data: moduleLessons, error: lessonsError } = await supabase
        .from('lessons')
        .select('slug, created_at')
        .eq('module_id', module.id)
        .order('created_at', { ascending: true });

      if (lessonsError) {
        console.error('[FIND NEXT LESSON SIMPLE] Failed to get lessons for module:', module.id, lessonsError);
        continue; // Skip this module but continue with others
      }

      if (moduleLessons && moduleLessons.length > 0) {
        allLessons.push(...moduleLessons);
      }
    }

    console.log('[FIND NEXT LESSON SIMPLE] Total lessons found:', allLessons.length);
    console.log('[FIND NEXT LESSON SIMPLE] All lesson slugs:', allLessons.map(l => l.slug));

    // Find the current lesson index
    const currentIndex = allLessons.findIndex(lesson => lesson.slug === currentLessonSlug);

    if (currentIndex === -1) {
      console.error('[FIND NEXT LESSON SIMPLE] Current lesson not found in list. Current:', currentLessonSlug);
      console.error('[FIND NEXT LESSON SIMPLE] Available lessons:', allLessons.map(l => l.slug));
      throw new Error('Current lesson not found in course lessons');
    }

    console.log('[FIND NEXT LESSON SIMPLE] Current lesson index:', currentIndex, 'of', allLessons.length);

    // Check if there's a next lesson
    const isLastLesson = currentIndex === allLessons.length - 1;
    const nextLessonSlug = isLastLesson ? null : allLessons[currentIndex + 1].slug;

    console.log('[FIND NEXT LESSON SIMPLE] Result:', { nextLessonSlug, isLastLesson });

    return {
      nextLessonSlug,
      isLastLesson
    };

  } catch (error: any) {
    console.error('[FIND NEXT LESSON SIMPLE] Error:', error);
    return {
      nextLessonSlug: null,
      isLastLesson: true
    };
  }
};

// Enhanced navigation that includes tests in the sequence
export const findNextItemInCourse = async (
  currentLessonSlug: string,
  userId: string
): Promise<{
  nextItemType: 'lesson' | 'pre_test' | 'post_test' | null;
  nextItemSlug: string | null;
  nextModuleId?: string;
  isLastItem: boolean;
}> => {
  try {
    console.log('[FIND NEXT ITEM] Starting search for lesson:', currentLessonSlug);

    // Get the current lesson
    const { data: currentLesson, error: lessonError } = await supabase
      .from('lessons')
      .select('id, slug, module_id')
      .eq('slug', currentLessonSlug)
      .single();

    if (lessonError || !currentLesson) {
      console.error('[FIND NEXT ITEM] Failed to find current lesson:', lessonError);
      return { nextItemType: null, nextItemSlug: null, isLastItem: true };
    }

    // Get the current module
    const { data: currentModule, error: moduleError } = await supabase
      .from('modules')
      .select('course_id, module_number')
      .eq('id', currentLesson.module_id)
      .single();

    if (moduleError || !currentModule) {
      console.error('[FIND NEXT ITEM] Failed to find current module:', moduleError);
      return { nextItemType: null, nextItemSlug: null, isLastItem: true };
    }

    // Check if there's a post-test for the current module that hasn't been completed
    const { data: postTest, error: postTestError } = await supabase
      .from('module_tests')
      .select('id')
      .eq('module_id', currentLesson.module_id)
      .eq('type', 'post_test')
      .maybeSingle();

    if (!postTestError && postTest) {
      // Check if post-test is completed
      const { data: postTestResponse, error: responseError } = await supabase
        .from('module_test_responses')
        .select('id')
        .eq('test_id', postTest.id)
        .eq('user_id', userId)
        .maybeSingle();

      if (!responseError && !postTestResponse) {
        // Post-test exists and is not completed - this should be the next item
        console.log('[FIND NEXT ITEM] Next item is post-test for current module');
        return {
          nextItemType: 'post_test',
          nextItemSlug: currentLesson.module_id, // Use module ID for test navigation
          nextModuleId: currentLesson.module_id,
          isLastItem: false
        };
      }
    }

    // Look for next lesson in the same module
    const { data: nextLessonsInModule, error: nextLessonError } = await supabase
      .from('lessons')
      .select('slug, created_at')
      .eq('module_id', currentLesson.module_id)
      .order('created_at', { ascending: true });

    if (!nextLessonError && nextLessonsInModule) {
      const currentIndex = nextLessonsInModule.findIndex(l => l.slug === currentLessonSlug);
      if (currentIndex !== -1 && currentIndex < nextLessonsInModule.length - 1) {
        // There's a next lesson in the same module
        console.log('[FIND NEXT ITEM] Next item is lesson in same module');
        return {
          nextItemType: 'lesson',
          nextItemSlug: nextLessonsInModule[currentIndex + 1].slug,
          isLastItem: false
        };
      }
    }

    // No more lessons in current module, look for next module
    const { data: nextModule, error: nextModuleError } = await supabase
      .from('modules')
      .select('id, module_number')
      .eq('course_id', currentModule.course_id)
      .gt('module_number', currentModule.module_number)
      .order('module_number', { ascending: true })
      .limit(1)
      .maybeSingle();

    if (nextModuleError || !nextModule) {
      // No next module - this is the last item
      console.log('[FIND NEXT ITEM] No next module found - last item');
      return { nextItemType: null, nextItemSlug: null, isLastItem: true };
    }

    // Check if next module has a pre-test that hasn't been completed
    const { data: preTest, error: preTestError } = await supabase
      .from('module_tests')
      .select('id')
      .eq('module_id', nextModule.id)
      .eq('type', 'pre_test')
      .maybeSingle();

    if (!preTestError && preTest) {
      // Check if pre-test is completed
      const { data: preTestResponse, error: preResponseError } = await supabase
        .from('module_test_responses')
        .select('id')
        .eq('test_id', preTest.id)
        .eq('user_id', userId)
        .maybeSingle();

      if (!preResponseError && !preTestResponse) {
        // Pre-test exists and is not completed - this should be the next item
        console.log('[FIND NEXT ITEM] Next item is pre-test for next module');
        return {
          nextItemType: 'pre_test',
          nextItemSlug: nextModule.id, // Use module ID for test navigation
          nextModuleId: nextModule.id,
          isLastItem: false
        };
      }
    }

    // No pre-test or pre-test completed, get first lesson of next module
    const { data: firstLessonInNextModule, error: firstLessonError } = await supabase
      .from('lessons')
      .select('slug')
      .eq('module_id', nextModule.id)
      .order('created_at', { ascending: true })
      .limit(1)
      .maybeSingle();

    if (firstLessonError || !firstLessonInNextModule) {
      console.log('[FIND NEXT ITEM] No lessons found in next module');
      return { nextItemType: null, nextItemSlug: null, isLastItem: true };
    }

    console.log('[FIND NEXT ITEM] Next item is first lesson in next module');
    return {
      nextItemType: 'lesson',
      nextItemSlug: firstLessonInNextModule.slug,
      isLastItem: false
    };

  } catch (error: any) {
    console.error('[FIND NEXT ITEM] Error:', error);
    return { nextItemType: null, nextItemSlug: null, isLastItem: true };
  }
};

// Find the next lesson in sequence
export const findNextLesson = async (
  currentLessonSlug: string
): Promise<{ nextLessonSlug: string | null; isLastLesson: boolean }> => {
  try {
    // First get the current lesson to find its module and order
    const { data: currentLesson, error: lessonError } = await supabase
      .from('lessons')
      .select('id, module_id, order')
      .eq('slug', currentLessonSlug)
      .single();

    if (lessonError || !currentLesson) {
      throw new Error('Could not find current lesson');
    }

    // Find the next lesson in the same module
    const { data: nextLessons, error: nextError } = await supabase
      .from('lessons')
      .select('slug')
      .eq('module_id', currentLesson.module_id)
      .gt('order', currentLesson.order)
      .order('order', { ascending: true })
      .limit(1);

    if (nextLessons && nextLessons.length > 0) {
      return {
        nextLessonSlug: nextLessons[0].slug,
        isLastLesson: false
      };
    }

    // If no next lesson in same module, check the next module
    const { data: currentModule, error: moduleError } = await supabase
      .from('modules')
      .select('course_id, order')
      .eq('id', currentLesson.module_id)
      .single();

    if (moduleError || !currentModule) {
      throw new Error('Could not find current module');
    }

    // Find the next module in the same course
    const { data: nextModules, error: nextModuleError } = await supabase
      .from('modules')
      .select('id')
      .eq('course_id', currentModule.course_id)
      .gt('order', currentModule.order)
      .order('order', { ascending: true })
      .limit(1);

    if (nextModules && nextModules.length > 0) {
      // Find the first lesson in the next module
      const { data: firstLessons, error: firstLessonError } = await supabase
        .from('lessons')
        .select('slug')
        .eq('module_id', nextModules[0].id)
        .order('order', { ascending: true })
        .limit(1);

      if (firstLessons && firstLessons.length > 0) {
        return {
          nextLessonSlug: firstLessons[0].slug,
          isLastLesson: false
        };
      }
    }

    // No next lesson or module
    return {
      nextLessonSlug: null,
      isLastLesson: true
    };
  } catch (error: any) {
    console.error('[FIND NEXT LESSON] Error finding next lesson:', error);
    console.error('[FIND NEXT LESSON] Error details:', {
      message: error.message,
      stack: error.stack,
      currentLessonSlug
    });

    // Return a safe fallback
    return {
      nextLessonSlug: null,
      isLastLesson: true // Mark as last lesson to prevent infinite loops
    };
  }
};
