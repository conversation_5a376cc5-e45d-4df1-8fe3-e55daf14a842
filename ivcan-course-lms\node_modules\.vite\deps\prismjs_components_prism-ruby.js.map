{"version": 3, "sources": ["../../prismjs/components/prism-ruby.js"], "sourcesContent": ["/**\n * Original by <PERSON>\n *\n * Adds the following new token classes:\n *     constant, builtin, variable, symbol, regex\n */\n(function (Prism) {\n\tPrism.languages.ruby = Prism.languages.extend('clike', {\n\t\t'comment': {\n\t\t\tpattern: /#.*|^=begin\\s[\\s\\S]*?^=end/m,\n\t\t\tgreedy: true\n\t\t},\n\t\t'class-name': {\n\t\t\tpattern: /(\\b(?:class|module)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+|\\b[A-Z_]\\w*(?=\\s*\\.\\s*new\\b)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'punctuation': /[.\\\\]/\n\t\t\t}\n\t\t},\n\t\t'keyword': /\\b(?:BEGIN|END|alias|and|begin|break|case|class|def|define_method|defined|do|each|else|elsif|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|private|protected|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\\b/,\n\t\t'operator': /\\.{2,3}|&\\.|===|<?=>|[!=]?~|(?:&&|\\|\\||<<|>>|\\*\\*|[+\\-*/%<>!^&|=])=?|[?:]/,\n\t\t'punctuation': /[(){}[\\].,;]/,\n\t});\n\n\tPrism.languages.insertBefore('ruby', 'operator', {\n\t\t'double-colon': {\n\t\t\tpattern: /::/,\n\t\t\talias: 'punctuation'\n\t\t},\n\t});\n\n\tvar interpolation = {\n\t\tpattern: /((?:^|[^\\\\])(?:\\\\{2})*)#\\{(?:[^{}]|\\{[^{}]*\\})*\\}/,\n\t\tlookbehind: true,\n\t\tinside: {\n\t\t\t'content': {\n\t\t\t\tpattern: /^(#\\{)[\\s\\S]+(?=\\}$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: Prism.languages.ruby\n\t\t\t},\n\t\t\t'delimiter': {\n\t\t\t\tpattern: /^#\\{|\\}$/,\n\t\t\t\talias: 'punctuation'\n\t\t\t}\n\t\t}\n\t};\n\n\tdelete Prism.languages.ruby.function;\n\n\tvar percentExpression = '(?:' + [\n\t\t/([^a-zA-Z0-9\\s{(\\[<=])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source,\n\t\t/\\((?:[^()\\\\]|\\\\[\\s\\S]|\\((?:[^()\\\\]|\\\\[\\s\\S])*\\))*\\)/.source,\n\t\t/\\{(?:[^{}\\\\]|\\\\[\\s\\S]|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\})*\\}/.source,\n\t\t/\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S]|\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S])*\\])*\\]/.source,\n\t\t/<(?:[^<>\\\\]|\\\\[\\s\\S]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)*>/.source\n\t].join('|') + ')';\n\n\tvar symbolName = /(?:\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|(?:\\b[a-zA-Z_]\\w*|[^\\s\\0-\\x7F]+)[?!]?|\\$.)/.source;\n\n\tPrism.languages.insertBefore('ruby', 'keyword', {\n\t\t'regex-literal': [\n\t\t\t{\n\t\t\t\tpattern: RegExp(/%r/.source + percentExpression + /[egimnosux]{0,6}/.source),\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation': interpolation,\n\t\t\t\t\t'regex': /[\\s\\S]+/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(^|[^/])\\/(?!\\/)(?:\\[[^\\r\\n\\]]+\\]|\\\\.|[^[/\\\\\\r\\n])+\\/[egimnosux]{0,6}(?=\\s*(?:$|[\\r\\n,.;})#]))/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation': interpolation,\n\t\t\t\t\t'regex': /[\\s\\S]+/\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'variable': /[@$]+[a-zA-Z_]\\w*(?:[?!]|\\b)/,\n\t\t'symbol': [\n\t\t\t{\n\t\t\t\tpattern: RegExp(/(^|[^:]):/.source + symbolName),\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: RegExp(/([\\r\\n{(,][ \\t]*)/.source + symbolName + /(?=:(?!:))/.source),\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t],\n\t\t'method-definition': {\n\t\t\tpattern: /(\\bdef\\s+)\\w+(?:\\s*\\.\\s*\\w+)?/,\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'function': /\\b\\w+$/,\n\t\t\t\t'keyword': /^self\\b/,\n\t\t\t\t'class-name': /^\\w+/,\n\t\t\t\t'punctuation': /\\./\n\t\t\t}\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('ruby', 'string', {\n\t\t'string-literal': [\n\t\t\t{\n\t\t\t\tpattern: RegExp(/%[qQiIwWs]?/.source + percentExpression),\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation': interpolation,\n\t\t\t\t\t'string': /[\\s\\S]+/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\"|')(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\#\\r\\n])*\\1/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation': interpolation,\n\t\t\t\t\t'string': /[\\s\\S]+/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /<<[-~]?([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n\t\t\t\talias: 'heredoc-string',\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'delimiter': {\n\t\t\t\t\t\tpattern: /^<<[-~]?[a-z_]\\w*|\\b[a-z_]\\w*$/i,\n\t\t\t\t\t\tinside: {\n\t\t\t\t\t\t\t'symbol': /\\b\\w+/,\n\t\t\t\t\t\t\t'punctuation': /^<<[-~]?/\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\t'interpolation': interpolation,\n\t\t\t\t\t'string': /[\\s\\S]+/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /<<[-~]?'([a-z_]\\w*)'[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n\t\t\t\talias: 'heredoc-string',\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'delimiter': {\n\t\t\t\t\t\tpattern: /^<<[-~]?'[a-z_]\\w*'|\\b[a-z_]\\w*$/i,\n\t\t\t\t\t\tinside: {\n\t\t\t\t\t\t\t'symbol': /\\b\\w+/,\n\t\t\t\t\t\t\t'punctuation': /^<<[-~]?'|'$/,\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\t'string': /[\\s\\S]+/\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'command-literal': [\n\t\t\t{\n\t\t\t\tpattern: RegExp(/%x/.source + percentExpression),\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation': interpolation,\n\t\t\t\t\t'command': {\n\t\t\t\t\t\tpattern: /[\\s\\S]+/,\n\t\t\t\t\t\talias: 'string'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /`(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|[^\\\\`#\\r\\n])*`/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation': interpolation,\n\t\t\t\t\t'command': {\n\t\t\t\t\t\tpattern: /[\\s\\S]+/,\n\t\t\t\t\t\talias: 'string'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t]\n\t});\n\n\tdelete Prism.languages.ruby.string;\n\n\tPrism.languages.insertBefore('ruby', 'number', {\n\t\t'builtin': /\\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Fixnum|Float|Hash|IO|Integer|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|Stat|String|Struct|Symbol|TMS|Thread|ThreadGroup|Time|TrueClass)\\b/,\n\t\t'constant': /\\b[A-Z][A-Z0-9_]*(?:[?!]|\\b)/\n\t});\n\n\tPrism.languages.rb = Prism.languages.ruby;\n}(Prism));\n"], "mappings": ";CAMC,SAAUA,QAAO;AACjB,EAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,SAAS;AAAA,IACtD,WAAW;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,EAChB,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,IAChD,gBAAgB;AAAA,MACf,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,EACD,CAAC;AAED,MAAI,gBAAgB;AAAA,IACnB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,MACP,WAAW;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQA,OAAM,UAAU;AAAA,MACzB;AAAA,MACA,aAAa;AAAA,QACZ,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAEA,SAAOA,OAAM,UAAU,KAAK;AAE5B,MAAI,oBAAoB,QAAQ;AAAA,IAC/B,oDAAoD;AAAA,IACpD,sDAAsD;AAAA,IACtD,sDAAsD;AAAA,IACtD,0DAA0D;AAAA,IAC1D,kDAAkD;AAAA,EACnD,EAAE,KAAK,GAAG,IAAI;AAEd,MAAI,aAAa,sEAAsE;AAEvF,EAAAA,OAAM,UAAU,aAAa,QAAQ,WAAW;AAAA,IAC/C,iBAAiB;AAAA,MAChB;AAAA,QACC,SAAS,OAAO,KAAK,SAAS,oBAAoB,mBAAmB,MAAM;AAAA,QAC3E,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,iBAAiB;AAAA,UACjB,SAAS;AAAA,QACV;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,iBAAiB;AAAA,UACjB,SAAS;AAAA,QACV;AAAA,MACD;AAAA,IACD;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,MACT;AAAA,QACC,SAAS,OAAO,YAAY,SAAS,UAAU;AAAA,QAC/C,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,SAAS,OAAO,oBAAoB,SAAS,aAAa,aAAa,MAAM;AAAA,QAC7E,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,qBAAqB;AAAA,MACpB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACP,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,IAC9C,kBAAkB;AAAA,MACjB;AAAA,QACC,SAAS,OAAO,cAAc,SAAS,iBAAiB;AAAA,QACxD,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,iBAAiB;AAAA,UACjB,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,iBAAiB;AAAA,UACjB,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,aAAa;AAAA,YACZ,SAAS;AAAA,YACT,QAAQ;AAAA,cACP,UAAU;AAAA,cACV,eAAe;AAAA,YAChB;AAAA,UACD;AAAA,UACA,iBAAiB;AAAA,UACjB,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,aAAa;AAAA,YACZ,SAAS;AAAA,YACT,QAAQ;AAAA,cACP,UAAU;AAAA,cACV,eAAe;AAAA,YAChB;AAAA,UACD;AAAA,UACA,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAAA,IACA,mBAAmB;AAAA,MAClB;AAAA,QACC,SAAS,OAAO,KAAK,SAAS,iBAAiB;AAAA,QAC/C,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,iBAAiB;AAAA,UACjB,WAAW;AAAA,YACV,SAAS;AAAA,YACT,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,iBAAiB;AAAA,UACjB,WAAW;AAAA,YACV,SAAS;AAAA,YACT,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD,CAAC;AAED,SAAOA,OAAM,UAAU,KAAK;AAE5B,EAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,IAC9C,WAAW;AAAA,IACX,YAAY;AAAA,EACb,CAAC;AAED,EAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AACtC,GAAE,KAAK;", "names": ["Prism"]}