{"version": 3, "sources": ["../../prismjs/components/prism-c.js"], "sourcesContent": ["Prism.languages.c = Prism.languages.extend('clike', {\n\t'comment': {\n\t\tpattern: /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n\t\tgreedy: true\n\t},\n\t'string': {\n\t\t// https://en.cppreference.com/w/c/language/string_literal\n\t\tpattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n\t\tgreedy: true\n\t},\n\t'class-name': {\n\t\tpattern: /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n\t\tlookbehind: true\n\t},\n\t'keyword': /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n\t'function': /\\b[a-z_]\\w*(?=\\s*\\()/i,\n\t'number': /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n\t'operator': />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n});\n\nPrism.languages.insertBefore('c', 'string', {\n\t'char': {\n\t\t// https://en.cppreference.com/w/c/language/character_constant\n\t\tpattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n\t\tgreedy: true\n\t}\n});\n\nPrism.languages.insertBefore('c', 'string', {\n\t'macro': {\n\t\t// allow for multiline macro definitions\n\t\t// spaces after the # character compile fine with gcc\n\t\tpattern: /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n\t\tlookbehind: true,\n\t\tgreedy: true,\n\t\talias: 'property',\n\t\tinside: {\n\t\t\t'string': [\n\t\t\t\t{\n\t\t\t\t\t// highlight the path of the include statement as a string\n\t\t\t\t\tpattern: /^(#\\s*include\\s*)<[^>]+>/,\n\t\t\t\t\tlookbehind: true\n\t\t\t\t},\n\t\t\t\tPrism.languages.c['string']\n\t\t\t],\n\t\t\t'char': Prism.languages.c['char'],\n\t\t\t'comment': Prism.languages.c['comment'],\n\t\t\t'macro-name': [\n\t\t\t\t{\n\t\t\t\t\tpattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n\t\t\t\t\tlookbehind: true\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tpattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'function'\n\t\t\t\t}\n\t\t\t],\n\t\t\t// highlight macro directives as keywords\n\t\t\t'directive': {\n\t\t\t\tpattern: /^(#\\s*)[a-z]+/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'keyword'\n\t\t\t},\n\t\t\t'directive-hash': /^#/,\n\t\t\t'punctuation': /##|\\\\(?=[\\r\\n])/,\n\t\t\t'expression': {\n\t\t\t\tpattern: /\\S[\\s\\S]*/,\n\t\t\t\tinside: Prism.languages.c\n\t\t\t}\n\t\t}\n\t}\n});\n\nPrism.languages.insertBefore('c', 'function', {\n\t// highlight predefined macros as constants\n\t'constant': /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/\n});\n\ndelete Prism.languages.c['boolean'];\n"], "mappings": ";AAAA,MAAM,UAAU,IAAI,MAAM,UAAU,OAAO,SAAS;AAAA,EACnD,WAAW;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,EACT;AAAA,EACA,UAAU;AAAA;AAAA,IAET,SAAS;AAAA,IACT,QAAQ;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACb,SAAS;AAAA,IACT,YAAY;AAAA,EACb;AAAA,EACA,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AACb,CAAC;AAED,MAAM,UAAU,aAAa,KAAK,UAAU;AAAA,EAC3C,QAAQ;AAAA;AAAA,IAEP,SAAS;AAAA,IACT,QAAQ;AAAA,EACT;AACD,CAAC;AAED,MAAM,UAAU,aAAa,KAAK,UAAU;AAAA,EAC3C,SAAS;AAAA;AAAA;AAAA,IAGR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,MACP,UAAU;AAAA,QACT;AAAA;AAAA,UAEC,SAAS;AAAA,UACT,YAAY;AAAA,QACb;AAAA,QACA,MAAM,UAAU,EAAE,QAAQ;AAAA,MAC3B;AAAA,MACA,QAAQ,MAAM,UAAU,EAAE,MAAM;AAAA,MAChC,WAAW,MAAM,UAAU,EAAE,SAAS;AAAA,MACtC,cAAc;AAAA,QACb;AAAA,UACC,SAAS;AAAA,UACT,YAAY;AAAA,QACb;AAAA,QACA;AAAA,UACC,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACR;AAAA,MACD;AAAA;AAAA,MAEA,aAAa;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,MACR;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,cAAc;AAAA,QACb,SAAS;AAAA,QACT,QAAQ,MAAM,UAAU;AAAA,MACzB;AAAA,IACD;AAAA,EACD;AACD,CAAC;AAED,MAAM,UAAU,aAAa,KAAK,YAAY;AAAA;AAAA,EAE7C,YAAY;AACb,CAAC;AAED,OAAO,MAAM,UAAU,EAAE,SAAS;", "names": []}