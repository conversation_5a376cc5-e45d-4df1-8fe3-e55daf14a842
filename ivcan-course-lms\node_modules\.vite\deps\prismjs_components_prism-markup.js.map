{"version": 3, "sources": ["../../prismjs/components/prism-markup.js"], "sourcesContent": ["Prism.languages.markup = {\n\t'comment': {\n\t\tpattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n\t\tgreedy: true\n\t},\n\t'prolog': {\n\t\tpattern: /<\\?[\\s\\S]+?\\?>/,\n\t\tgreedy: true\n\t},\n\t'doctype': {\n\t\t// https://www.w3.org/TR/xml/#NT-doctypedecl\n\t\tpattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'internal-subset': {\n\t\t\t\tpattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: null // see below\n\t\t\t},\n\t\t\t'string': {\n\t\t\t\tpattern: /\"[^\"]*\"|'[^']*'/,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t'punctuation': /^<!|>$|[[\\]]/,\n\t\t\t'doctype-tag': /^DOCTYPE/i,\n\t\t\t'name': /[^\\s<>'\"]+/\n\t\t}\n\t},\n\t'cdata': {\n\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t\tgreedy: true\n\t},\n\t'tag': {\n\t\tpattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'tag': {\n\t\t\t\tpattern: /^<\\/?[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /^<\\/?/,\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t},\n\t\t\t'special-attr': [],\n\t\t\t'attr-value': {\n\t\t\t\tpattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /^(\\s*)[\"']|[\"']$/,\n\t\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t},\n\t\t\t'punctuation': /\\/?>/,\n\t\t\t'attr-name': {\n\t\t\t\tpattern: /[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t},\n\t'entity': [\n\t\t{\n\t\t\tpattern: /&[\\da-z]{1,8};/i,\n\t\t\talias: 'named-entity'\n\t\t},\n\t\t/&#x?[\\da-f]{1,8};/i\n\t]\n};\n\nPrism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n\tPrism.languages.markup['entity'];\nPrism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n\n// Plugin to make entity title show the real entity, idea by Roman Komarov\nPrism.hooks.add('wrap', function (env) {\n\n\tif (env.type === 'entity') {\n\t\tenv.attributes['title'] = env.content.replace(/&amp;/, '&');\n\t}\n});\n\nObject.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n\t/**\n\t * Adds an inlined language to markup.\n\t *\n\t * An example of an inlined language is CSS with `<style>` tags.\n\t *\n\t * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addInlined('style', 'css');\n\t */\n\tvalue: function addInlined(tagName, lang) {\n\t\tvar includedCdataInside = {};\n\t\tincludedCdataInside['language-' + lang] = {\n\t\t\tpattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\t\tincludedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n\n\t\tvar inside = {\n\t\t\t'included-cdata': {\n\t\t\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t\t\t\tinside: includedCdataInside\n\t\t\t}\n\t\t};\n\t\tinside['language-' + lang] = {\n\t\t\tpattern: /[\\s\\S]+/,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\n\t\tvar def = {};\n\t\tdef[tagName] = {\n\t\t\tpattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () { return tagName; }), 'i'),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: inside\n\t\t};\n\n\t\tPrism.languages.insertBefore('markup', 'cdata', def);\n\t}\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n\t/**\n\t * Adds an pattern to highlight languages embedded in HTML attributes.\n\t *\n\t * An example of an inlined language is CSS with `style` attributes.\n\t *\n\t * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addAttribute('style', 'css');\n\t */\n\tvalue: function (attrName, lang) {\n\t\tPrism.languages.markup.tag.inside['special-attr'].push({\n\t\t\tpattern: RegExp(\n\t\t\t\t/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n\t\t\t\t'i'\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'attr-name': /^[^\\s=]+/,\n\t\t\t\t'attr-value': {\n\t\t\t\t\tpattern: /=[\\s\\S]+/,\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'value': {\n\t\t\t\t\t\t\tpattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n\t\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\t\talias: [lang, 'language-' + lang],\n\t\t\t\t\t\t\tinside: Prism.languages[lang]\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t/\"|'/\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n});\n\nPrism.languages.html = Prism.languages.markup;\nPrism.languages.mathml = Prism.languages.markup;\nPrism.languages.svg = Prism.languages.markup;\n\nPrism.languages.xml = Prism.languages.extend('markup', {});\nPrism.languages.ssml = Prism.languages.xml;\nPrism.languages.atom = Prism.languages.xml;\nPrism.languages.rss = Prism.languages.xml;\n"], "mappings": ";AAAA,MAAM,UAAU,SAAS;AAAA,EACxB,WAAW;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACT;AAAA,EACA,WAAW;AAAA;AAAA,IAEV,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACP,mBAAmB;AAAA,QAClB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,MACT;AAAA,MACA,eAAe;AAAA,MACf,eAAe;AAAA,MACf,QAAQ;AAAA,IACT;AAAA,EACD;AAAA,EACA,SAAS;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACP,OAAO;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,UACP,eAAe;AAAA,UACf,aAAa;AAAA,QACd;AAAA,MACD;AAAA,MACA,gBAAgB,CAAC;AAAA,MACjB,cAAc;AAAA,QACb,SAAS;AAAA,QACT,QAAQ;AAAA,UACP,eAAe;AAAA,YACd;AAAA,cACC,SAAS;AAAA,cACT,OAAO;AAAA,YACR;AAAA,YACA;AAAA,cACC,SAAS;AAAA,cACT,YAAY;AAAA,YACb;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,MACA,eAAe;AAAA,MACf,aAAa;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ;AAAA,UACP,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IAED;AAAA,EACD;AAAA,EACA,UAAU;AAAA,IACT;AAAA,MACC,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,IACA;AAAA,EACD;AACD;AAEA,MAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,QAAQ,IACjE,MAAM,UAAU,OAAO,QAAQ;AAChC,MAAM,UAAU,OAAO,SAAS,EAAE,OAAO,iBAAiB,EAAE,SAAS,MAAM,UAAU;AAGrF,MAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AAEtC,MAAI,IAAI,SAAS,UAAU;AAC1B,QAAI,WAAW,OAAO,IAAI,IAAI,QAAQ,QAAQ,SAAS,GAAG;AAAA,EAC3D;AACD,CAAC;AAED,OAAO,eAAe,MAAM,UAAU,OAAO,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY/D,OAAO,SAAS,WAAW,SAAS,MAAM;AACzC,QAAI,sBAAsB,CAAC;AAC3B,wBAAoB,cAAc,IAAI,IAAI;AAAA,MACzC,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ,MAAM,UAAU,IAAI;AAAA,IAC7B;AACA,wBAAoB,OAAO,IAAI;AAE/B,QAAI,SAAS;AAAA,MACZ,kBAAkB;AAAA,QACjB,SAAS;AAAA,QACT,QAAQ;AAAA,MACT;AAAA,IACD;AACA,WAAO,cAAc,IAAI,IAAI;AAAA,MAC5B,SAAS;AAAA,MACT,QAAQ,MAAM,UAAU,IAAI;AAAA,IAC7B;AAEA,QAAI,MAAM,CAAC;AACX,QAAI,OAAO,IAAI;AAAA,MACd,SAAS,OAAO,wFAAwF,OAAO,QAAQ,OAAO,WAAY;AAAE,eAAO;AAAA,MAAS,CAAC,GAAG,GAAG;AAAA,MACnK,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR;AAAA,IACD;AAEA,UAAM,UAAU,aAAa,UAAU,SAAS,GAAG;AAAA,EACpD;AACD,CAAC;AACD,OAAO,eAAe,MAAM,UAAU,OAAO,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYjE,OAAO,SAAU,UAAU,MAAM;AAChC,UAAM,UAAU,OAAO,IAAI,OAAO,cAAc,EAAE,KAAK;AAAA,MACtD,SAAS;AAAA,QACR,aAAa,SAAS,QAAQ,WAAW,MAAM,iDAAiD;AAAA,QAChG;AAAA,MACD;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ;AAAA,QACP,aAAa;AAAA,QACb,cAAc;AAAA,UACb,SAAS;AAAA,UACT,QAAQ;AAAA,YACP,SAAS;AAAA,cACR,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO,CAAC,MAAM,cAAc,IAAI;AAAA,cAChC,QAAQ,MAAM,UAAU,IAAI;AAAA,YAC7B;AAAA,YACA,eAAe;AAAA,cACd;AAAA,gBACC,SAAS;AAAA,gBACT,OAAO;AAAA,cACR;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AACD,CAAC;AAED,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,SAAS,MAAM,UAAU;AACzC,MAAM,UAAU,MAAM,MAAM,UAAU;AAEtC,MAAM,UAAU,MAAM,MAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AACzD,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,MAAM,MAAM,UAAU;", "names": []}