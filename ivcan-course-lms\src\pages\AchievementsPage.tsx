import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import Layout from '../components/Layout';
import { useAuth } from '@/context/AuthContext';
import { useUserRole } from '@/hooks/useUserRole';
import { useIsMobile } from '@/hooks/use-mobile';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Award, Download, FileText, Loader2, RefreshCw, Search, Trophy, Share2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import BadgeCard from '@/components/achievements/BadgeCard';
import { getUserAchievements, UserAchievement } from '@/services/achievements/achievementService';
import { PageContainer, ContentSection } from '@/components/ui/floating-sidebar-container';

interface Certificate {
  id: string;
  course_id: string;
  user_id: string;
  completed_at: string;
  created_at?: string; // Optional fallback
  status: string;
  course: {
    title: string;
    description: string;
    image_url?: string;
  };
}

const AchievementsPage = () => {
  const { user } = useAuth();
  const { isTeacher } = useUserRole();
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('certificates');
  const [sharingCertId, setSharingCertId] = useState<string | null>(null);

  // Fetch user's completed courses (certificates)
  const { data: certificates, isLoading: certificatesLoading, refetch: refetchCertificates } = useQuery({
    queryKey: ['certificates', user?.id],
    queryFn: async () => {
      if (!user) return [];

      console.log(`Fetching certificates for user ${user.id}`);

      // First try to get from user_course_enrollment with status completed
      const { data: enrollmentData, error: enrollmentError } = await supabase
        .from('user_course_enrollment')
        .select(`
          id,
          course_id,
          user_id,
          completed_at,
          created_at,
          updated_at,
          status,
          course:courses(id, title, description, image_url)
        `)
        .eq('user_id', user.id)
        .eq('status', 'completed');

      if (enrollmentError) {
        console.error('Error fetching certificates from enrollments:', enrollmentError);
        return [];
      }

      console.log('Certificates data from enrollments:', enrollmentData);

      // Fix any missing completed_at dates
      const fixedData = enrollmentData.map(cert => {
        if (!cert.completed_at) {
          console.log(`Fixing missing completed_at for certificate ${cert.id}`);
          // Use updated_at or created_at as fallback
          cert.completed_at = cert.updated_at || cert.created_at || new Date().toISOString();

          // Try to update the record in the database
          try {
            supabase
              .from('user_course_enrollment')
              .update({ completed_at: cert.completed_at })
              .eq('id', cert.id)
              .then(() => console.log(`Updated completed_at for certificate ${cert.id}`))
              .catch(err => console.error(`Failed to update completed_at for certificate ${cert.id}:`, err));
          } catch (updateError) {
            console.error('Error updating certificate:', updateError);
          }
        }
        return cert;
      });

      return fixedData as Certificate[];
    },
    enabled: !!user,
  });

  // Fetch user's achievements (badges)
  const { data: achievements, isLoading: achievementsLoading, refetch: refetchAchievements } = useQuery({
    queryKey: ['user-achievements', user?.id],
    queryFn: async () => {
      if (!user) return [];

      console.log(`Fetching achievements for user ${user.id}`);
      const userAchievements = await getUserAchievements(user.id);

      // Enhance achievements with additional data if needed
      if (userAchievements.length > 0) {
        // Get module details for module-related badges
        const moduleIds = userAchievements
          .filter(a => a.module_id)
          .map(a => a.module_id as string);

        if (moduleIds.length > 0) {
          const { data: moduleData } = await supabase
            .from('modules')
            .select('id, title, module_number, course_id')
            .in('id', moduleIds);

          if (moduleData) {
            // Add module info to achievements
            userAchievements.forEach(achievement => {
              if (achievement.module_id) {
                const moduleInfo = moduleData.find(m => m.id === achievement.module_id);
                if (moduleInfo) {
                  (achievement as any).moduleInfo = moduleInfo;
                }
              }
            });
          }
        }

        // Get course details for course-related badges
        const courseIds = userAchievements
          .filter(a => a.course_id)
          .map(a => a.course_id as string);

        if (courseIds.length > 0) {
          const { data: courseData } = await supabase
            .from('courses')
            .select('id, title, description')
            .in('id', courseIds);

          if (courseData) {
            // Add course info to achievements
            userAchievements.forEach(achievement => {
              if (achievement.course_id) {
                const courseInfo = courseData.find(c => c.id === achievement.course_id);
                if (courseInfo) {
                  (achievement as any).courseInfo = courseInfo;
                }
              }
            });
          }
        }
      }

      console.log('User achievements data:', userAchievements);
      return userAchievements;
    },
    enabled: !!user,
  });

  // Filter certificates based on search query
  const filteredCertificates = certificates?.filter(cert =>
    cert.course.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Filter achievements based on search query
  const filteredAchievements = achievements?.filter(achievement =>
    achievement.achievement.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    achievement.achievement.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Generate a certificate ID from the actual ID
  const generateCertificateId = (id: string) => {
    // Create a formatted certificate ID (e.g., CERT-1234-ABCD)
    return `CERT-${id.substring(0, 4).toUpperCase()}-${id.substring(id.length - 4).toUpperCase()}`;
  };

  // Navigate to certificate page
  const viewCertificate = (courseId: string) => {
    navigate(`/certificate/${courseId}`);
  };

  // Share certificate
  const shareCertificate = async (certificate: Certificate) => {
    if (!navigator.share) {
      toast({
        title: "Sharing not supported",
        description: "Your browser doesn't support sharing. Try using a mobile device.",
        variant: "destructive"
      });
      return;
    }

    setSharingCertId(certificate.id);

    try {
      await navigator.share({
        title: `${certificate.course.title} Certificate`,
        text: `I've completed the ${certificate.course.title} course and earned a certificate!`,
        url: window.location.origin + `/certificate/${certificate.course_id}`
      });

      toast({
        title: "Shared successfully",
        description: "Your certificate has been shared."
      });
    } catch (error) {
      if (error.name !== 'AbortError') { // Ignore if user cancelled
        console.error('Error sharing certificate:', error);
        toast({
          title: "Sharing failed",
          description: "There was an error sharing your certificate.",
          variant: "destructive"
        });
      }
    } finally {
      setSharingCertId(null);
    }
  };

  if (!user) {
    navigate('/login');
    return null;
  }

  return (
    <Layout>
      <PageContainer pageType="certificate">
        <ContentSection spacing="lg">
          <motion.div
            className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6 sm:mb-8"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
          <div>
            <h1 className={cn(
              "font-bold mb-1 sm:mb-2",
              isMobile ? "text-2xl" : "text-3xl"
            )}>Your Achievements</h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              View and download your certificates and achievements
            </p>
          </div>

          <div className="flex gap-2 w-full md:w-auto">
            <div className="relative flex-1 md:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search achievements..."
                className="pl-9 w-full text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                refetchCertificates();
                refetchAchievements();
              }}
              disabled={certificatesLoading || achievementsLoading}
              title="Refresh achievements"
              className="flex-shrink-0"
            >
              <RefreshCw className={cn("h-4 w-4", (certificatesLoading || achievementsLoading) && "animate-spin")} />
            </Button>
          </div>
        </motion.div>

        {/* Achievement Summary */}
        {!certificatesLoading && !achievementsLoading && (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="bg-primary/5 border-primary/10">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Badges Earned</h3>
                    <p className="text-sm text-muted-foreground">
                      {achievements?.filter(a => a.is_claimed).length || 0} of {achievements?.length || 0} claimed
                    </p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <Trophy className="h-6 w-6 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-primary/5 border-primary/10">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Certificates Earned</h3>
                    <p className="text-sm text-muted-foreground">
                      {certificates?.length || 0} course{certificates?.length !== 1 ? 's' : ''} completed
                    </p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <Award className="h-6 w-6 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        <Tabs defaultValue="certificates" className="space-y-6 sm:space-y-8" onValueChange={setActiveTab}>
          <TabsList className="w-full sm:w-auto">
            <TabsTrigger
              value="certificates"
              className="flex items-center gap-2 flex-1 sm:flex-initial"
            >
              <Award className="h-4 w-4" />
              <span className={isMobile ? "text-sm" : ""}>Certificates</span>
            </TabsTrigger>
            <TabsTrigger
              value="badges"
              className="flex items-center gap-2 flex-1 sm:flex-initial"
            >
              <Trophy className="h-4 w-4" />
              <span className={isMobile ? "text-sm" : ""}>Badges</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="certificates" className="space-y-6">
            {certificatesLoading ? (
              <div className="flex justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : filteredCertificates && filteredCertificates.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {filteredCertificates.map((certificate) => (
                  <motion.div
                    key={certificate.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="h-full"
                  >
                    <Card className="h-full flex flex-col overflow-hidden hover:shadow-md transition-shadow duration-200 border-primary/10">
                      <CardHeader className="pb-2 sm:pb-3">
                        <div className="flex justify-between items-start gap-2">
                          <CardTitle className={cn(
                            "font-bold",
                            isMobile ? "text-base" : "text-lg"
                          )}>{certificate.course.title}</CardTitle>
                          <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 whitespace-nowrap text-xs">
                            {generateCertificateId(certificate.id)}
                          </Badge>
                        </div>
                        <CardDescription className="line-clamp-2 text-xs sm:text-sm mt-1">
                          {certificate.course.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-grow pb-2">
                        <div className="flex flex-col gap-1">
                          <div className="flex items-center text-xs sm:text-sm text-muted-foreground">
                            <FileText className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                            <span className="truncate">
                              Completed on {format(new Date(certificate.completed_at || certificate.created_at), 'MMMM d, yyyy')}
                            </span>
                          </div>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Trophy className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                            <span>Course Certificate</span>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex flex-col sm:flex-row gap-2 pt-2">
                        <Button
                          variant="default"
                          size={isMobile ? "sm" : "default"}
                          className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                          onClick={() => viewCertificate(certificate.course_id)}
                        >
                          <Award className="h-4 w-4 mr-2" />
                          View Certificate
                        </Button>

                        {(isMobile || (typeof navigator !== 'undefined' && 'share' in navigator)) && (
                          <Button
                            variant="outline"
                            size={isMobile ? "sm" : "default"}
                            className="w-full border-primary/20 text-primary hover:bg-primary/5"
                            onClick={() => shareCertificate(certificate)}
                            disabled={sharingCertId === certificate.id}
                          >
                            {sharingCertId === certificate.id ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Share2 className="h-4 w-4 mr-2" />
                            )}
                            Share
                          </Button>
                        )}
                      </CardFooter>
                    </Card>
                  </motion.div>
                ))}
              </div>
            ) : (
              <motion.div
                className="text-center py-8 sm:py-12 border rounded-lg bg-card"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <Award className="h-12 w-12 sm:h-16 sm:w-16 mx-auto text-muted-foreground/30 mb-3 sm:mb-4" />
                <h3 className="text-base sm:text-lg font-medium mb-2">No Certificates Yet</h3>
                {isTeacher ? (
                  <div className="px-4">
                    <p className="text-muted-foreground text-sm sm:text-base max-w-md mx-auto mb-4 sm:mb-6">
                      As a teacher, you can view certificates for courses you've completed.
                      Complete a course to earn a certificate that will appear here.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-2 justify-center max-w-xs mx-auto">
                      <Button
                        onClick={() => navigate('/dashboard')}
                        className="w-full sm:w-auto"
                      >
                        Explore Courses
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => navigate('/admin')}
                        className="w-full sm:w-auto"
                      >
                        Manage Courses
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="px-4">
                    <p className="text-muted-foreground text-sm sm:text-base max-w-md mx-auto mb-4 sm:mb-6">
                      Complete courses to earn certificates that will appear here.
                    </p>
                    <Button
                      onClick={() => navigate('/dashboard')}
                      className="w-full sm:w-auto max-w-xs mx-auto"
                    >
                      Explore Courses
                    </Button>
                  </div>
                )}
              </motion.div>
            )}
          </TabsContent>

          <TabsContent value="badges" className="space-y-6">
            {achievementsLoading ? (
              <div className="flex justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : filteredAchievements && filteredAchievements.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {filteredAchievements.map((achievement) => (
                  <BadgeCard
                    key={achievement.id}
                    achievement={achievement}
                    userId={user?.id || ''}
                    moduleInfo={(achievement as any).moduleInfo}
                    courseInfo={(achievement as any).courseInfo}
                  />
                ))}
              </div>
            ) : (
              <motion.div
                className="text-center py-8 sm:py-12 border rounded-lg bg-card"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <Trophy className="h-12 w-12 sm:h-16 sm:w-16 mx-auto text-muted-foreground/30 mb-3 sm:mb-4" />
                <h3 className="text-base sm:text-lg font-medium mb-2">No Badges Yet</h3>
                <p className="text-muted-foreground text-sm sm:text-base max-w-md mx-auto mb-4 sm:mb-6 px-4">
                  Complete modules and courses to earn badges that will appear here.
                </p>
                <Button
                  onClick={() => navigate('/dashboard')}
                  className="w-full sm:w-auto max-w-xs mx-auto"
                >
                  Explore Courses
                </Button>
              </motion.div>
            )}
          </TabsContent>
        </Tabs>
        </ContentSection>
      </PageContainer>
    </Layout>
  );
};

export default AchievementsPage;
