{"version": 3, "sources": ["../../prismjs/components/prism-jsx.js"], "sourcesContent": ["(function (Prism) {\n\n\tvar javascript = Prism.util.clone(Prism.languages.javascript);\n\n\tvar space = /(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source;\n\tvar braces = /(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source;\n\tvar spread = /(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source;\n\n\t/**\n\t * @param {string} source\n\t * @param {string} [flags]\n\t */\n\tfunction re(source, flags) {\n\t\tsource = source\n\t\t\t.replace(/<S>/g, function () { return space; })\n\t\t\t.replace(/<BRACES>/g, function () { return braces; })\n\t\t\t.replace(/<SPREAD>/g, function () { return spread; });\n\t\treturn RegExp(source, flags);\n\t}\n\n\tspread = re(spread).source;\n\n\n\tPrism.languages.jsx = Prism.languages.extend('markup', javascript);\n\tPrism.languages.jsx.tag.pattern = re(\n\t\t/<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/.source\n\t);\n\n\tPrism.languages.jsx.tag.inside['tag'].pattern = /^<\\/?[^\\s>\\/]*/;\n\tPrism.languages.jsx.tag.inside['attr-value'].pattern = /=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/;\n\tPrism.languages.jsx.tag.inside['tag'].inside['class-name'] = /^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/;\n\tPrism.languages.jsx.tag.inside['comment'] = javascript['comment'];\n\n\tPrism.languages.insertBefore('inside', 'attr-name', {\n\t\t'spread': {\n\t\t\tpattern: re(/<SPREAD>/.source),\n\t\t\tinside: Prism.languages.jsx\n\t\t}\n\t}, Prism.languages.jsx.tag);\n\n\tPrism.languages.insertBefore('inside', 'special-attr', {\n\t\t'script': {\n\t\t\t// Allow for two levels of nesting\n\t\t\tpattern: re(/=<BRACES>/.source),\n\t\t\talias: 'language-javascript',\n\t\t\tinside: {\n\t\t\t\t'script-punctuation': {\n\t\t\t\t\tpattern: /^=(?=\\{)/,\n\t\t\t\t\talias: 'punctuation'\n\t\t\t\t},\n\t\t\t\trest: Prism.languages.jsx\n\t\t\t},\n\t\t}\n\t}, Prism.languages.jsx.tag);\n\n\t// The following will handle plain text inside tags\n\tvar stringifyToken = function (token) {\n\t\tif (!token) {\n\t\t\treturn '';\n\t\t}\n\t\tif (typeof token === 'string') {\n\t\t\treturn token;\n\t\t}\n\t\tif (typeof token.content === 'string') {\n\t\t\treturn token.content;\n\t\t}\n\t\treturn token.content.map(stringifyToken).join('');\n\t};\n\n\tvar walkTokens = function (tokens) {\n\t\tvar openedTags = [];\n\t\tfor (var i = 0; i < tokens.length; i++) {\n\t\t\tvar token = tokens[i];\n\t\t\tvar notTagNorBrace = false;\n\n\t\t\tif (typeof token !== 'string') {\n\t\t\t\tif (token.type === 'tag' && token.content[0] && token.content[0].type === 'tag') {\n\t\t\t\t\t// We found a tag, now find its kind\n\n\t\t\t\t\tif (token.content[0].content[0].content === '</') {\n\t\t\t\t\t\t// Closing tag\n\t\t\t\t\t\tif (openedTags.length > 0 && openedTags[openedTags.length - 1].tagName === stringifyToken(token.content[0].content[1])) {\n\t\t\t\t\t\t\t// Pop matching opening tag\n\t\t\t\t\t\t\topenedTags.pop();\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (token.content[token.content.length - 1].content === '/>') {\n\t\t\t\t\t\t\t// Autoclosed tag, ignore\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Opening tag\n\t\t\t\t\t\t\topenedTags.push({\n\t\t\t\t\t\t\t\ttagName: stringifyToken(token.content[0].content[1]),\n\t\t\t\t\t\t\t\topenedBraces: 0\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (openedTags.length > 0 && token.type === 'punctuation' && token.content === '{') {\n\n\t\t\t\t\t// Here we might have entered a JSX context inside a tag\n\t\t\t\t\topenedTags[openedTags.length - 1].openedBraces++;\n\n\t\t\t\t} else if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces > 0 && token.type === 'punctuation' && token.content === '}') {\n\n\t\t\t\t\t// Here we might have left a JSX context inside a tag\n\t\t\t\t\topenedTags[openedTags.length - 1].openedBraces--;\n\n\t\t\t\t} else {\n\t\t\t\t\tnotTagNorBrace = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (notTagNorBrace || typeof token === 'string') {\n\t\t\t\tif (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces === 0) {\n\t\t\t\t\t// Here we are inside a tag, and not inside a JSX context.\n\t\t\t\t\t// That's plain text: drop any tokens matched.\n\t\t\t\t\tvar plainText = stringifyToken(token);\n\n\t\t\t\t\t// And merge text with adjacent text\n\t\t\t\t\tif (i < tokens.length - 1 && (typeof tokens[i + 1] === 'string' || tokens[i + 1].type === 'plain-text')) {\n\t\t\t\t\t\tplainText += stringifyToken(tokens[i + 1]);\n\t\t\t\t\t\ttokens.splice(i + 1, 1);\n\t\t\t\t\t}\n\t\t\t\t\tif (i > 0 && (typeof tokens[i - 1] === 'string' || tokens[i - 1].type === 'plain-text')) {\n\t\t\t\t\t\tplainText = stringifyToken(tokens[i - 1]) + plainText;\n\t\t\t\t\t\ttokens.splice(i - 1, 1);\n\t\t\t\t\t\ti--;\n\t\t\t\t\t}\n\n\t\t\t\t\ttokens[i] = new Prism.Token('plain-text', plainText, null, plainText);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (token.content && typeof token.content !== 'string') {\n\t\t\t\twalkTokens(token.content);\n\t\t\t}\n\t\t}\n\t};\n\n\tPrism.hooks.add('after-tokenize', function (env) {\n\t\tif (env.language !== 'jsx' && env.language !== 'tsx') {\n\t\t\treturn;\n\t\t}\n\t\twalkTokens(env.tokens);\n\t});\n\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAEjB,MAAI,aAAaA,OAAM,KAAK,MAAMA,OAAM,UAAU,UAAU;AAE5D,MAAI,QAAQ,+CAA+C;AAC3D,MAAI,SAAS,+CAA+C;AAC5D,MAAI,SAAS,uCAAuC;AAMpD,WAAS,GAAG,QAAQ,OAAO;AAC1B,aAAS,OACP,QAAQ,QAAQ,WAAY;AAAE,aAAO;AAAA,IAAO,CAAC,EAC7C,QAAQ,aAAa,WAAY;AAAE,aAAO;AAAA,IAAQ,CAAC,EACnD,QAAQ,aAAa,WAAY;AAAE,aAAO;AAAA,IAAQ,CAAC;AACrD,WAAO,OAAO,QAAQ,KAAK;AAAA,EAC5B;AAEA,WAAS,GAAG,MAAM,EAAE;AAGpB,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,UAAU,UAAU;AACjE,EAAAA,OAAM,UAAU,IAAI,IAAI,UAAU;AAAA,IACjC,wIAAwI;AAAA,EACzI;AAEA,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE,UAAU;AAChD,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,YAAY,EAAE,UAAU;AACvD,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE,OAAO,YAAY,IAAI;AAC7D,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,SAAS,IAAI,WAAW,SAAS;AAEhE,EAAAA,OAAM,UAAU,aAAa,UAAU,aAAa;AAAA,IACnD,UAAU;AAAA,MACT,SAAS,GAAG,WAAW,MAAM;AAAA,MAC7B,QAAQA,OAAM,UAAU;AAAA,IACzB;AAAA,EACD,GAAGA,OAAM,UAAU,IAAI,GAAG;AAE1B,EAAAA,OAAM,UAAU,aAAa,UAAU,gBAAgB;AAAA,IACtD,UAAU;AAAA;AAAA,MAET,SAAS,GAAG,YAAY,MAAM;AAAA,MAC9B,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,sBAAsB;AAAA,UACrB,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACA,MAAMA,OAAM,UAAU;AAAA,MACvB;AAAA,IACD;AAAA,EACD,GAAGA,OAAM,UAAU,IAAI,GAAG;AAG1B,MAAI,iBAAiB,SAAU,OAAO;AACrC,QAAI,CAAC,OAAO;AACX,aAAO;AAAA,IACR;AACA,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO;AAAA,IACR;AACA,QAAI,OAAO,MAAM,YAAY,UAAU;AACtC,aAAO,MAAM;AAAA,IACd;AACA,WAAO,MAAM,QAAQ,IAAI,cAAc,EAAE,KAAK,EAAE;AAAA,EACjD;AAEA,MAAI,aAAa,SAAU,QAAQ;AAClC,QAAI,aAAa,CAAC;AAClB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,iBAAiB;AAErB,UAAI,OAAO,UAAU,UAAU;AAC9B,YAAI,MAAM,SAAS,SAAS,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,EAAE,SAAS,OAAO;AAGhF,cAAI,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,YAAY,MAAM;AAEjD,gBAAI,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,YAAY,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG;AAEvH,yBAAW,IAAI;AAAA,YAChB;AAAA,UACD,OAAO;AACN,gBAAI,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE,YAAY,MAAM;AAAA,YAE9D,OAAO;AAEN,yBAAW,KAAK;AAAA,gBACf,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;AAAA,gBACnD,cAAc;AAAA,cACf,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,WAAW,WAAW,SAAS,KAAK,MAAM,SAAS,iBAAiB,MAAM,YAAY,KAAK;AAG1F,qBAAW,WAAW,SAAS,CAAC,EAAE;AAAA,QAEnC,WAAW,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,eAAe,KAAK,MAAM,SAAS,iBAAiB,MAAM,YAAY,KAAK;AAGhJ,qBAAW,WAAW,SAAS,CAAC,EAAE;AAAA,QAEnC,OAAO;AACN,2BAAiB;AAAA,QAClB;AAAA,MACD;AACA,UAAI,kBAAkB,OAAO,UAAU,UAAU;AAChD,YAAI,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,iBAAiB,GAAG;AAGlF,cAAI,YAAY,eAAe,KAAK;AAGpC,cAAI,IAAI,OAAO,SAAS,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,OAAO,IAAI,CAAC,EAAE,SAAS,eAAe;AACxG,yBAAa,eAAe,OAAO,IAAI,CAAC,CAAC;AACzC,mBAAO,OAAO,IAAI,GAAG,CAAC;AAAA,UACvB;AACA,cAAI,IAAI,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,OAAO,IAAI,CAAC,EAAE,SAAS,eAAe;AACxF,wBAAY,eAAe,OAAO,IAAI,CAAC,CAAC,IAAI;AAC5C,mBAAO,OAAO,IAAI,GAAG,CAAC;AACtB;AAAA,UACD;AAEA,iBAAO,CAAC,IAAI,IAAIA,OAAM,MAAM,cAAc,WAAW,MAAM,SAAS;AAAA,QACrE;AAAA,MACD;AAEA,UAAI,MAAM,WAAW,OAAO,MAAM,YAAY,UAAU;AACvD,mBAAW,MAAM,OAAO;AAAA,MACzB;AAAA,IACD;AAAA,EACD;AAEA,EAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAChD,QAAI,IAAI,aAAa,SAAS,IAAI,aAAa,OAAO;AACrD;AAAA,IACD;AACA,eAAW,IAAI,MAAM;AAAA,EACtB,CAAC;AAEF,GAAE,KAAK;", "names": ["Prism"]}