{"version": 3, "sources": ["../../prismjs/components/prism-typescript.js"], "sourcesContent": ["(function (Prism) {\n\n\tPrism.languages.typescript = Prism.languages.extend('javascript', {\n\t\t'class-name': {\n\t\t\tpattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: null // see below\n\t\t},\n\t\t'builtin': /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/,\n\t});\n\n\t// The keywords TypeScript adds to JavaScript\n\tPrism.languages.typescript.keyword.push(\n\t\t/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/,\n\t\t// keywords that have to be followed by an identifier\n\t\t/\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/,\n\t\t// This is for `import type *, {}`\n\t\t/\\btype\\b(?=\\s*(?:[\\{*]|$))/\n\t);\n\n\t// doesn't work with TS because TS is too complex\n\tdelete Prism.languages.typescript['parameter'];\n\tdelete Prism.languages.typescript['literal-property'];\n\n\t// a version of typescript specifically for highlighting types\n\tvar typeInside = Prism.languages.extend('typescript', {});\n\tdelete typeInside['class-name'];\n\n\tPrism.languages.typescript['class-name'].inside = typeInside;\n\n\tPrism.languages.insertBefore('typescript', 'function', {\n\t\t'decorator': {\n\t\t\tpattern: /@[$\\w\\xA0-\\uFFFF]+/,\n\t\t\tinside: {\n\t\t\t\t'at': {\n\t\t\t\t\tpattern: /^@/,\n\t\t\t\t\talias: 'operator'\n\t\t\t\t},\n\t\t\t\t'function': /^[\\s\\S]+/\n\t\t\t}\n\t\t},\n\t\t'generic-function': {\n\t\t\t// e.g. foo<T extends \"bar\" | \"baz\">( ...\n\t\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n\t\t\t\t'generic': {\n\t\t\t\t\tpattern: /<[\\s\\S]+/, // everything after the first <\n\t\t\t\t\talias: 'class-name',\n\t\t\t\t\tinside: typeInside\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\tPrism.languages.ts = Prism.languages.typescript;\n\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAEjB,EAAAA,OAAM,UAAU,aAAaA,OAAM,UAAU,OAAO,cAAc;AAAA,IACjE,cAAc;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA;AAAA,IACT;AAAA,IACA,WAAW;AAAA,EACZ,CAAC;AAGD,EAAAA,OAAM,UAAU,WAAW,QAAQ;AAAA,IAClC;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,EACD;AAGA,SAAOA,OAAM,UAAU,WAAW,WAAW;AAC7C,SAAOA,OAAM,UAAU,WAAW,kBAAkB;AAGpD,MAAI,aAAaA,OAAM,UAAU,OAAO,cAAc,CAAC,CAAC;AACxD,SAAO,WAAW,YAAY;AAE9B,EAAAA,OAAM,UAAU,WAAW,YAAY,EAAE,SAAS;AAElD,EAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,IACtD,aAAa;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,QACP,MAAM;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACb;AAAA,IACD;AAAA,IACA,oBAAoB;AAAA;AAAA,MAEnB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,YAAY;AAAA,QACZ,WAAW;AAAA,UACV,SAAS;AAAA;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAEtC,GAAE,KAAK;", "names": ["Prism"]}