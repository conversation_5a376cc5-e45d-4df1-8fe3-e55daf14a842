{"version": 3, "sources": ["../../node_modules/highlight.js/lib/core.js", "../../@tiptap/extension-code-block-lowlight/src/lowlight-plugin.ts", "../../@tiptap/extension-code-block-lowlight/src/code-block-lowlight.ts"], "sourcesContent": ["/* eslint-disable no-multi-assign */\n\nfunction deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear =\n      obj.delete =\n      obj.set =\n        function () {\n          throw new Error('map is read-only');\n        };\n  } else if (obj instanceof Set) {\n    obj.add =\n      obj.clear =\n      obj.delete =\n        function () {\n          throw new Error('set is read-only');\n        };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n\n  Object.getOwnPropertyNames(obj).forEach((name) => {\n    const prop = obj[name];\n    const type = typeof prop;\n\n    // Freeze prop if it is an object or function and also not already frozen\n    if ((type === 'object' || type === 'function') && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n\n  return obj;\n}\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\n\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope;\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nconst scopeToCSSClass = (name, { prefix }) => {\n  // sub-language\n  if (name.startsWith(\"language:\")) {\n    return name.replace(\"language:\", \"language-\");\n  }\n  // tiered scope: comment.line\n  if (name.includes(\".\")) {\n    const pieces = name.split(\".\");\n    return [\n      `${prefix}${pieces.shift()}`,\n      ...(pieces.map((x, i) => `${x}${\"_\".repeat(i + 1)}`))\n    ].join(\" \");\n  }\n  // simple scope\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    const className = scopeToCSSClass(node.scope,\n      { prefix: this.classPrefix });\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{scope?: string, language?: string, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n\n/** @returns {DataNode} */\nconst newNode = (opts = {}) => {\n  /** @type DataNode */\n  const result = { children: [] };\n  Object.assign(result, opts);\n  return result;\n};\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} scope */\n  openNode(scope) {\n    /** @type Node */\n    const node = newNode({ scope });\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addText(text)\n  - __addSublanguage(emitter, subLanguageName)\n  - startScope(scope)\n  - endScope()\n  - finalize()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /** @param {string} scope */\n  startScope(scope) {\n    this.openNode(scope);\n  }\n\n  endScope() {\n    this.closeNode();\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  __addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    if (name) node.scope = `language:${name}`;\n\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    this.closeAllNodes();\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, { joinWith }) {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit$1(\n    {\n      scope: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  const ENGLISH_WORD = either(\n    // list of common 1 and 2 letter words in English\n    \"I\",\n    \"a\",\n    \"is\",\n    \"so\",\n    \"us\",\n    \"to\",\n    \"at\",\n    \"if\",\n    \"in\",\n    \"it\",\n    \"on\",\n    // note: this is not an exhaustive list of contractions, just popular ones\n    /[A-Za-z]+['](d|ve|re|ll|t|s|n)/, // contractions - can't we'd they're let's, etc\n    /[A-Za-z]+[-][a-z]+/, // `no-way`, etc.\n    /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push(\n    {\n      // TODO: how to include \", (, ) without breaking grammars that use these for\n      // comment delimiters?\n      // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n      // ---\n\n      // this tries to find sequences of 3 english words in a row (without any\n      // \"programming\" type syntax) this gives us a strong signal that we've\n      // TRULY found a comment - vs perhaps scanning with the wrong language.\n      // It's possible to find something that LOOKS like the start of the\n      // comment - but then if there is no readable text - good chance it is a\n      // false match and not a comment.\n      //\n      // for a visual example please see:\n      // https://github.com/highlightjs/highlight.js/issues/2827\n\n      begin: concat(\n        /[ ]+/, // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n        '(',\n        ENGLISH_WORD,\n        /[.]?[:]?([.][ ]|[ ])/,\n        '){3}') // look for 3 words in a row\n    }\n  );\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  scope: \"regexp\",\n  begin: /\\/(?=[^/\\n]*\\/)/,\n  end: /\\/[gimuy]*/,\n  contains: [\n    BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [BACKSLASH_ESCAPE]\n    }\n  ]\n};\nconst TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  COMMENT: COMMENT,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  IDENT_RE: IDENT_RE,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  METHOD_GUARD: METHOD_GUARD,\n  NUMBER_MODE: NUMBER_MODE,\n  NUMBER_RE: NUMBER_RE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nconst beforeMatchExt = (mode, parent) => {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n  const originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [\n      Object.assign(originalMode, { endsParent: true })\n    ]\n  };\n  mode.relevance = 0;\n\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, scopeName = DEFAULT_KEYWORD_SCOPE) {\n  /** @type {import(\"highlight.js/private\").KeywordDict} */\n  const compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nconst MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, { key }) {\n  let offset = 0;\n  const scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  const emit = {};\n  /** @type Record<number,string> */\n  const positions = {};\n\n  for (let i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.begin, { key: \"beginScope\" });\n  mode.begin = _rewriteBackreferences(mode.begin, { joinWith: \"\" });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.end, { key: \"endScope\" });\n  mode.end = _rewriteBackreferences(mode.end, { joinWith: \"\" });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = { _wrap: mode.beginScope };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = { _wrap: mode.endScope };\n  }\n\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm'\n      + (language.case_insensitive ? 'i' : '')\n      + (language.unicodeRegex ? 'u' : '')\n      + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(_rewriteBackreferences(terminators, { joinWith: '|' }), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      scopeClassName,\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch,\n      MultiClass,\n      beforeMatchExt\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit$1(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, { starts: mode.starts ? inherit$1(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"11.10.0\";\n\nclass HTMLInjectionError extends Error {\n  constructor(reason, html) {\n    super(reason);\n    this.name = \"HTMLInjectionError\";\n    this.html = html;\n  }\n}\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\n\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\n\n\nconst escape = escapeHTML;\nconst inherit = inherit$1;\nconst NO_MATCH = Symbol(\"nomatch\");\nconst MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) { ignoreIllegals = true; }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    const keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        const data = keywordData(top, word);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result._top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.__addSublanguage(result._emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {string} text\n     * @param {string} scope\n     */\n    function emitKeyword(keyword, scope) {\n      if (keyword === \"\") return;\n\n      emitter.startScope(scope);\n      emitter.addText(keyword);\n      emitter.endScope();\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      let i = 1;\n      const max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) { i++; continue; }\n        const klass = language.classNameAliases[scope[i]] || scope[i];\n        const text = match[i];\n        if (klass) {\n          emitKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substring(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language);\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      if (!language.__emitTokens) {\n        top.matcher.considerAll();\n\n        for (;;) {\n          iterations++;\n          if (resumeScanAtSamePosition) {\n            // only regexes not matched previously will now be\n            // considered for a potential match\n            resumeScanAtSamePosition = false;\n          } else {\n            top.matcher.considerAll();\n          }\n          top.matcher.lastIndex = index;\n\n          const match = top.matcher.exec(codeToHighlight);\n          // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n          if (!match) break;\n\n          const beforeMatch = codeToHighlight.substring(index, match.index);\n          const processedCount = processLexeme(beforeMatch, match);\n          index = match.index + processedCount;\n        }\n        processLexeme(codeToHighlight.substring(index));\n      } else {\n        language.__emitTokens(codeToHighlight, emitter);\n      }\n\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        language: languageName,\n        value: result,\n        relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.secondBest = secondBest;\n\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = (currentLang && aliases[currentLang]) || resultLang;\n\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    fire(\"before:highlightElement\",\n      { el: element, language });\n\n    if (element.dataset.highlighted) {\n      console.log(\"Element previously highlighted. To highlight again, first unset `dataset.highlighted`.\", element);\n      return;\n    }\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        const err = new HTMLInjectionError(\n          \"One of your code blocks includes unescaped HTML.\",\n          element.innerHTML\n        );\n        throw err;\n      }\n    }\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    element.innerHTML = result.value;\n    element.dataset.highlighted = \"yes\";\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n\n    fire(\"after:highlightElement\", { el: element, result, text });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function removePlugin(plugin) {\n    const index = plugins.indexOf(plugin);\n    if (index !== -1) {\n      plugins.splice(index, 1);\n    }\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin,\n    removePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreeze(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n\n  return hljs;\n};\n\n// Other names for the variable may break build script\nconst highlight = HLJS({});\n\n// returns a new instance of the highlighter to be used for extensions\n// check https://github.com/wooorm/lowlight/issues/47\nhighlight.newInstance = () => HLJS({});\n\nmodule.exports = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;\n", "import { findChildren } from '@tiptap/core'\nimport { Node as ProsemirrorNode } from '@tiptap/pm/model'\nimport { <PERSON>lug<PERSON>, PluginKey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n// @ts-ignore\nimport highlight from 'highlight.js/lib/core'\n\nfunction parseNodes(nodes: any[], className: string[] = []): { text: string; classes: string[] }[] {\n  return nodes\n    .map(node => {\n      const classes = [...className, ...(node.properties ? node.properties.className : [])]\n\n      if (node.children) {\n        return parseNodes(node.children, classes)\n      }\n\n      return {\n        text: node.value,\n        classes,\n      }\n    })\n    .flat()\n}\n\nfunction getHighlightNodes(result: any) {\n  // `.value` for lowlight v1, `.children` for lowlight v2\n  return result.value || result.children || []\n}\n\nfunction registered(aliasOrLanguage: string) {\n  return Boolean(highlight.getLanguage(aliasOrLanguage))\n}\n\nfunction getDecorations({\n  doc,\n  name,\n  lowlight,\n  defaultLanguage,\n}: {\n  doc: ProsemirrorNode\n  name: string\n  lowlight: any\n  defaultLanguage: string | null | undefined\n}) {\n  const decorations: Decoration[] = []\n\n  findChildren(doc, node => node.type.name === name).forEach(block => {\n    let from = block.pos + 1\n    const language = block.node.attrs.language || defaultLanguage\n    const languages = lowlight.listLanguages()\n\n    const nodes = language && (languages.includes(language) || registered(language) || lowlight.registered?.(language))\n      ? getHighlightNodes(lowlight.highlight(language, block.node.textContent))\n      : getHighlightNodes(lowlight.highlightAuto(block.node.textContent))\n\n    parseNodes(nodes).forEach(node => {\n      const to = from + node.text.length\n\n      if (node.classes.length) {\n        const decoration = Decoration.inline(from, to, {\n          class: node.classes.join(' '),\n        })\n\n        decorations.push(decoration)\n      }\n\n      from = to\n    })\n  })\n\n  return DecorationSet.create(doc, decorations)\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction isFunction(param: any): param is Function {\n  return typeof param === 'function'\n}\n\nexport function LowlightPlugin({\n  name,\n  lowlight,\n  defaultLanguage,\n}: {\n  name: string\n  lowlight: any\n  defaultLanguage: string | null | undefined\n}) {\n  if (!['highlight', 'highlightAuto', 'listLanguages'].every(api => isFunction(lowlight[api]))) {\n    throw Error(\n      'You should provide an instance of lowlight to use the code-block-lowlight extension',\n    )\n  }\n\n  const lowlightPlugin: Plugin<any> = new Plugin({\n    key: new PluginKey('lowlight'),\n\n    state: {\n      init: (_, { doc }) => getDecorations({\n        doc,\n        name,\n        lowlight,\n        defaultLanguage,\n      }),\n      apply: (transaction, decorationSet, oldState, newState) => {\n        const oldNodeName = oldState.selection.$head.parent.type.name\n        const newNodeName = newState.selection.$head.parent.type.name\n        const oldNodes = findChildren(oldState.doc, node => node.type.name === name)\n        const newNodes = findChildren(newState.doc, node => node.type.name === name)\n\n        if (\n          transaction.docChanged\n          // Apply decorations if:\n          // selection includes named node,\n          && ([oldNodeName, newNodeName].includes(name)\n            // OR transaction adds/removes named node,\n            || newNodes.length !== oldNodes.length\n            // OR transaction has changes that completely encapsulte a node\n            // (for example, a transaction that affects the entire document).\n            // Such transactions can happen during collab syncing via y-prosemirror, for example.\n            || transaction.steps.some(step => {\n              // @ts-ignore\n              return (\n                // @ts-ignore\n                step.from !== undefined\n                // @ts-ignore\n                && step.to !== undefined\n                && oldNodes.some(node => {\n                  // @ts-ignore\n                  return (\n                    // @ts-ignore\n                    node.pos >= step.from\n                    // @ts-ignore\n                    && node.pos + node.node.nodeSize <= step.to\n                  )\n                })\n              )\n            }))\n        ) {\n          return getDecorations({\n            doc: transaction.doc,\n            name,\n            lowlight,\n            defaultLanguage,\n          })\n        }\n\n        return decorationSet.map(transaction.mapping, transaction.doc)\n      },\n    },\n\n    props: {\n      decorations(state) {\n        return lowlightPlugin.getState(state)\n      },\n    },\n  })\n\n  return lowlightPlugin\n}\n", "import CodeBlock, { CodeBlockOptions } from '@tiptap/extension-code-block'\n\nimport { LowlightPlugin } from './lowlight-plugin.js'\n\nexport interface CodeBlockLowlightOptions extends CodeBlockOptions {\n  /**\n   * The lowlight instance.\n   */\n  lowlight: any,\n}\n\n/**\n * This extension allows you to highlight code blocks with lowlight.\n * @see https://tiptap.dev/api/nodes/code-block-lowlight\n */\nexport const CodeBlockLowlight = CodeBlock.extend<CodeBlockLowlightOptions>({\n  addOptions() {\n    return {\n      ...this.parent?.(),\n      lowlight: {},\n      languageClassPrefix: 'language-',\n      exitOnTripleEnter: true,\n      exitOnArrowDown: true,\n      defaultLanguage: null,\n      HTMLAttributes: {},\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      ...this.parent?.() || [],\n      LowlightPlugin({\n        name: this.name,\n        lowlight: this.options.lowlight,\n        defaultLanguage: this.options.defaultLanguage,\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;AAEA,SAAS,WAAW,KAAK;AACvB,MAAI,eAAe,KAAK;AACtB,QAAI,QACF,IAAI,SACJ,IAAI,MACF,WAAY;AACV,YAAM,IAAI,MAAM,kBAAkB;IAC5C;EACA,WAAa,eAAe,KAAK;AAC7B,QAAI,MACF,IAAI,QACJ,IAAI,SACF,WAAY;AACV,YAAM,IAAI,MAAM,kBAAkB;IAC5C;EACA;AAGE,SAAO,OAAO,GAAG;AAEjB,SAAO,oBAAoB,GAAG,EAAE,QAAQ,CAAC,SAAS;AAChD,UAAM,OAAO,IAAI,IAAI;AACrB,UAAM,OAAO,OAAO;AAGpB,SAAK,SAAS,YAAY,SAAS,eAAe,CAAC,OAAO,SAAS,IAAI,GAAG;AACxE,iBAAW,IAAI;IACrB;EACA,CAAG;AAED,SAAO;AACT;AAMA,IAAM,WAAN,MAAe;;;;EAIb,YAAY,MAAM;AAEhB,QAAI,KAAK,SAAS,OAAW,MAAK,OAAO,CAAA;AAEzC,SAAK,OAAO,KAAK;AACjB,SAAK,iBAAiB;EAC1B;EAEE,cAAc;AACZ,SAAK,iBAAiB;EAC1B;AACA;AAMA,SAAS,WAAW,OAAO;AACzB,SAAO,MACJ,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ;AAC3B;AAUA,SAAS,UAAU,aAAa,SAAS;AAEvC,QAAM,SAAS,uBAAO,OAAO,IAAI;AAEjC,aAAW,OAAO,UAAU;AAC1B,WAAO,GAAG,IAAI,SAAS,GAAG;EAC9B;AACE,UAAQ,QAAQ,SAAS,KAAK;AAC5B,eAAW,OAAO,KAAK;AACrB,aAAO,GAAG,IAAI,IAAI,GAAG;IAC3B;EACA,CAAG;AACD;;IAAyB;;AAC3B;AAcA,IAAM,aAAa;AAMnB,IAAM,oBAAoB,CAAC,SAAS;AAGlC,SAAO,CAAC,CAAC,KAAK;AAChB;AAOA,IAAM,kBAAkB,CAAC,MAAM,EAAE,OAAM,MAAO;AAE5C,MAAI,KAAK,WAAW,WAAW,GAAG;AAChC,WAAO,KAAK,QAAQ,aAAa,WAAW;EAChD;AAEE,MAAI,KAAK,SAAS,GAAG,GAAG;AACtB,UAAM,SAAS,KAAK,MAAM,GAAG;AAC7B,WAAO;MACL,GAAG,MAAM,GAAG,OAAO,MAAK,CAAE;MAC1B,GAAI,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC,CAAC,EAAE;IACzD,EAAM,KAAK,GAAG;EACd;AAEE,SAAO,GAAG,MAAM,GAAG,IAAI;AACzB;AAGA,IAAM,eAAN,MAAmB;;;;;;;EAOjB,YAAY,WAAW,SAAS;AAC9B,SAAK,SAAS;AACd,SAAK,cAAc,QAAQ;AAC3B,cAAU,KAAK,IAAI;EACvB;;;;;EAME,QAAQ,MAAM;AACZ,SAAK,UAAU,WAAW,IAAI;EAClC;;;;;EAME,SAAS,MAAM;AACb,QAAI,CAAC,kBAAkB,IAAI,EAAG;AAE9B,UAAM,YAAY;MAAgB,KAAK;MACrC,EAAE,QAAQ,KAAK,YAAW;IAAE;AAC9B,SAAK,KAAK,SAAS;EACvB;;;;;EAME,UAAU,MAAM;AACd,QAAI,CAAC,kBAAkB,IAAI,EAAG;AAE9B,SAAK,UAAU;EACnB;;;;EAKE,QAAQ;AACN,WAAO,KAAK;EAChB;;;;;;EAQE,KAAK,WAAW;AACd,SAAK,UAAU,gBAAgB,SAAS;EAC5C;AACA;AAQA,IAAM,UAAU,CAAC,OAAO,CAAA,MAAO;AAE7B,QAAM,SAAS,EAAE,UAAU,CAAA,EAAE;AAC7B,SAAO,OAAO,QAAQ,IAAI;AAC1B,SAAO;AACT;AAEA,IAAM,YAAN,MAAM,WAAU;EACd,cAAc;AAEZ,SAAK,WAAW,QAAO;AACvB,SAAK,QAAQ,CAAC,KAAK,QAAQ;EAC/B;EAEE,IAAI,MAAM;AACR,WAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;EAC3C;EAEE,IAAI,OAAO;AAAE,WAAO,KAAK;EAAS;;EAGlC,IAAI,MAAM;AACR,SAAK,IAAI,SAAS,KAAK,IAAI;EAC/B;;EAGE,SAAS,OAAO;AAEd,UAAM,OAAO,QAAQ,EAAE,MAAK,CAAE;AAC9B,SAAK,IAAI,IAAI;AACb,SAAK,MAAM,KAAK,IAAI;EACxB;EAEE,YAAY;AACV,QAAI,KAAK,MAAM,SAAS,GAAG;AACzB,aAAO,KAAK,MAAM,IAAG;IAC3B;AAEI,WAAO;EACX;EAEE,gBAAgB;AACd,WAAO,KAAK,UAAS,EAAG;EAC5B;EAEE,SAAS;AACP,WAAO,KAAK,UAAU,KAAK,UAAU,MAAM,CAAC;EAChD;;;;;EAME,KAAK,SAAS;AAEZ,WAAO,KAAK,YAAY,MAAM,SAAS,KAAK,QAAQ;EAGxD;;;;;EAME,OAAO,MAAM,SAAS,MAAM;AAC1B,QAAI,OAAO,SAAS,UAAU;AAC5B,cAAQ,QAAQ,IAAI;IAC1B,WAAe,KAAK,UAAU;AACxB,cAAQ,SAAS,IAAI;AACrB,WAAK,SAAS,QAAQ,CAAC,UAAU,KAAK,MAAM,SAAS,KAAK,CAAC;AAC3D,cAAQ,UAAU,IAAI;IAC5B;AACI,WAAO;EACX;;;;EAKE,OAAO,UAAU,MAAM;AACrB,QAAI,OAAO,SAAS,SAAU;AAC9B,QAAI,CAAC,KAAK,SAAU;AAEpB,QAAI,KAAK,SAAS,MAAM,QAAM,OAAO,OAAO,QAAQ,GAAG;AAGrD,WAAK,WAAW,CAAC,KAAK,SAAS,KAAK,EAAE,CAAC;IAC7C,OAAW;AACL,WAAK,SAAS,QAAQ,CAAC,UAAU;AAC/B,mBAAU,UAAU,KAAK;MACjC,CAAO;IACP;EACA;AACA;AAoBA,IAAM,mBAAN,cAA+B,UAAU;;;;EAIvC,YAAY,SAAS;AACnB,UAAK;AACL,SAAK,UAAU;EACnB;;;;EAKE,QAAQ,MAAM;AACZ,QAAI,SAAS,IAAI;AAAE;IAAO;AAE1B,SAAK,IAAI,IAAI;EACjB;;EAGE,WAAW,OAAO;AAChB,SAAK,SAAS,KAAK;EACvB;EAEE,WAAW;AACT,SAAK,UAAS;EAClB;;;;;EAME,iBAAiB,SAAS,MAAM;AAE9B,UAAM,OAAO,QAAQ;AACrB,QAAI,KAAM,MAAK,QAAQ,YAAY,IAAI;AAEvC,SAAK,IAAI,IAAI;EACjB;EAEE,SAAS;AACP,UAAM,WAAW,IAAI,aAAa,MAAM,KAAK,OAAO;AACpD,WAAO,SAAS,MAAK;EACzB;EAEE,WAAW;AACT,SAAK,cAAa;AAClB,WAAO;EACX;AACA;AAWA,SAAS,OAAO,IAAI;AAClB,MAAI,CAAC,GAAI,QAAO;AAChB,MAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,SAAO,GAAG;AACZ;AAMA,SAAS,UAAU,IAAI;AACrB,SAAO,OAAO,OAAO,IAAI,GAAG;AAC9B;AAMA,SAAS,iBAAiB,IAAI;AAC5B,SAAO,OAAO,OAAO,IAAI,IAAI;AAC/B;AAMA,SAAS,SAAS,IAAI;AACpB,SAAO,OAAO,OAAO,IAAI,IAAI;AAC/B;AAMA,SAAS,UAAU,MAAM;AACvB,QAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,SAAO;AACT;AAMA,SAAS,qBAAqB,MAAM;AAClC,QAAM,OAAO,KAAK,KAAK,SAAS,CAAC;AAEjC,MAAI,OAAO,SAAS,YAAY,KAAK,gBAAgB,QAAQ;AAC3D,SAAK,OAAO,KAAK,SAAS,GAAG,CAAC;AAC9B,WAAO;EACX,OAAS;AACL,WAAO,CAAA;EACX;AACA;AAWA,SAAS,UAAU,MAAM;AAEvB,QAAM,OAAO,qBAAqB,IAAI;AACtC,QAAM,SAAS,OACV,KAAK,UAAU,KAAK,QACrB,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC3C,SAAO;AACT;AAMA,SAAS,iBAAiB,IAAI;AAC5B,SAAQ,IAAI,OAAO,GAAG,SAAQ,IAAK,GAAG,EAAG,KAAK,EAAE,EAAE,SAAS;AAC7D;AAOA,SAAS,WAAW,IAAI,QAAQ;AAC9B,QAAM,QAAQ,MAAM,GAAG,KAAK,MAAM;AAClC,SAAO,SAAS,MAAM,UAAU;AAClC;AASA,IAAM,aAAa;AAanB,SAAS,uBAAuB,SAAS,EAAE,SAAQ,GAAI;AACrD,MAAI,cAAc;AAElB,SAAO,QAAQ,IAAI,CAAC,UAAU;AAC5B,mBAAe;AACf,UAAM,SAAS;AACf,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM;AAEV,WAAO,GAAG,SAAS,GAAG;AACpB,YAAM,QAAQ,WAAW,KAAK,EAAE;AAChC,UAAI,CAAC,OAAO;AACV,eAAO;AACP;MACR;AACM,aAAO,GAAG,UAAU,GAAG,MAAM,KAAK;AAClC,WAAK,GAAG,UAAU,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM;AAC/C,UAAI,MAAM,CAAC,EAAE,CAAC,MAAM,QAAQ,MAAM,CAAC,GAAG;AAEpC,eAAO,OAAO,OAAO,OAAO,MAAM,CAAC,CAAC,IAAI,MAAM;MACtD,OAAa;AACL,eAAO,MAAM,CAAC;AACd,YAAI,MAAM,CAAC,MAAM,KAAK;AACpB;QACV;MACA;IACA;AACI,WAAO;EACX,CAAG,EAAE,IAAI,QAAM,IAAI,EAAE,GAAG,EAAE,KAAK,QAAQ;AACvC;AAMA,IAAM,mBAAmB;AACzB,IAAM,WAAW;AACjB,IAAM,sBAAsB;AAC5B,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,mBAAmB;AACzB,IAAM,iBAAiB;AAKvB,IAAM,UAAU,CAAC,OAAO,CAAA,MAAO;AAC7B,QAAM,eAAe;AACrB,MAAI,KAAK,QAAQ;AACf,SAAK,QAAQ;MACX;MACA;MACA,KAAK;MACL;IAAM;EACZ;AACE,SAAO,UAAU;IACf,OAAO;IACP,OAAO;IACP,KAAK;IACL,WAAW;;IAEX,YAAY,CAAC,GAAG,SAAS;AACvB,UAAI,EAAE,UAAU,EAAG,MAAK,YAAW;IACzC;EACA,GAAK,IAAI;AACT;AAGA,IAAM,mBAAmB;EACvB,OAAO;EAAgB,WAAW;AACpC;AACA,IAAM,mBAAmB;EACvB,OAAO;EACP,OAAO;EACP,KAAK;EACL,SAAS;EACT,UAAU,CAAC,gBAAgB;AAC7B;AACA,IAAM,oBAAoB;EACxB,OAAO;EACP,OAAO;EACP,KAAK;EACL,SAAS;EACT,UAAU,CAAC,gBAAgB;AAC7B;AACA,IAAM,qBAAqB;EACzB,OAAO;AACT;AASA,IAAM,UAAU,SAAS,OAAO,KAAK,cAAc,CAAA,GAAI;AACrD,QAAM,OAAO;IACX;MACE,OAAO;MACP;MACA;MACA,UAAU,CAAA;IAChB;IACI;EACJ;AACE,OAAK,SAAS,KAAK;IACjB,OAAO;;;IAGP,OAAO;IACP,KAAK;IACL,cAAc;IACd,WAAW;EACf,CAAG;AACD,QAAM,eAAe;;IAEnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;;IACA;;IACA;;EACJ;AAEE,OAAK,SAAS;IACZ;;;;;;;;;;;;;;MAgBE,OAAO;QACL;;QACA;QACA;QACA;QACA;MAAM;;IACd;EACA;AACE,SAAO;AACT;AACA,IAAM,sBAAsB,QAAQ,MAAM,GAAG;AAC7C,IAAM,uBAAuB,QAAQ,QAAQ,MAAM;AACnD,IAAM,oBAAoB,QAAQ,KAAK,GAAG;AAC1C,IAAM,cAAc;EAClB,OAAO;EACP,OAAO;EACP,WAAW;AACb;AACA,IAAM,gBAAgB;EACpB,OAAO;EACP,OAAO;EACP,WAAW;AACb;AACA,IAAM,qBAAqB;EACzB,OAAO;EACP,OAAO;EACP,WAAW;AACb;AACA,IAAM,cAAc;EAClB,OAAO;EACP,OAAO;EACP,KAAK;EACL,UAAU;IACR;IACA;MACE,OAAO;MACP,KAAK;MACL,WAAW;MACX,UAAU,CAAC,gBAAgB;IACjC;EACA;AACA;AACA,IAAM,aAAa;EACjB,OAAO;EACP,OAAO;EACP,WAAW;AACb;AACA,IAAM,wBAAwB;EAC5B,OAAO;EACP,OAAO;EACP,WAAW;AACb;AACA,IAAM,eAAe;;EAEnB,OAAO,YAAY;EACnB,WAAW;AACb;AASA,IAAM,oBAAoB,SAAS,MAAM;AACvC,SAAO,OAAO;IAAO;IACnB;;MAEE,YAAY,CAAC,GAAG,SAAS;AAAE,aAAK,KAAK,cAAc,EAAE,CAAC;MAAE;;MAExD,UAAU,CAAC,GAAG,SAAS;AAAE,YAAI,KAAK,KAAK,gBAAgB,EAAE,CAAC,EAAG,MAAK,YAAW;MAAG;IACtF;EAAK;AACL;AAEA,IAAI,QAAqB,OAAO,OAAO;EACrC,WAAW;EACX;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,CAAC;AA+BD,SAAS,sBAAsB,OAAO,UAAU;AAC9C,QAAM,SAAS,MAAM,MAAM,MAAM,QAAQ,CAAC;AAC1C,MAAI,WAAW,KAAK;AAClB,aAAS,YAAW;EACxB;AACA;AAMA,SAAS,eAAe,MAAM,SAAS;AAErC,MAAI,KAAK,cAAc,QAAW;AAChC,SAAK,QAAQ,KAAK;AAClB,WAAO,KAAK;EAChB;AACA;AAMA,SAAS,cAAc,MAAM,QAAQ;AACnC,MAAI,CAAC,OAAQ;AACb,MAAI,CAAC,KAAK,cAAe;AAOzB,OAAK,QAAQ,SAAS,KAAK,cAAc,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI;AAChE,OAAK,gBAAgB;AACrB,OAAK,WAAW,KAAK,YAAY,KAAK;AACtC,SAAO,KAAK;AAKZ,MAAI,KAAK,cAAc,OAAW,MAAK,YAAY;AACrD;AAMA,SAAS,eAAe,MAAM,SAAS;AACrC,MAAI,CAAC,MAAM,QAAQ,KAAK,OAAO,EAAG;AAElC,OAAK,UAAU,OAAO,GAAG,KAAK,OAAO;AACvC;AAMA,SAAS,aAAa,MAAM,SAAS;AACnC,MAAI,CAAC,KAAK,MAAO;AACjB,MAAI,KAAK,SAAS,KAAK,IAAK,OAAM,IAAI,MAAM,0CAA0C;AAEtF,OAAK,QAAQ,KAAK;AAClB,SAAO,KAAK;AACd;AAMA,SAAS,iBAAiB,MAAM,SAAS;AAEvC,MAAI,KAAK,cAAc,OAAW,MAAK,YAAY;AACrD;AAIA,IAAM,iBAAiB,CAAC,MAAM,WAAW;AACvC,MAAI,CAAC,KAAK,YAAa;AAGvB,MAAI,KAAK,OAAQ,OAAM,IAAI,MAAM,wCAAwC;AAEzE,QAAM,eAAe,OAAO,OAAO,CAAA,GAAI,IAAI;AAC3C,SAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,QAAQ;AAAE,WAAO,KAAK,GAAG;EAAE,CAAE;AAExD,OAAK,WAAW,aAAa;AAC7B,OAAK,QAAQ,OAAO,aAAa,aAAa,UAAU,aAAa,KAAK,CAAC;AAC3E,OAAK,SAAS;IACZ,WAAW;IACX,UAAU;MACR,OAAO,OAAO,cAAc,EAAE,YAAY,KAAI,CAAE;IACtD;EACA;AACE,OAAK,YAAY;AAEjB,SAAO,aAAa;AACtB;AAGA,IAAM,kBAAkB;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA;;EACA;;AACF;AAEA,IAAM,wBAAwB;AAQ9B,SAAS,gBAAgB,aAAa,iBAAiB,YAAY,uBAAuB;AAExF,QAAM,mBAAmB,uBAAO,OAAO,IAAI;AAI3C,MAAI,OAAO,gBAAgB,UAAU;AACnC,gBAAY,WAAW,YAAY,MAAM,GAAG,CAAC;EACjD,WAAa,MAAM,QAAQ,WAAW,GAAG;AACrC,gBAAY,WAAW,WAAW;EACtC,OAAS;AACL,WAAO,KAAK,WAAW,EAAE,QAAQ,SAASA,YAAW;AAEnD,aAAO;QACL;QACA,gBAAgB,YAAYA,UAAS,GAAG,iBAAiBA,UAAS;MAC1E;IACA,CAAK;EACL;AACE,SAAO;AAYP,WAAS,YAAYA,YAAW,aAAa;AAC3C,QAAI,iBAAiB;AACnB,oBAAc,YAAY,IAAI,OAAK,EAAE,YAAW,CAAE;IACxD;AACI,gBAAY,QAAQ,SAAS,SAAS;AACpC,YAAM,OAAO,QAAQ,MAAM,GAAG;AAC9B,uBAAiB,KAAK,CAAC,CAAC,IAAI,CAACA,YAAW,gBAAgB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IAC/E,CAAK;EACL;AACA;AAUA,SAAS,gBAAgB,SAAS,eAAe;AAG/C,MAAI,eAAe;AACjB,WAAO,OAAO,aAAa;EAC/B;AAEE,SAAO,cAAc,OAAO,IAAI,IAAI;AACtC;AAMA,SAAS,cAAc,SAAS;AAC9B,SAAO,gBAAgB,SAAS,QAAQ,YAAW,CAAE;AACvD;AAYA,IAAM,mBAAmB,CAAA;AAKzB,IAAM,QAAQ,CAAC,YAAY;AACzB,UAAQ,MAAM,OAAO;AACvB;AAMA,IAAM,OAAO,CAAC,YAAY,SAAS;AACjC,UAAQ,IAAI,SAAS,OAAO,IAAI,GAAG,IAAI;AACzC;AAMA,IAAM,aAAa,CAACC,UAAS,YAAY;AACvC,MAAI,iBAAiB,GAAGA,QAAO,IAAI,OAAO,EAAE,EAAG;AAE/C,UAAQ,IAAI,oBAAoBA,QAAO,KAAK,OAAO,EAAE;AACrD,mBAAiB,GAAGA,QAAO,IAAI,OAAO,EAAE,IAAI;AAC9C;AAQA,IAAM,kBAAkB,IAAI,MAAK;AA8BjC,SAAS,gBAAgB,MAAM,SAAS,EAAE,IAAG,GAAI;AAC/C,MAAI,SAAS;AACb,QAAM,aAAa,KAAK,GAAG;AAE3B,QAAM,OAAO,CAAA;AAEb,QAAM,YAAY,CAAA;AAElB,WAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,KAAK;AACxC,cAAU,IAAI,MAAM,IAAI,WAAW,CAAC;AACpC,SAAK,IAAI,MAAM,IAAI;AACnB,cAAU,iBAAiB,QAAQ,IAAI,CAAC,CAAC;EAC7C;AAGE,OAAK,GAAG,IAAI;AACZ,OAAK,GAAG,EAAE,QAAQ;AAClB,OAAK,GAAG,EAAE,SAAS;AACrB;AAKA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,CAAC,MAAM,QAAQ,KAAK,KAAK,EAAG;AAEhC,MAAI,KAAK,QAAQ,KAAK,gBAAgB,KAAK,aAAa;AACtD,UAAM,oEAAoE;AAC1E,UAAM;EACV;AAEE,MAAI,OAAO,KAAK,eAAe,YAAY,KAAK,eAAe,MAAM;AACnE,UAAM,2BAA2B;AACjC,UAAM;EACV;AAEE,kBAAgB,MAAM,KAAK,OAAO,EAAE,KAAK,aAAY,CAAE;AACvD,OAAK,QAAQ,uBAAuB,KAAK,OAAO,EAAE,UAAU,GAAE,CAAE;AAClE;AAKA,SAAS,cAAc,MAAM;AAC3B,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,EAAG;AAE9B,MAAI,KAAK,QAAQ,KAAK,cAAc,KAAK,WAAW;AAClD,UAAM,8DAA8D;AACpE,UAAM;EACV;AAEE,MAAI,OAAO,KAAK,aAAa,YAAY,KAAK,aAAa,MAAM;AAC/D,UAAM,yBAAyB;AAC/B,UAAM;EACV;AAEE,kBAAgB,MAAM,KAAK,KAAK,EAAE,KAAK,WAAU,CAAE;AACnD,OAAK,MAAM,uBAAuB,KAAK,KAAK,EAAE,UAAU,GAAE,CAAE;AAC9D;AAaA,SAAS,WAAW,MAAM;AACxB,MAAI,KAAK,SAAS,OAAO,KAAK,UAAU,YAAY,KAAK,UAAU,MAAM;AACvE,SAAK,aAAa,KAAK;AACvB,WAAO,KAAK;EAChB;AACA;AAKA,SAAS,WAAW,MAAM;AACxB,aAAW,IAAI;AAEf,MAAI,OAAO,KAAK,eAAe,UAAU;AACvC,SAAK,aAAa,EAAE,OAAO,KAAK,WAAU;EAC9C;AACE,MAAI,OAAO,KAAK,aAAa,UAAU;AACrC,SAAK,WAAW,EAAE,OAAO,KAAK,SAAQ;EAC1C;AAEE,kBAAgB,IAAI;AACpB,gBAAc,IAAI;AACpB;AAoBA,SAAS,gBAAgB,UAAU;AAOjC,WAAS,OAAO,OAAO,QAAQ;AAC7B,WAAO,IAAI;MACT,OAAO,KAAK;MACZ,OACG,SAAS,mBAAmB,MAAM,OAClC,SAAS,eAAe,MAAM,OAC9B,SAAS,MAAM;IACxB;EACA;EAeE,MAAM,WAAW;IACf,cAAc;AACZ,WAAK,eAAe,CAAA;AAEpB,WAAK,UAAU,CAAA;AACf,WAAK,UAAU;AACf,WAAK,WAAW;IACtB;;IAGI,QAAQ,IAAI,MAAM;AAChB,WAAK,WAAW,KAAK;AAErB,WAAK,aAAa,KAAK,OAAO,IAAI;AAClC,WAAK,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;AAC5B,WAAK,WAAW,iBAAiB,EAAE,IAAI;IAC7C;IAEI,UAAU;AACR,UAAI,KAAK,QAAQ,WAAW,GAAG;AAG7B,aAAK,OAAO,MAAM;MAC1B;AACM,YAAM,cAAc,KAAK,QAAQ,IAAI,QAAM,GAAG,CAAC,CAAC;AAChD,WAAK,YAAY,OAAO,uBAAuB,aAAa,EAAE,UAAU,IAAG,CAAE,GAAG,IAAI;AACpF,WAAK,YAAY;IACvB;;IAGI,KAAK,GAAG;AACN,WAAK,UAAU,YAAY,KAAK;AAChC,YAAM,QAAQ,KAAK,UAAU,KAAK,CAAC;AACnC,UAAI,CAAC,OAAO;AAAE,eAAO;MAAK;AAG1B,YAAM,IAAI,MAAM,UAAU,CAAC,IAAIC,OAAMA,KAAI,KAAK,OAAO,MAAS;AAE9D,YAAM,YAAY,KAAK,aAAa,CAAC;AAGrC,YAAM,OAAO,GAAG,CAAC;AAEjB,aAAO,OAAO,OAAO,OAAO,SAAS;IAC3C;EACA;EAiCE,MAAM,oBAAoB;IACxB,cAAc;AAEZ,WAAK,QAAQ,CAAA;AAEb,WAAK,eAAe,CAAA;AACpB,WAAK,QAAQ;AAEb,WAAK,YAAY;AACjB,WAAK,aAAa;IACxB;;IAGI,WAAW,OAAO;AAChB,UAAI,KAAK,aAAa,KAAK,EAAG,QAAO,KAAK,aAAa,KAAK;AAE5D,YAAM,UAAU,IAAI,WAAU;AAC9B,WAAK,MAAM,MAAM,KAAK,EAAE,QAAQ,CAAC,CAAC,IAAI,IAAI,MAAM,QAAQ,QAAQ,IAAI,IAAI,CAAC;AACzE,cAAQ,QAAO;AACf,WAAK,aAAa,KAAK,IAAI;AAC3B,aAAO;IACb;IAEI,6BAA6B;AAC3B,aAAO,KAAK,eAAe;IACjC;IAEI,cAAc;AACZ,WAAK,aAAa;IACxB;;IAGI,QAAQ,IAAI,MAAM;AAChB,WAAK,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC;AAC1B,UAAI,KAAK,SAAS,QAAS,MAAK;IACtC;;IAGI,KAAK,GAAG;AACN,YAAM,IAAI,KAAK,WAAW,KAAK,UAAU;AACzC,QAAE,YAAY,KAAK;AACnB,UAAI,SAAS,EAAE,KAAK,CAAC;AAiCrB,UAAI,KAAK,2BAA0B,GAAI;AACrC,YAAI,UAAU,OAAO,UAAU,KAAK,UAAW;aAAO;AACpD,gBAAM,KAAK,KAAK,WAAW,CAAC;AAC5B,aAAG,YAAY,KAAK,YAAY;AAChC,mBAAS,GAAG,KAAK,CAAC;QAC5B;MACA;AAEM,UAAI,QAAQ;AACV,aAAK,cAAc,OAAO,WAAW;AACrC,YAAI,KAAK,eAAe,KAAK,OAAO;AAElC,eAAK,YAAW;QAC1B;MACA;AAEM,aAAO;IACb;EACA;AASE,WAAS,eAAe,MAAM;AAC5B,UAAM,KAAK,IAAI,oBAAmB;AAElC,SAAK,SAAS,QAAQ,UAAQ,GAAG,QAAQ,KAAK,OAAO,EAAE,MAAM,MAAM,MAAM,QAAO,CAAE,CAAC;AAEnF,QAAI,KAAK,eAAe;AACtB,SAAG,QAAQ,KAAK,eAAe,EAAE,MAAM,MAAK,CAAE;IACpD;AACI,QAAI,KAAK,SAAS;AAChB,SAAG,QAAQ,KAAK,SAAS,EAAE,MAAM,UAAS,CAAE;IAClD;AAEI,WAAO;EACX;AAyCE,WAAS,YAAY,MAAM,QAAQ;AACjC,UAAM;;MAAmC;;AACzC,QAAI,KAAK,WAAY,QAAO;AAE5B;MACE;;;MAGA;MACA;MACA;IACN,EAAM,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAElC,aAAS,mBAAmB,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAG5D,SAAK,gBAAgB;AAErB;MACE;;;MAGA;;MAEA;IACN,EAAM,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAElC,SAAK,aAAa;AAElB,QAAI,iBAAiB;AACrB,QAAI,OAAO,KAAK,aAAa,YAAY,KAAK,SAAS,UAAU;AAI/D,WAAK,WAAW,OAAO,OAAO,CAAA,GAAI,KAAK,QAAQ;AAC/C,uBAAiB,KAAK,SAAS;AAC/B,aAAO,KAAK,SAAS;IAC3B;AACI,qBAAiB,kBAAkB;AAEnC,QAAI,KAAK,UAAU;AACjB,WAAK,WAAW,gBAAgB,KAAK,UAAU,SAAS,gBAAgB;IAC9E;AAEI,UAAM,mBAAmB,OAAO,gBAAgB,IAAI;AAEpD,QAAI,QAAQ;AACV,UAAI,CAAC,KAAK,MAAO,MAAK,QAAQ;AAC9B,YAAM,UAAU,OAAO,MAAM,KAAK;AAClC,UAAI,CAAC,KAAK,OAAO,CAAC,KAAK,eAAgB,MAAK,MAAM;AAClD,UAAI,KAAK,IAAK,OAAM,QAAQ,OAAO,MAAM,GAAG;AAC5C,YAAM,gBAAgB,OAAO,MAAM,GAAG,KAAK;AAC3C,UAAI,KAAK,kBAAkB,OAAO,eAAe;AAC/C,cAAM,kBAAkB,KAAK,MAAM,MAAM,MAAM,OAAO;MAC9D;IACA;AACI,QAAI,KAAK,QAAS,OAAM,YAAY;;MAAuC,KAAK;IAAO;AACvF,QAAI,CAAC,KAAK,SAAU,MAAK,WAAW,CAAA;AAEpC,SAAK,WAAW,CAAA,EAAG,OAAO,GAAG,KAAK,SAAS,IAAI,SAAS,GAAG;AACzD,aAAO,kBAAkB,MAAM,SAAS,OAAO,CAAC;IACtD,CAAK,CAAC;AACF,SAAK,SAAS,QAAQ,SAAS,GAAG;AAAE;;QAA+B;QAAI;MAAK;IAAE,CAAE;AAEhF,QAAI,KAAK,QAAQ;AACf,kBAAY,KAAK,QAAQ,MAAM;IACrC;AAEI,UAAM,UAAU,eAAe,KAAK;AACpC,WAAO;EACX;AAEE,MAAI,CAAC,SAAS,mBAAoB,UAAS,qBAAqB,CAAA;AAGhE,MAAI,SAAS,YAAY,SAAS,SAAS,SAAS,MAAM,GAAG;AAC3D,UAAM,IAAI,MAAM,2FAA2F;EAC/G;AAGE,WAAS,mBAAmB,UAAU,SAAS,oBAAoB,CAAA,CAAE;AAErE,SAAO;;IAA+B;EAAQ;AAChD;AAaA,SAAS,mBAAmB,MAAM;AAChC,MAAI,CAAC,KAAM,QAAO;AAElB,SAAO,KAAK,kBAAkB,mBAAmB,KAAK,MAAM;AAC9D;AAYA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,KAAK,YAAY,CAAC,KAAK,gBAAgB;AACzC,SAAK,iBAAiB,KAAK,SAAS,IAAI,SAAS,SAAS;AACxD,aAAO,UAAU,MAAM,EAAE,UAAU,KAAI,GAAI,OAAO;IACxD,CAAK;EACL;AAKE,MAAI,KAAK,gBAAgB;AACvB,WAAO,KAAK;EAChB;AAME,MAAI,mBAAmB,IAAI,GAAG;AAC5B,WAAO,UAAU,MAAM,EAAE,QAAQ,KAAK,SAAS,UAAU,KAAK,MAAM,IAAI,KAAI,CAAE;EAClF;AAEE,MAAI,OAAO,SAAS,IAAI,GAAG;AACzB,WAAO,UAAU,IAAI;EACzB;AAGE,SAAO;AACT;AAEA,IAAI,UAAU;AAEd,IAAM,qBAAN,cAAiC,MAAM;EACrC,YAAY,QAAQ,MAAM;AACxB,UAAM,MAAM;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;EAChB;AACA;AA+BA,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,WAAW,OAAO,SAAS;AACjC,IAAM,mBAAmB;AAMzB,IAAM,OAAO,SAAS,MAAM;AAG1B,QAAM,YAAY,uBAAO,OAAO,IAAI;AAEpC,QAAM,UAAU,uBAAO,OAAO,IAAI;AAElC,QAAM,UAAU,CAAA;AAIhB,MAAI,YAAY;AAChB,QAAM,qBAAqB;AAE3B,QAAM,qBAAqB,EAAE,mBAAmB,MAAM,MAAM,cAAc,UAAU,CAAA,EAAE;AAKtF,MAAI,UAAU;IACZ,qBAAqB;IACrB,oBAAoB;IACpB,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,aAAa;IACb,WAAW;;;IAGX,WAAW;EACf;AAQE,WAAS,mBAAmB,cAAc;AACxC,WAAO,QAAQ,cAAc,KAAK,YAAY;EAClD;AAKE,WAAS,cAAc,OAAO;AAC5B,QAAI,UAAU,MAAM,YAAY;AAEhC,eAAW,MAAM,aAAa,MAAM,WAAW,YAAY;AAG3D,UAAM,QAAQ,QAAQ,iBAAiB,KAAK,OAAO;AACnD,QAAI,OAAO;AACT,YAAM,WAAW,YAAY,MAAM,CAAC,CAAC;AACrC,UAAI,CAAC,UAAU;AACb,aAAK,mBAAmB,QAAQ,MAAM,MAAM,CAAC,CAAC,CAAC;AAC/C,aAAK,qDAAqD,KAAK;MACvE;AACM,aAAO,WAAW,MAAM,CAAC,IAAI;IACnC;AAEI,WAAO,QACJ,MAAM,KAAK,EACX,KAAK,CAAC,WAAW,mBAAmB,MAAM,KAAK,YAAY,MAAM,CAAC;EACzE;AAuBE,WAASC,WAAU,oBAAoB,eAAe,gBAAgB;AACpE,QAAI,OAAO;AACX,QAAI,eAAe;AACnB,QAAI,OAAO,kBAAkB,UAAU;AACrC,aAAO;AACP,uBAAiB,cAAc;AAC/B,qBAAe,cAAc;IACnC,OAAW;AAEL,iBAAW,UAAU,qDAAqD;AAC1E,iBAAW,UAAU,uGAAuG;AAC5H,qBAAe;AACf,aAAO;IACb;AAII,QAAI,mBAAmB,QAAW;AAAE,uBAAiB;IAAK;AAG1D,UAAM,UAAU;MACd;MACA,UAAU;IAChB;AAGI,SAAK,oBAAoB,OAAO;AAIhC,UAAM,SAAS,QAAQ,SACnB,QAAQ,SACR,WAAW,QAAQ,UAAU,QAAQ,MAAM,cAAc;AAE7D,WAAO,OAAO,QAAQ;AAEtB,SAAK,mBAAmB,MAAM;AAE9B,WAAO;EACX;AAWE,WAAS,WAAW,cAAc,iBAAiB,gBAAgB,cAAc;AAC/E,UAAM,cAAc,uBAAO,OAAO,IAAI;AAQtC,aAAS,YAAY,MAAM,WAAW;AACpC,aAAO,KAAK,SAAS,SAAS;IACpC;AAEI,aAAS,kBAAkB;AACzB,UAAI,CAAC,IAAI,UAAU;AACjB,gBAAQ,QAAQ,UAAU;AAC1B;MACR;AAEM,UAAI,YAAY;AAChB,UAAI,iBAAiB,YAAY;AACjC,UAAI,QAAQ,IAAI,iBAAiB,KAAK,UAAU;AAChD,UAAI,MAAM;AAEV,aAAO,OAAO;AACZ,eAAO,WAAW,UAAU,WAAW,MAAM,KAAK;AAClD,cAAM,OAAO,SAAS,mBAAmB,MAAM,CAAC,EAAE,YAAW,IAAK,MAAM,CAAC;AACzE,cAAM,OAAO,YAAY,KAAK,IAAI;AAClC,YAAI,MAAM;AACR,gBAAM,CAAC,MAAM,gBAAgB,IAAI;AACjC,kBAAQ,QAAQ,GAAG;AACnB,gBAAM;AAEN,sBAAY,IAAI,KAAK,YAAY,IAAI,KAAK,KAAK;AAC/C,cAAI,YAAY,IAAI,KAAK,iBAAkB,cAAa;AACxD,cAAI,KAAK,WAAW,GAAG,GAAG;AAGxB,mBAAO,MAAM,CAAC;UAC1B,OAAiB;AACL,kBAAM,WAAW,SAAS,iBAAiB,IAAI,KAAK;AACpD,wBAAY,MAAM,CAAC,GAAG,QAAQ;UAC1C;QACA,OAAe;AACL,iBAAO,MAAM,CAAC;QACxB;AACQ,oBAAY,IAAI,iBAAiB;AACjC,gBAAQ,IAAI,iBAAiB,KAAK,UAAU;MACpD;AACM,aAAO,WAAW,UAAU,SAAS;AACrC,cAAQ,QAAQ,GAAG;IACzB;AAEI,aAAS,qBAAqB;AAC5B,UAAI,eAAe,GAAI;AAEvB,UAAIC,UAAS;AAEb,UAAI,OAAO,IAAI,gBAAgB,UAAU;AACvC,YAAI,CAAC,UAAU,IAAI,WAAW,GAAG;AAC/B,kBAAQ,QAAQ,UAAU;AAC1B;QACV;AACQ,QAAAA,UAAS,WAAW,IAAI,aAAa,YAAY,MAAM,cAAc,IAAI,WAAW,CAAC;AACrF,sBAAc,IAAI,WAAW;QAAiCA,QAAO;MAC7E,OAAa;AACL,QAAAA,UAAS,cAAc,YAAY,IAAI,YAAY,SAAS,IAAI,cAAc,IAAI;MAC1F;AAMM,UAAI,IAAI,YAAY,GAAG;AACrB,qBAAaA,QAAO;MAC5B;AACM,cAAQ,iBAAiBA,QAAO,UAAUA,QAAO,QAAQ;IAC/D;AAEI,aAAS,gBAAgB;AACvB,UAAI,IAAI,eAAe,MAAM;AAC3B,2BAAkB;MAC1B,OAAa;AACL,wBAAe;MACvB;AACM,mBAAa;IACnB;AAMI,aAAS,YAAY,SAAS,OAAO;AACnC,UAAI,YAAY,GAAI;AAEpB,cAAQ,WAAW,KAAK;AACxB,cAAQ,QAAQ,OAAO;AACvB,cAAQ,SAAQ;IACtB;AAMI,aAAS,eAAe,OAAO,OAAO;AACpC,UAAI,IAAI;AACR,YAAM,MAAM,MAAM,SAAS;AAC3B,aAAO,KAAK,KAAK;AACf,YAAI,CAAC,MAAM,MAAM,CAAC,GAAG;AAAE;AAAK;QAAS;AACrC,cAAM,QAAQ,SAAS,iBAAiB,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;AAC5D,cAAM,OAAO,MAAM,CAAC;AACpB,YAAI,OAAO;AACT,sBAAY,MAAM,KAAK;QACjC,OAAe;AACL,uBAAa;AACb,0BAAe;AACf,uBAAa;QACvB;AACQ;MACR;IACA;AAMI,aAAS,aAAa,MAAM,OAAO;AACjC,UAAI,KAAK,SAAS,OAAO,KAAK,UAAU,UAAU;AAChD,gBAAQ,SAAS,SAAS,iBAAiB,KAAK,KAAK,KAAK,KAAK,KAAK;MAC5E;AACM,UAAI,KAAK,YAAY;AAEnB,YAAI,KAAK,WAAW,OAAO;AACzB,sBAAY,YAAY,SAAS,iBAAiB,KAAK,WAAW,KAAK,KAAK,KAAK,WAAW,KAAK;AACjG,uBAAa;QACvB,WAAmB,KAAK,WAAW,QAAQ;AAEjC,yBAAe,KAAK,YAAY,KAAK;AACrC,uBAAa;QACvB;MACA;AAEM,YAAM,OAAO,OAAO,MAAM,EAAE,QAAQ,EAAE,OAAO,IAAG,EAAE,CAAE;AACpD,aAAO;IACb;AAQI,aAAS,UAAU,MAAM,OAAO,oBAAoB;AAClD,UAAI,UAAU,WAAW,KAAK,OAAO,kBAAkB;AAEvD,UAAI,SAAS;AACX,YAAI,KAAK,QAAQ,GAAG;AAClB,gBAAM,OAAO,IAAI,SAAS,IAAI;AAC9B,eAAK,QAAQ,EAAE,OAAO,IAAI;AAC1B,cAAI,KAAK,eAAgB,WAAU;QAC7C;AAEQ,YAAI,SAAS;AACX,iBAAO,KAAK,cAAc,KAAK,QAAQ;AACrC,mBAAO,KAAK;UACxB;AACU,iBAAO;QACjB;MACA;AAGM,UAAI,KAAK,gBAAgB;AACvB,eAAO,UAAU,KAAK,QAAQ,OAAO,kBAAkB;MAC/D;IACA;AAOI,aAAS,SAAS,QAAQ;AACxB,UAAI,IAAI,QAAQ,eAAe,GAAG;AAGhC,sBAAc,OAAO,CAAC;AACtB,eAAO;MACf,OAAa;AAGL,mCAA2B;AAC3B,eAAO;MACf;IACA;AAQI,aAAS,aAAa,OAAO;AAC3B,YAAM,SAAS,MAAM,CAAC;AACtB,YAAM,UAAU,MAAM;AAEtB,YAAM,OAAO,IAAI,SAAS,OAAO;AAEjC,YAAM,kBAAkB,CAAC,QAAQ,eAAe,QAAQ,UAAU,CAAC;AACnE,iBAAW,MAAM,iBAAiB;AAChC,YAAI,CAAC,GAAI;AACT,WAAG,OAAO,IAAI;AACd,YAAI,KAAK,eAAgB,QAAO,SAAS,MAAM;MACvD;AAEM,UAAI,QAAQ,MAAM;AAChB,sBAAc;MACtB,OAAa;AACL,YAAI,QAAQ,cAAc;AACxB,wBAAc;QACxB;AACQ,sBAAa;AACb,YAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,cAAc;AACjD,uBAAa;QACvB;MACA;AACM,mBAAa,SAAS,KAAK;AAC3B,aAAO,QAAQ,cAAc,IAAI,OAAO;IAC9C;AAOI,aAAS,WAAW,OAAO;AACzB,YAAM,SAAS,MAAM,CAAC;AACtB,YAAM,qBAAqB,gBAAgB,UAAU,MAAM,KAAK;AAEhE,YAAM,UAAU,UAAU,KAAK,OAAO,kBAAkB;AACxD,UAAI,CAAC,SAAS;AAAE,eAAO;MAAS;AAEhC,YAAM,SAAS;AACf,UAAI,IAAI,YAAY,IAAI,SAAS,OAAO;AACtC,sBAAa;AACb,oBAAY,QAAQ,IAAI,SAAS,KAAK;MAC9C,WAAiB,IAAI,YAAY,IAAI,SAAS,QAAQ;AAC9C,sBAAa;AACb,uBAAe,IAAI,UAAU,KAAK;MAC1C,WAAiB,OAAO,MAAM;AACtB,sBAAc;MACtB,OAAa;AACL,YAAI,EAAE,OAAO,aAAa,OAAO,aAAa;AAC5C,wBAAc;QACxB;AACQ,sBAAa;AACb,YAAI,OAAO,YAAY;AACrB,uBAAa;QACvB;MACA;AACM,SAAG;AACD,YAAI,IAAI,OAAO;AACb,kBAAQ,UAAS;QAC3B;AACQ,YAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,aAAa;AACjC,uBAAa,IAAI;QAC3B;AACQ,cAAM,IAAI;MAClB,SAAe,QAAQ,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AAClB,qBAAa,QAAQ,QAAQ,KAAK;MAC1C;AACM,aAAO,OAAO,YAAY,IAAI,OAAO;IAC3C;AAEI,aAAS,uBAAuB;AAC9B,YAAM,OAAO,CAAA;AACb,eAAS,UAAU,KAAK,YAAY,UAAU,UAAU,QAAQ,QAAQ;AACtE,YAAI,QAAQ,OAAO;AACjB,eAAK,QAAQ,QAAQ,KAAK;QACpC;MACA;AACM,WAAK,QAAQ,UAAQ,QAAQ,SAAS,IAAI,CAAC;IACjD;AAGI,QAAI,YAAY,CAAA;AAQhB,aAAS,cAAc,iBAAiB,OAAO;AAC7C,YAAM,SAAS,SAAS,MAAM,CAAC;AAG/B,oBAAc;AAEd,UAAI,UAAU,MAAM;AAClB,sBAAa;AACb,eAAO;MACf;AAMM,UAAI,UAAU,SAAS,WAAW,MAAM,SAAS,SAAS,UAAU,UAAU,MAAM,SAAS,WAAW,IAAI;AAE1G,sBAAc,gBAAgB,MAAM,MAAM,OAAO,MAAM,QAAQ,CAAC;AAChE,YAAI,CAAC,WAAW;AAEd,gBAAM,MAAM,IAAI,MAAM,wBAAwB,YAAY,GAAG;AAC7D,cAAI,eAAe;AACnB,cAAI,UAAU,UAAU;AACxB,gBAAM;QAChB;AACQ,eAAO;MACf;AACM,kBAAY;AAEZ,UAAI,MAAM,SAAS,SAAS;AAC1B,eAAO,aAAa,KAAK;MACjC,WAAiB,MAAM,SAAS,aAAa,CAAC,gBAAgB;AAGtD,cAAM,MAAM,IAAI,MAAM,qBAAqB,SAAS,kBAAkB,IAAI,SAAS,eAAe,GAAG;AACrG,YAAI,OAAO;AACX,cAAM;MACd,WAAiB,MAAM,SAAS,OAAO;AAC/B,cAAM,YAAY,WAAW,KAAK;AAClC,YAAI,cAAc,UAAU;AAC1B,iBAAO;QACjB;MACA;AAKM,UAAI,MAAM,SAAS,aAAa,WAAW,IAAI;AAE7C,eAAO;MACf;AAMM,UAAI,aAAa,OAAU,aAAa,MAAM,QAAQ,GAAG;AACvD,cAAM,MAAM,IAAI,MAAM,2DAA2D;AACjF,cAAM;MACd;AAUM,oBAAc;AACd,aAAO,OAAO;IACpB;AAEI,UAAM,WAAW,YAAY,YAAY;AACzC,QAAI,CAAC,UAAU;AACb,YAAM,mBAAmB,QAAQ,MAAM,YAAY,CAAC;AACpD,YAAM,IAAI,MAAM,wBAAwB,eAAe,GAAG;IAChE;AAEI,UAAM,KAAK,gBAAgB,QAAQ;AACnC,QAAI,SAAS;AAEb,QAAI,MAAM,gBAAgB;AAE1B,UAAM,gBAAgB,CAAA;AACtB,UAAM,UAAU,IAAI,QAAQ,UAAU,OAAO;AAC7C,yBAAoB;AACpB,QAAI,aAAa;AACjB,QAAI,YAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,aAAa;AACjB,QAAI,2BAA2B;AAE/B,QAAI;AACF,UAAI,CAAC,SAAS,cAAc;AAC1B,YAAI,QAAQ,YAAW;AAEvB,mBAAS;AACP;AACA,cAAI,0BAA0B;AAG5B,uCAA2B;UACvC,OAAiB;AACL,gBAAI,QAAQ,YAAW;UACnC;AACU,cAAI,QAAQ,YAAY;AAExB,gBAAM,QAAQ,IAAI,QAAQ,KAAK,eAAe;AAG9C,cAAI,CAAC,MAAO;AAEZ,gBAAM,cAAc,gBAAgB,UAAU,OAAO,MAAM,KAAK;AAChE,gBAAM,iBAAiB,cAAc,aAAa,KAAK;AACvD,kBAAQ,MAAM,QAAQ;QAChC;AACQ,sBAAc,gBAAgB,UAAU,KAAK,CAAC;MACtD,OAAa;AACL,iBAAS,aAAa,iBAAiB,OAAO;MACtD;AAEM,cAAQ,SAAQ;AAChB,eAAS,QAAQ,OAAM;AAEvB,aAAO;QACL,UAAU;QACV,OAAO;QACP;QACA,SAAS;QACT,UAAU;QACV,MAAM;MACd;IACA,SAAa,KAAK;AACZ,UAAI,IAAI,WAAW,IAAI,QAAQ,SAAS,SAAS,GAAG;AAClD,eAAO;UACL,UAAU;UACV,OAAO,OAAO,eAAe;UAC7B,SAAS;UACT,WAAW;UACX,YAAY;YACV,SAAS,IAAI;YACb;YACA,SAAS,gBAAgB,MAAM,QAAQ,KAAK,QAAQ,GAAG;YACvD,MAAM,IAAI;YACV,aAAa;UACzB;UACU,UAAU;QACpB;MACA,WAAiB,WAAW;AACpB,eAAO;UACL,UAAU;UACV,OAAO,OAAO,eAAe;UAC7B,SAAS;UACT,WAAW;UACX,aAAa;UACb,UAAU;UACV,MAAM;QAChB;MACA,OAAa;AACL,cAAM;MACd;IACA;EACA;AASE,WAAS,wBAAwB,MAAM;AACrC,UAAM,SAAS;MACb,OAAO,OAAO,IAAI;MAClB,SAAS;MACT,WAAW;MACX,MAAM;MACN,UAAU,IAAI,QAAQ,UAAU,OAAO;IAC7C;AACI,WAAO,SAAS,QAAQ,IAAI;AAC5B,WAAO;EACX;AAgBE,WAAS,cAAc,MAAM,gBAAgB;AAC3C,qBAAiB,kBAAkB,QAAQ,aAAa,OAAO,KAAK,SAAS;AAC7E,UAAM,YAAY,wBAAwB,IAAI;AAE9C,UAAM,UAAU,eAAe,OAAO,WAAW,EAAE,OAAO,aAAa,EAAE;MAAI,UAC3E,WAAW,MAAM,MAAM,KAAK;IAClC;AACI,YAAQ,QAAQ,SAAS;AAEzB,UAAM,SAAS,QAAQ,KAAK,CAAC,GAAG,MAAM;AAEpC,UAAI,EAAE,cAAc,EAAE,UAAW,QAAO,EAAE,YAAY,EAAE;AAIxD,UAAI,EAAE,YAAY,EAAE,UAAU;AAC5B,YAAI,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU;AACrD,iBAAO;QACjB,WAAmB,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU;AAC5D,iBAAO;QACjB;MACA;AAMM,aAAO;IACb,CAAK;AAED,UAAM,CAAC,MAAM,UAAU,IAAI;AAG3B,UAAM,SAAS;AACf,WAAO,aAAa;AAEpB,WAAO;EACX;AASE,WAAS,gBAAgB,SAAS,aAAa,YAAY;AACzD,UAAM,WAAY,eAAe,QAAQ,WAAW,KAAM;AAE1D,YAAQ,UAAU,IAAI,MAAM;AAC5B,YAAQ,UAAU,IAAI,YAAY,QAAQ,EAAE;EAChD;AAOE,WAAS,iBAAiB,SAAS;AAEjC,QAAI,OAAO;AACX,UAAM,WAAW,cAAc,OAAO;AAEtC,QAAI,mBAAmB,QAAQ,EAAG;AAElC;MAAK;MACH,EAAE,IAAI,SAAS,SAAQ;IAAE;AAE3B,QAAI,QAAQ,QAAQ,aAAa;AAC/B,cAAQ,IAAI,0FAA0F,OAAO;AAC7G;IACN;AAOI,QAAI,QAAQ,SAAS,SAAS,GAAG;AAC/B,UAAI,CAAC,QAAQ,qBAAqB;AAChC,gBAAQ,KAAK,+FAA+F;AAC5G,gBAAQ,KAAK,2DAA2D;AACxE,gBAAQ,KAAK,kCAAkC;AAC/C,gBAAQ,KAAK,OAAO;MAC5B;AACM,UAAI,QAAQ,oBAAoB;AAC9B,cAAM,MAAM,IAAI;UACd;UACA,QAAQ;QAClB;AACQ,cAAM;MACd;IACA;AAEI,WAAO;AACP,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,WAAWD,WAAU,MAAM,EAAE,UAAU,gBAAgB,KAAI,CAAE,IAAI,cAAc,IAAI;AAElG,YAAQ,YAAY,OAAO;AAC3B,YAAQ,QAAQ,cAAc;AAC9B,oBAAgB,SAAS,UAAU,OAAO,QAAQ;AAClD,YAAQ,SAAS;MACf,UAAU,OAAO;;MAEjB,IAAI,OAAO;MACX,WAAW,OAAO;IACxB;AACI,QAAI,OAAO,YAAY;AACrB,cAAQ,aAAa;QACnB,UAAU,OAAO,WAAW;QAC5B,WAAW,OAAO,WAAW;MACrC;IACA;AAEI,SAAK,0BAA0B,EAAE,IAAI,SAAS,QAAQ,KAAI,CAAE;EAChE;AAOE,WAAS,UAAU,aAAa;AAC9B,cAAU,QAAQ,SAAS,WAAW;EAC1C;AAGE,QAAM,mBAAmB,MAAM;AAC7B,iBAAY;AACZ,eAAW,UAAU,yDAAyD;EAClF;AAGE,WAAS,yBAAyB;AAChC,iBAAY;AACZ,eAAW,UAAU,+DAA+D;EACxF;AAEE,MAAI,iBAAiB;AAKrB,WAAS,eAAe;AAEtB,QAAI,SAAS,eAAe,WAAW;AACrC,uBAAiB;AACjB;IACN;AAEI,UAAM,SAAS,SAAS,iBAAiB,QAAQ,WAAW;AAC5D,WAAO,QAAQ,gBAAgB;EACnC;AAEE,WAAS,OAAO;AAEd,QAAI,eAAgB,cAAY;EACpC;AAGE,MAAI,OAAO,WAAW,eAAe,OAAO,kBAAkB;AAC5D,WAAO,iBAAiB,oBAAoB,MAAM,KAAK;EAC3D;AAQE,WAAS,iBAAiB,cAAc,oBAAoB;AAC1D,QAAI,OAAO;AACX,QAAI;AACF,aAAO,mBAAmB,IAAI;IACpC,SAAa,SAAS;AAChB,YAAM,wDAAwD,QAAQ,MAAM,YAAY,CAAC;AAEzF,UAAI,CAAC,WAAW;AAAE,cAAM;MAAQ,OAAQ;AAAE,cAAM,OAAO;MAAE;AAKzD,aAAO;IACb;AAEI,QAAI,CAAC,KAAK,KAAM,MAAK,OAAO;AAC5B,cAAU,YAAY,IAAI;AAC1B,SAAK,gBAAgB,mBAAmB,KAAK,MAAM,IAAI;AAEvD,QAAI,KAAK,SAAS;AAChB,sBAAgB,KAAK,SAAS,EAAE,aAAY,CAAE;IACpD;EACA;AAOE,WAAS,mBAAmB,cAAc;AACxC,WAAO,UAAU,YAAY;AAC7B,eAAW,SAAS,OAAO,KAAK,OAAO,GAAG;AACxC,UAAI,QAAQ,KAAK,MAAM,cAAc;AACnC,eAAO,QAAQ,KAAK;MAC5B;IACA;EACA;AAKE,WAAS,gBAAgB;AACvB,WAAO,OAAO,KAAK,SAAS;EAChC;AAME,WAAS,YAAY,MAAM;AACzB,YAAQ,QAAQ,IAAI,YAAW;AAC/B,WAAO,UAAU,IAAI,KAAK,UAAU,QAAQ,IAAI,CAAC;EACrD;AAOE,WAAS,gBAAgB,WAAW,EAAE,aAAY,GAAI;AACpD,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,CAAC,SAAS;IAC5B;AACI,cAAU,QAAQ,WAAS;AAAE,cAAQ,MAAM,YAAW,CAAE,IAAI;IAAa,CAAE;EAC/E;AAME,WAAS,cAAc,MAAM;AAC3B,UAAM,OAAO,YAAY,IAAI;AAC7B,WAAO,QAAQ,CAAC,KAAK;EACzB;AAOE,WAAS,iBAAiB,QAAQ;AAEhC,QAAI,OAAO,uBAAuB,KAAK,CAAC,OAAO,yBAAyB,GAAG;AACzE,aAAO,yBAAyB,IAAI,CAAC,SAAS;AAC5C,eAAO,uBAAuB;UAC5B,OAAO,OAAO,EAAE,OAAO,KAAK,GAAE,GAAI,IAAI;QAChD;MACA;IACA;AACI,QAAI,OAAO,sBAAsB,KAAK,CAAC,OAAO,wBAAwB,GAAG;AACvE,aAAO,wBAAwB,IAAI,CAAC,SAAS;AAC3C,eAAO,sBAAsB;UAC3B,OAAO,OAAO,EAAE,OAAO,KAAK,GAAE,GAAI,IAAI;QAChD;MACA;IACA;EACA;AAKE,WAAS,UAAU,QAAQ;AACzB,qBAAiB,MAAM;AACvB,YAAQ,KAAK,MAAM;EACvB;AAKE,WAAS,aAAa,QAAQ;AAC5B,UAAM,QAAQ,QAAQ,QAAQ,MAAM;AACpC,QAAI,UAAU,IAAI;AAChB,cAAQ,OAAO,OAAO,CAAC;IAC7B;EACA;AAOE,WAAS,KAAK,OAAO,MAAM;AACzB,UAAM,KAAK;AACX,YAAQ,QAAQ,SAAS,QAAQ;AAC/B,UAAI,OAAO,EAAE,GAAG;AACd,eAAO,EAAE,EAAE,IAAI;MACvB;IACA,CAAK;EACL;AAME,WAAS,wBAAwB,IAAI;AACnC,eAAW,UAAU,kDAAkD;AACvE,eAAW,UAAU,kCAAkC;AAEvD,WAAO,iBAAiB,EAAE;EAC9B;AAGE,SAAO,OAAO,MAAM;IAClB,WAAAA;IACA;IACA;IACA;;IAEA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ,CAAG;AAED,OAAK,YAAY,WAAW;AAAE,gBAAY;EAAM;AAChD,OAAK,WAAW,WAAW;AAAE,gBAAY;EAAK;AAC9C,OAAK,gBAAgB;AAErB,OAAK,QAAQ;IACX;IACA;IACA;IACA;IACA;EACJ;AAEE,aAAW,OAAO,OAAO;AAEvB,QAAI,OAAO,MAAM,GAAG,MAAM,UAAU;AAElC,iBAAW,MAAM,GAAG,CAAC;IAC3B;EACA;AAGE,SAAO,OAAO,MAAM,KAAK;AAEzB,SAAO;AACT;AAGA,IAAM,YAAY,KAAK,CAAA,CAAE;AAIzB,UAAU,cAAc,MAAM,KAAK,CAAA,CAAE;IAErC,OAAiB;AACjB,UAAU,cAAc;AACxB,UAAU,UAAU;;AC7hFpB,SAAS,WAAW,OAAc,YAAsB,CAAA,GAAE;AACxD,SAAO,MACJ,IAAI,UAAO;AACV,UAAM,UAAU,CAAC,GAAG,WAAW,GAAI,KAAK,aAAa,KAAK,WAAW,YAAY,CAAA,CAAG;AAEpF,QAAI,KAAK,UAAU;AACjB,aAAO,WAAW,KAAK,UAAU,OAAO;;AAG1C,WAAO;MACL,MAAM,KAAK;MACX;;EAEJ,CAAC,EACA,KAAI;AACT;AAEA,SAAS,kBAAkB,QAAW;AAEpC,SAAO,OAAO,SAAS,OAAO,YAAY,CAAA;AAC5C;AAEA,SAAS,WAAW,iBAAuB;AACzC,SAAO,QAAQA,YAAU,YAAY,eAAe,CAAC;AACvD;AAEA,SAAS,eAAe,EACtB,KACA,MACA,UACA,gBAAe,GAMhB;AACC,QAAM,cAA4B,CAAA;AAElC,eAAa,KAAK,UAAQ,KAAK,KAAK,SAAS,IAAI,EAAE,QAAQ,WAAQ;;AACjE,QAAI,OAAO,MAAM,MAAM;AACvB,UAAM,WAAW,MAAM,KAAK,MAAM,YAAY;AAC9C,UAAM,YAAY,SAAS,cAAa;AAExC,UAAM,QAAQ,aAAa,UAAU,SAAS,QAAQ,KAAK,WAAW,QAAQ,OAAK,KAAA,SAAS,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,UAAA,QAAQ,MAC7G,kBAAkB,SAAS,UAAU,UAAU,MAAM,KAAK,WAAW,CAAC,IACtE,kBAAkB,SAAS,cAAc,MAAM,KAAK,WAAW,CAAC;AAEpE,eAAW,KAAK,EAAE,QAAQ,UAAO;AAC/B,YAAM,KAAK,OAAO,KAAK,KAAK;AAE5B,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,aAAa,WAAW,OAAO,MAAM,IAAI;UAC7C,OAAO,KAAK,QAAQ,KAAK,GAAG;QAC7B,CAAA;AAED,oBAAY,KAAK,UAAU;;AAG7B,aAAO;IACT,CAAC;EACH,CAAC;AAED,SAAO,cAAc,OAAO,KAAK,WAAW;AAC9C;AAGA,SAAS,WAAW,OAAU;AAC5B,SAAO,OAAO,UAAU;AAC1B;AAEM,SAAU,eAAe,EAC7B,MACA,UACA,gBAAe,GAKhB;AACC,MAAI,CAAC,CAAC,aAAa,iBAAiB,eAAe,EAAE,MAAM,SAAO,WAAW,SAAS,GAAG,CAAC,CAAC,GAAG;AAC5F,UAAM,MACJ,qFAAqF;;AAIzF,QAAM,iBAA8B,IAAI,OAAO;IAC7C,KAAK,IAAI,UAAU,UAAU;IAE7B,OAAO;MACL,MAAM,CAAC,GAAG,EAAE,IAAG,MAAO,eAAe;QACnC;QACA;QACA;QACA;OACD;MACD,OAAO,CAAC,aAAa,eAAe,UAAU,aAAY;AACxD,cAAM,cAAc,SAAS,UAAU,MAAM,OAAO,KAAK;AACzD,cAAM,cAAc,SAAS,UAAU,MAAM,OAAO,KAAK;AACzD,cAAM,WAAW,aAAa,SAAS,KAAK,UAAQ,KAAK,KAAK,SAAS,IAAI;AAC3E,cAAM,WAAW,aAAa,SAAS,KAAK,UAAQ,KAAK,KAAK,SAAS,IAAI;AAE3E,YACE,YAAY,eAGR,CAAC,aAAa,WAAW,EAAE,SAAS,IAAI,KAEvC,SAAS,WAAW,SAAS,UAI7B,YAAY,MAAM,KAAK,UAAO;AAE/B;;YAEE,KAAK,SAAS,UAEX,KAAK,OAAO,UACZ,SAAS,KAAK,UAAO;AAEtB;;gBAEE,KAAK,OAAO,KAAK,QAEd,KAAK,MAAM,KAAK,KAAK,YAAY,KAAK;;aAE5C;;SAEJ,IACH;AACA,iBAAO,eAAe;YACpB,KAAK,YAAY;YACjB;YACA;YACA;UACD,CAAA;;AAGH,eAAO,cAAc,IAAI,YAAY,SAAS,YAAY,GAAG;;IAEhE;IAED,OAAO;MACL,YAAY,OAAK;AACf,eAAO,eAAe,SAAS,KAAK;;IAEvC;EACF,CAAA;AAED,SAAO;AACT;AC/Ia,IAAA,oBAAoB,UAAU,OAAiC;EAC1E,aAAU;;AACR,WAAO;MACL,IAAG,KAAA,KAAK,YAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA;MAClB,UAAU,CAAA;MACV,qBAAqB;MACrB,mBAAmB;MACnB,iBAAiB;MACjB,iBAAiB;MACjB,gBAAgB,CAAA;;;EAIpB,wBAAqB;;AACnB,WAAO;MACL,KAAG,KAAA,KAAK,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA,MAAQ,CAAA;MACtB,eAAe;QACb,MAAM,KAAK;QACX,UAAU,KAAK,QAAQ;QACvB,iBAAiB,KAAK,QAAQ;OAC/B;;;AAGN,CAAA;", "names": ["scopeName", "version", "i", "highlight", "result"]}