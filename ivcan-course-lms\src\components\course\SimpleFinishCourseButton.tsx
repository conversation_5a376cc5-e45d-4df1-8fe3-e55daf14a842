import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Trophy, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { finishCourse } from '@/services/course/completionService';
import CourseCompletionCelebration from './CourseCompletionCelebration';

interface SimpleFinishCourseButtonProps {
  courseId: string;
  userId: string;
  modules: any[];
  courseName?: string;
}

const SimpleFinishCourseButton: React.FC<SimpleFinishCourseButtonProps> = ({
  courseId,
  userId,
  modules,
  courseName = 'this course'
}) => {
  const [isCompleting, setIsCompleting] = useState(false);
  const [allModulesCompleted, setAllModulesCompleted] = useState(false);
  const [isCourseCompleted, setIsCourseCompleted] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Debug: Log when component renders
  console.log('SimpleFinishCourseButton: Component rendered with props:', {
    courseId,
    userId,
    moduleCount: modules?.length || 0,
    courseName
  });

  // Check if all modules are completed
  useEffect(() => {
    const checkModuleCompletion = async () => {
      try {
        if (!modules || modules.length === 0 || !userId) {
          console.log('SimpleFinishCourseButton: Missing data', {
            hasModules: !!modules,
            moduleCount: modules?.length || 0,
            hasUserId: !!userId
          });
          return;
        }

        console.log('SimpleFinishCourseButton: Checking module completion for:', { courseId, userId, moduleCount: modules.length });
        console.log('SimpleFinishCourseButton: Module IDs:', modules.map(m => ({ id: m.id, title: m.title, is_completed: m.is_completed })));

        // First check if modules already have completion status from the API
        const modulesWithCompletion = modules.filter(m => m.is_completed === true);
        console.log('SimpleFinishCourseButton: Modules marked as completed in API:', modulesWithCompletion.length);

        // Also check the database directly for module progress
        const { data: moduleProgress, error: progressError } = await supabase
          .from('user_module_progress')
          .select('module_id, is_completed')
          .eq('user_id', userId)
          .eq('is_completed', true)
          .in('module_id', modules.map(m => m.id));

        if (progressError) {
          console.error('SimpleFinishCourseButton: Error checking module completion:', progressError);
          return;
        }

        const completedCount = moduleProgress?.length || 0;
        const totalModules = modules.length;
        const completed = completedCount === totalModules;

        console.log(`SimpleFinishCourseButton: Module completion status from DB: ${completedCount}/${totalModules} completed`);
        console.log('SimpleFinishCourseButton: Completed module IDs from DB:', moduleProgress?.map(m => m.module_id));
        console.log('SimpleFinishCourseButton: All modules completed?', completed);
        setAllModulesCompleted(completed);

        // Check if the course is already completed
        const { data: enrollment } = await supabase
          .from('user_course_enrollment')
          .select('status')
          .eq('user_id', userId)
          .eq('course_id', courseId)
          .single();

        const courseCompleted = enrollment?.status === 'completed';
        setIsCourseCompleted(courseCompleted);
        
        console.log(`Course enrollment status: ${enrollment?.status}`);
      } catch (error) {
        console.error('Error checking module completion:', error);
      }
    };

    checkModuleCompletion();
  }, [modules, userId, courseId]);

  const handleFinishCourse = async () => {
    if (!courseId || !userId) {
      toast.error('Missing course or user information');
      return;
    }

    if (!allModulesCompleted) {
      toast.error('You need to complete all modules before finishing the course');
      return;
    }

    try {
      setIsCompleting(true);
      
      console.log(`Attempting to finish course ${courseId} for user ${userId}`);
      
      // Use the finishCourse function that properly handles certificate generation
      const result = await finishCourse(courseId, userId);
      
      if (result.success) {
        console.log('Course finished successfully');
        
        // Show celebration
        setShowCelebration(true);
        
        // Update state
        setIsCourseCompleted(true);
        
        // Show success toast
        toast.success(`🎉 Congratulations! You've completed ${courseName}! Your certificate is ready.`);
        
        // Refresh queries to update achievements and certificates
        await queryClient.invalidateQueries({ queryKey: ['courseProgress'] });
        await queryClient.invalidateQueries({ queryKey: ['courseEnrollment'] });
        await queryClient.invalidateQueries({ queryKey: ['certificates'] });
        await queryClient.invalidateQueries({ queryKey: ['user-achievements'] });
      } else {
        console.error('Failed to finish course:', result.error);
        toast.error(result.error || 'Failed to complete course. Please try again.');
      }
    } catch (error) {
      console.error('Error finishing course:', error);
      toast.error('Failed to complete course. Please try again.');
    } finally {
      setIsCompleting(false);
    }
  };

  const handleCloseCelebration = () => {
    setShowCelebration(false);
    navigate(`/certificate/${courseId}`);
  };

  // Don't show button if course is already completed
  if (isCourseCompleted) {
    return (
      <div className="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
        <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
        <span className="text-green-700 dark:text-green-300 font-medium">Course Completed!</span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate(`/certificate/${courseId}`)}
          className="ml-3 border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/30"
        >
          View Certificate
        </Button>
      </div>
    );
  }

  // Always show the button for debugging - we'll handle the state in the main button

  return (
    <>
      <CourseCompletionCelebration
        courseId={courseId}
        courseName={courseName}
        isVisible={showCelebration}
        onClose={handleCloseCelebration}
      />

      <div className="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex items-center space-x-3">
          <Trophy className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          <span className="text-blue-700 dark:text-blue-300 font-medium">
            {allModulesCompleted
              ? 'All modules completed! Ready to finish the course?'
              : `Debug: ${modules.filter(m => m.is_completed).length}/${modules.length} modules completed`
            }
          </span>
          <Button
            onClick={handleFinishCourse}
            disabled={isCompleting || !allModulesCompleted}
            className={allModulesCompleted
              ? "bg-blue-600 hover:bg-blue-700 text-white"
              : "bg-gray-400 text-gray-700 cursor-not-allowed"
            }
          >
            {isCompleting ? (
              <>
                <span className="animate-spin border-2 border-current border-t-transparent rounded-full w-4 h-4 mr-2" />
                Completing...
              </>
            ) : (
              <>
                <Trophy className="h-4 w-4 mr-2" />
                {allModulesCompleted ? 'Finish Course' : 'Complete All Modules First'}
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );
};

export default SimpleFinishCourseButton;
