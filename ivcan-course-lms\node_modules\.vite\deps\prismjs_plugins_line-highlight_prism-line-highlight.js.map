{"version": 3, "sources": ["../../prismjs/plugins/line-highlight/prism-line-highlight.js"], "sourcesContent": ["(function () {\n\n\tif (typeof Prism === 'undefined' || typeof document === 'undefined' || !document.querySelector) {\n\t\treturn;\n\t}\n\n\tvar LINE_NUMBERS_CLASS = 'line-numbers';\n\tvar LINKABLE_LINE_NUMBERS_CLASS = 'linkable-line-numbers';\n\tvar NEW_LINE_EXP = /\\n(?!$)/g;\n\n\t/**\n\t * @param {string} selector\n\t * @param {ParentNode} [container]\n\t * @returns {HTMLElement[]}\n\t */\n\tfunction $$(selector, container) {\n\t\treturn Array.prototype.slice.call((container || document).querySelectorAll(selector));\n\t}\n\n\t/**\n\t * Returns whether the given element has the given class.\n\t *\n\t * @param {Element} element\n\t * @param {string} className\n\t * @returns {boolean}\n\t */\n\tfunction hasClass(element, className) {\n\t\treturn element.classList.contains(className);\n\t}\n\n\t/**\n\t * Calls the given function.\n\t *\n\t * @param {() => any} func\n\t * @returns {void}\n\t */\n\tfunction callFunction(func) {\n\t\tfunc();\n\t}\n\n\t// Some browsers round the line-height, others don't.\n\t// We need to test for it to position the elements properly.\n\tvar isLineHeightRounded = (function () {\n\t\tvar res;\n\t\treturn function () {\n\t\t\tif (typeof res === 'undefined') {\n\t\t\t\tvar d = document.createElement('div');\n\t\t\t\td.style.fontSize = '13px';\n\t\t\t\td.style.lineHeight = '1.5';\n\t\t\t\td.style.padding = '0';\n\t\t\t\td.style.border = '0';\n\t\t\t\td.innerHTML = '&nbsp;<br />&nbsp;';\n\t\t\t\tdocument.body.appendChild(d);\n\t\t\t\t// Browsers that round the line-height should have offsetHeight === 38\n\t\t\t\t// The others should have 39.\n\t\t\t\tres = d.offsetHeight === 38;\n\t\t\t\tdocument.body.removeChild(d);\n\t\t\t}\n\t\t\treturn res;\n\t\t};\n\t}());\n\n\t/**\n\t * Returns the top offset of the content box of the given parent and the content box of one of its children.\n\t *\n\t * @param {HTMLElement} parent\n\t * @param {HTMLElement} child\n\t */\n\tfunction getContentBoxTopOffset(parent, child) {\n\t\tvar parentStyle = getComputedStyle(parent);\n\t\tvar childStyle = getComputedStyle(child);\n\n\t\t/**\n\t\t * Returns the numeric value of the given pixel value.\n\t\t *\n\t\t * @param {string} px\n\t\t */\n\t\tfunction pxToNumber(px) {\n\t\t\treturn +px.substr(0, px.length - 2);\n\t\t}\n\n\t\treturn child.offsetTop\n\t\t\t+ pxToNumber(childStyle.borderTopWidth)\n\t\t\t+ pxToNumber(childStyle.paddingTop)\n\t\t\t- pxToNumber(parentStyle.paddingTop);\n\t}\n\n\t/**\n\t * Returns whether the Line Highlight plugin is active for the given element.\n\t *\n\t * If this function returns `false`, do not call `highlightLines` for the given element.\n\t *\n\t * @param {HTMLElement | null | undefined} pre\n\t * @returns {boolean}\n\t */\n\tfunction isActiveFor(pre) {\n\t\tif (!pre || !/pre/i.test(pre.nodeName)) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (pre.hasAttribute('data-line')) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (pre.id && Prism.util.isActive(pre, LINKABLE_LINE_NUMBERS_CLASS)) {\n\t\t\t// Technically, the line numbers plugin is also necessary but this plugin doesn't control the classes of\n\t\t\t// the line numbers plugin, so we can't assume that they are present.\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tvar scrollIntoView = true;\n\n\tPrism.plugins.lineHighlight = {\n\t\t/**\n\t\t * Highlights the lines of the given pre.\n\t\t *\n\t\t * This function is split into a DOM measuring and mutate phase to improve performance.\n\t\t * The returned function mutates the DOM when called.\n\t\t *\n\t\t * @param {HTMLElement} pre\n\t\t * @param {string | null} [lines]\n\t\t * @param {string} [classes='']\n\t\t * @returns {() => void}\n\t\t */\n\t\thighlightLines: function highlightLines(pre, lines, classes) {\n\t\t\tlines = typeof lines === 'string' ? lines : (pre.getAttribute('data-line') || '');\n\n\t\t\tvar ranges = lines.replace(/\\s+/g, '').split(',').filter(Boolean);\n\t\t\tvar offset = +pre.getAttribute('data-line-offset') || 0;\n\n\t\t\tvar parseMethod = isLineHeightRounded() ? parseInt : parseFloat;\n\t\t\tvar lineHeight = parseMethod(getComputedStyle(pre).lineHeight);\n\t\t\tvar hasLineNumbers = Prism.util.isActive(pre, LINE_NUMBERS_CLASS);\n\t\t\tvar codeElement = pre.querySelector('code');\n\t\t\tvar parentElement = hasLineNumbers ? pre : codeElement || pre;\n\t\t\tvar mutateActions = /** @type {(() => void)[]} */ ([]);\n\t\t\tvar lineBreakMatch = codeElement.textContent.match(NEW_LINE_EXP);\n\t\t\tvar numberOfLines = lineBreakMatch ? lineBreakMatch.length + 1 : 1;\n\t\t\t/**\n\t\t\t * The top offset between the content box of the <code> element and the content box of the parent element of\n\t\t\t * the line highlight element (either `<pre>` or `<code>`).\n\t\t\t *\n\t\t\t * This offset might not be zero for some themes where the <code> element has a top margin. Some plugins\n\t\t\t * (or users) might also add element above the <code> element. Because the line highlight is aligned relative\n\t\t\t * to the <pre> element, we have to take this into account.\n\t\t\t *\n\t\t\t * This offset will be 0 if the parent element of the line highlight element is the `<code>` element.\n\t\t\t */\n\t\t\tvar codePreOffset = !codeElement || parentElement == codeElement ? 0 : getContentBoxTopOffset(pre, codeElement);\n\n\t\t\tranges.forEach(function (currentRange) {\n\t\t\t\tvar range = currentRange.split('-');\n\n\t\t\t\tvar start = +range[0];\n\t\t\t\tvar end = +range[1] || start;\n\t\t\t\tend = Math.min(numberOfLines + offset, end);\n\n\t\t\t\tif (end < start) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t/** @type {HTMLElement} */\n\t\t\t\tvar line = pre.querySelector('.line-highlight[data-range=\"' + currentRange + '\"]') || document.createElement('div');\n\n\t\t\t\tmutateActions.push(function () {\n\t\t\t\t\tline.setAttribute('aria-hidden', 'true');\n\t\t\t\t\tline.setAttribute('data-range', currentRange);\n\t\t\t\t\tline.className = (classes || '') + ' line-highlight';\n\t\t\t\t});\n\n\t\t\t\t// if the line-numbers plugin is enabled, then there is no reason for this plugin to display the line numbers\n\t\t\t\tif (hasLineNumbers && Prism.plugins.lineNumbers) {\n\t\t\t\t\tvar startNode = Prism.plugins.lineNumbers.getLine(pre, start);\n\t\t\t\t\tvar endNode = Prism.plugins.lineNumbers.getLine(pre, end);\n\n\t\t\t\t\tif (startNode) {\n\t\t\t\t\t\tvar top = startNode.offsetTop + codePreOffset + 'px';\n\t\t\t\t\t\tmutateActions.push(function () {\n\t\t\t\t\t\t\tline.style.top = top;\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t\tif (endNode) {\n\t\t\t\t\t\tvar height = (endNode.offsetTop - startNode.offsetTop) + endNode.offsetHeight + 'px';\n\t\t\t\t\t\tmutateActions.push(function () {\n\t\t\t\t\t\t\tline.style.height = height;\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tmutateActions.push(function () {\n\t\t\t\t\t\tline.setAttribute('data-start', String(start));\n\n\t\t\t\t\t\tif (end > start) {\n\t\t\t\t\t\t\tline.setAttribute('data-end', String(end));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tline.style.top = (start - offset - 1) * lineHeight + codePreOffset + 'px';\n\n\t\t\t\t\t\tline.textContent = new Array(end - start + 2).join(' \\n');\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tmutateActions.push(function () {\n\t\t\t\t\tline.style.width = pre.scrollWidth + 'px';\n\t\t\t\t});\n\n\t\t\t\tmutateActions.push(function () {\n\t\t\t\t\t// allow this to play nicely with the line-numbers plugin\n\t\t\t\t\t// need to attack to pre as when line-numbers is enabled, the code tag is relatively which screws up the positioning\n\t\t\t\t\tparentElement.appendChild(line);\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tvar id = pre.id;\n\t\t\tif (hasLineNumbers && Prism.util.isActive(pre, LINKABLE_LINE_NUMBERS_CLASS) && id) {\n\t\t\t\t// This implements linkable line numbers. Linkable line numbers use Line Highlight to create a link to a\n\t\t\t\t// specific line. For this to work, the pre element has to:\n\t\t\t\t//  1) have line numbers,\n\t\t\t\t//  2) have the `linkable-line-numbers` class or an ascendant that has that class, and\n\t\t\t\t//  3) have an id.\n\n\t\t\t\tif (!hasClass(pre, LINKABLE_LINE_NUMBERS_CLASS)) {\n\t\t\t\t\t// add class to pre\n\t\t\t\t\tmutateActions.push(function () {\n\t\t\t\t\t\tpre.classList.add(LINKABLE_LINE_NUMBERS_CLASS);\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tvar start = parseInt(pre.getAttribute('data-start') || '1');\n\n\t\t\t\t// iterate all line number spans\n\t\t\t\t$$('.line-numbers-rows > span', pre).forEach(function (lineSpan, i) {\n\t\t\t\t\tvar lineNumber = i + start;\n\t\t\t\t\tlineSpan.onclick = function () {\n\t\t\t\t\t\tvar hash = id + '.' + lineNumber;\n\n\t\t\t\t\t\t// this will prevent scrolling since the span is obviously in view\n\t\t\t\t\t\tscrollIntoView = false;\n\t\t\t\t\t\tlocation.hash = hash;\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tscrollIntoView = true;\n\t\t\t\t\t\t}, 1);\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn function () {\n\t\t\t\tmutateActions.forEach(callFunction);\n\t\t\t};\n\t\t}\n\t};\n\n\n\tfunction applyHash() {\n\t\tvar hash = location.hash.slice(1);\n\n\t\t// Remove pre-existing temporary lines\n\t\t$$('.temporary.line-highlight').forEach(function (line) {\n\t\t\tline.parentNode.removeChild(line);\n\t\t});\n\n\t\tvar range = (hash.match(/\\.([\\d,-]+)$/) || [, ''])[1];\n\n\t\tif (!range || document.getElementById(hash)) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar id = hash.slice(0, hash.lastIndexOf('.'));\n\t\tvar pre = document.getElementById(id);\n\n\t\tif (!pre) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!pre.hasAttribute('data-line')) {\n\t\t\tpre.setAttribute('data-line', '');\n\t\t}\n\n\t\tvar mutateDom = Prism.plugins.lineHighlight.highlightLines(pre, range, 'temporary ');\n\t\tmutateDom();\n\n\t\tif (scrollIntoView) {\n\t\t\tdocument.querySelector('.temporary.line-highlight').scrollIntoView();\n\t\t}\n\t}\n\n\tvar fakeTimer = 0; // Hack to limit the number of times applyHash() runs\n\n\tPrism.hooks.add('before-sanity-check', function (env) {\n\t\tvar pre = env.element.parentElement;\n\t\tif (!isActiveFor(pre)) {\n\t\t\treturn;\n\t\t}\n\n\t\t/*\n\t\t * Cleanup for other plugins (e.g. autoloader).\n\t\t *\n\t\t * Sometimes <code> blocks are highlighted multiple times. It is necessary\n\t\t * to cleanup any left-over tags, because the whitespace inside of the <div>\n\t\t * tags change the content of the <code> tag.\n\t\t */\n\t\tvar num = 0;\n\t\t$$('.line-highlight', pre).forEach(function (line) {\n\t\t\tnum += line.textContent.length;\n\t\t\tline.parentNode.removeChild(line);\n\t\t});\n\t\t// Remove extra whitespace\n\t\tif (num && /^(?: \\n)+$/.test(env.code.slice(-num))) {\n\t\t\tenv.code = env.code.slice(0, -num);\n\t\t}\n\t});\n\n\tPrism.hooks.add('complete', function completeHook(env) {\n\t\tvar pre = env.element.parentElement;\n\t\tif (!isActiveFor(pre)) {\n\t\t\treturn;\n\t\t}\n\n\t\tclearTimeout(fakeTimer);\n\n\t\tvar hasLineNumbers = Prism.plugins.lineNumbers;\n\t\tvar isLineNumbersLoaded = env.plugins && env.plugins.lineNumbers;\n\n\t\tif (hasClass(pre, LINE_NUMBERS_CLASS) && hasLineNumbers && !isLineNumbersLoaded) {\n\t\t\tPrism.hooks.add('line-numbers', completeHook);\n\t\t} else {\n\t\t\tvar mutateDom = Prism.plugins.lineHighlight.highlightLines(pre);\n\t\t\tmutateDom();\n\t\t\tfakeTimer = setTimeout(applyHash, 1);\n\t\t}\n\t});\n\n\twindow.addEventListener('hashchange', applyHash);\n\twindow.addEventListener('resize', function () {\n\t\tvar actions = $$('pre')\n\t\t\t.filter(isActiveFor)\n\t\t\t.map(function (pre) {\n\t\t\t\treturn Prism.plugins.lineHighlight.highlightLines(pre);\n\t\t\t});\n\t\tactions.forEach(callFunction);\n\t});\n\n}());\n"], "mappings": ";CAAC,WAAY;AAEZ,MAAI,OAAO,UAAU,eAAe,OAAO,aAAa,eAAe,CAAC,SAAS,eAAe;AAC/F;AAAA,EACD;AAEA,MAAI,qBAAqB;AACzB,MAAI,8BAA8B;AAClC,MAAI,eAAe;AAOnB,WAAS,GAAG,UAAU,WAAW;AAChC,WAAO,MAAM,UAAU,MAAM,MAAM,aAAa,UAAU,iBAAiB,QAAQ,CAAC;AAAA,EACrF;AASA,WAAS,SAAS,SAAS,WAAW;AACrC,WAAO,QAAQ,UAAU,SAAS,SAAS;AAAA,EAC5C;AAQA,WAAS,aAAa,MAAM;AAC3B,SAAK;AAAA,EACN;AAIA,MAAI,sBAAuB,2BAAY;AACtC,QAAI;AACJ,WAAO,WAAY;AAClB,UAAI,OAAO,QAAQ,aAAa;AAC/B,YAAI,IAAI,SAAS,cAAc,KAAK;AACpC,UAAE,MAAM,WAAW;AACnB,UAAE,MAAM,aAAa;AACrB,UAAE,MAAM,UAAU;AAClB,UAAE,MAAM,SAAS;AACjB,UAAE,YAAY;AACd,iBAAS,KAAK,YAAY,CAAC;AAG3B,cAAM,EAAE,iBAAiB;AACzB,iBAAS,KAAK,YAAY,CAAC;AAAA,MAC5B;AACA,aAAO;AAAA,IACR;AAAA,EACD,EAAE;AAQF,WAAS,uBAAuB,QAAQ,OAAO;AAC9C,QAAI,cAAc,iBAAiB,MAAM;AACzC,QAAI,aAAa,iBAAiB,KAAK;AAOvC,aAAS,WAAW,IAAI;AACvB,aAAO,CAAC,GAAG,OAAO,GAAG,GAAG,SAAS,CAAC;AAAA,IACnC;AAEA,WAAO,MAAM,YACV,WAAW,WAAW,cAAc,IACpC,WAAW,WAAW,UAAU,IAChC,WAAW,YAAY,UAAU;AAAA,EACrC;AAUA,WAAS,YAAY,KAAK;AACzB,QAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,QAAQ,GAAG;AACvC,aAAO;AAAA,IACR;AAEA,QAAI,IAAI,aAAa,WAAW,GAAG;AAClC,aAAO;AAAA,IACR;AAEA,QAAI,IAAI,MAAM,MAAM,KAAK,SAAS,KAAK,2BAA2B,GAAG;AAGpE,aAAO;AAAA,IACR;AAEA,WAAO;AAAA,EACR;AAEA,MAAI,iBAAiB;AAErB,QAAM,QAAQ,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAY7B,gBAAgB,SAAS,eAAe,KAAK,OAAO,SAAS;AAC5D,cAAQ,OAAO,UAAU,WAAW,QAAS,IAAI,aAAa,WAAW,KAAK;AAE9E,UAAI,SAAS,MAAM,QAAQ,QAAQ,EAAE,EAAE,MAAM,GAAG,EAAE,OAAO,OAAO;AAChE,UAAI,SAAS,CAAC,IAAI,aAAa,kBAAkB,KAAK;AAEtD,UAAI,cAAc,oBAAoB,IAAI,WAAW;AACrD,UAAI,aAAa,YAAY,iBAAiB,GAAG,EAAE,UAAU;AAC7D,UAAI,iBAAiB,MAAM,KAAK,SAAS,KAAK,kBAAkB;AAChE,UAAI,cAAc,IAAI,cAAc,MAAM;AAC1C,UAAI,gBAAgB,iBAAiB,MAAM,eAAe;AAC1D,UAAI;AAAA;AAAA,QAA+C,CAAC;AAAA;AACpD,UAAI,iBAAiB,YAAY,YAAY,MAAM,YAAY;AAC/D,UAAI,gBAAgB,iBAAiB,eAAe,SAAS,IAAI;AAWjE,UAAI,gBAAgB,CAAC,eAAe,iBAAiB,cAAc,IAAI,uBAAuB,KAAK,WAAW;AAE9G,aAAO,QAAQ,SAAU,cAAc;AACtC,YAAI,QAAQ,aAAa,MAAM,GAAG;AAElC,YAAIA,SAAQ,CAAC,MAAM,CAAC;AACpB,YAAI,MAAM,CAAC,MAAM,CAAC,KAAKA;AACvB,cAAM,KAAK,IAAI,gBAAgB,QAAQ,GAAG;AAE1C,YAAI,MAAMA,QAAO;AAChB;AAAA,QACD;AAGA,YAAI,OAAO,IAAI,cAAc,iCAAiC,eAAe,IAAI,KAAK,SAAS,cAAc,KAAK;AAElH,sBAAc,KAAK,WAAY;AAC9B,eAAK,aAAa,eAAe,MAAM;AACvC,eAAK,aAAa,cAAc,YAAY;AAC5C,eAAK,aAAa,WAAW,MAAM;AAAA,QACpC,CAAC;AAGD,YAAI,kBAAkB,MAAM,QAAQ,aAAa;AAChD,cAAI,YAAY,MAAM,QAAQ,YAAY,QAAQ,KAAKA,MAAK;AAC5D,cAAI,UAAU,MAAM,QAAQ,YAAY,QAAQ,KAAK,GAAG;AAExD,cAAI,WAAW;AACd,gBAAI,MAAM,UAAU,YAAY,gBAAgB;AAChD,0BAAc,KAAK,WAAY;AAC9B,mBAAK,MAAM,MAAM;AAAA,YAClB,CAAC;AAAA,UACF;AAEA,cAAI,SAAS;AACZ,gBAAI,SAAU,QAAQ,YAAY,UAAU,YAAa,QAAQ,eAAe;AAChF,0BAAc,KAAK,WAAY;AAC9B,mBAAK,MAAM,SAAS;AAAA,YACrB,CAAC;AAAA,UACF;AAAA,QACD,OAAO;AACN,wBAAc,KAAK,WAAY;AAC9B,iBAAK,aAAa,cAAc,OAAOA,MAAK,CAAC;AAE7C,gBAAI,MAAMA,QAAO;AAChB,mBAAK,aAAa,YAAY,OAAO,GAAG,CAAC;AAAA,YAC1C;AAEA,iBAAK,MAAM,OAAOA,SAAQ,SAAS,KAAK,aAAa,gBAAgB;AAErE,iBAAK,cAAc,IAAI,MAAM,MAAMA,SAAQ,CAAC,EAAE,KAAK,KAAK;AAAA,UACzD,CAAC;AAAA,QACF;AAEA,sBAAc,KAAK,WAAY;AAC9B,eAAK,MAAM,QAAQ,IAAI,cAAc;AAAA,QACtC,CAAC;AAED,sBAAc,KAAK,WAAY;AAG9B,wBAAc,YAAY,IAAI;AAAA,QAC/B,CAAC;AAAA,MACF,CAAC;AAED,UAAI,KAAK,IAAI;AACb,UAAI,kBAAkB,MAAM,KAAK,SAAS,KAAK,2BAA2B,KAAK,IAAI;AAOlF,YAAI,CAAC,SAAS,KAAK,2BAA2B,GAAG;AAEhD,wBAAc,KAAK,WAAY;AAC9B,gBAAI,UAAU,IAAI,2BAA2B;AAAA,UAC9C,CAAC;AAAA,QACF;AAEA,YAAI,QAAQ,SAAS,IAAI,aAAa,YAAY,KAAK,GAAG;AAG1D,WAAG,6BAA6B,GAAG,EAAE,QAAQ,SAAU,UAAU,GAAG;AACnE,cAAI,aAAa,IAAI;AACrB,mBAAS,UAAU,WAAY;AAC9B,gBAAI,OAAO,KAAK,MAAM;AAGtB,6BAAiB;AACjB,qBAAS,OAAO;AAChB,uBAAW,WAAY;AACtB,+BAAiB;AAAA,YAClB,GAAG,CAAC;AAAA,UACL;AAAA,QACD,CAAC;AAAA,MACF;AAEA,aAAO,WAAY;AAClB,sBAAc,QAAQ,YAAY;AAAA,MACnC;AAAA,IACD;AAAA,EACD;AAGA,WAAS,YAAY;AACpB,QAAI,OAAO,SAAS,KAAK,MAAM,CAAC;AAGhC,OAAG,2BAA2B,EAAE,QAAQ,SAAU,MAAM;AACvD,WAAK,WAAW,YAAY,IAAI;AAAA,IACjC,CAAC;AAED,QAAI,SAAS,KAAK,MAAM,cAAc,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC;AAEpD,QAAI,CAAC,SAAS,SAAS,eAAe,IAAI,GAAG;AAC5C;AAAA,IACD;AAEA,QAAI,KAAK,KAAK,MAAM,GAAG,KAAK,YAAY,GAAG,CAAC;AAC5C,QAAI,MAAM,SAAS,eAAe,EAAE;AAEpC,QAAI,CAAC,KAAK;AACT;AAAA,IACD;AAEA,QAAI,CAAC,IAAI,aAAa,WAAW,GAAG;AACnC,UAAI,aAAa,aAAa,EAAE;AAAA,IACjC;AAEA,QAAI,YAAY,MAAM,QAAQ,cAAc,eAAe,KAAK,OAAO,YAAY;AACnF,cAAU;AAEV,QAAI,gBAAgB;AACnB,eAAS,cAAc,2BAA2B,EAAE,eAAe;AAAA,IACpE;AAAA,EACD;AAEA,MAAI,YAAY;AAEhB,QAAM,MAAM,IAAI,uBAAuB,SAAU,KAAK;AACrD,QAAI,MAAM,IAAI,QAAQ;AACtB,QAAI,CAAC,YAAY,GAAG,GAAG;AACtB;AAAA,IACD;AASA,QAAI,MAAM;AACV,OAAG,mBAAmB,GAAG,EAAE,QAAQ,SAAU,MAAM;AAClD,aAAO,KAAK,YAAY;AACxB,WAAK,WAAW,YAAY,IAAI;AAAA,IACjC,CAAC;AAED,QAAI,OAAO,aAAa,KAAK,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG;AACnD,UAAI,OAAO,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG;AAAA,IAClC;AAAA,EACD,CAAC;AAED,QAAM,MAAM,IAAI,YAAY,SAAS,aAAa,KAAK;AACtD,QAAI,MAAM,IAAI,QAAQ;AACtB,QAAI,CAAC,YAAY,GAAG,GAAG;AACtB;AAAA,IACD;AAEA,iBAAa,SAAS;AAEtB,QAAI,iBAAiB,MAAM,QAAQ;AACnC,QAAI,sBAAsB,IAAI,WAAW,IAAI,QAAQ;AAErD,QAAI,SAAS,KAAK,kBAAkB,KAAK,kBAAkB,CAAC,qBAAqB;AAChF,YAAM,MAAM,IAAI,gBAAgB,YAAY;AAAA,IAC7C,OAAO;AACN,UAAI,YAAY,MAAM,QAAQ,cAAc,eAAe,GAAG;AAC9D,gBAAU;AACV,kBAAY,WAAW,WAAW,CAAC;AAAA,IACpC;AAAA,EACD,CAAC;AAED,SAAO,iBAAiB,cAAc,SAAS;AAC/C,SAAO,iBAAiB,UAAU,WAAY;AAC7C,QAAI,UAAU,GAAG,KAAK,EACpB,OAAO,WAAW,EAClB,IAAI,SAAU,KAAK;AACnB,aAAO,MAAM,QAAQ,cAAc,eAAe,GAAG;AAAA,IACtD,CAAC;AACF,YAAQ,QAAQ,YAAY;AAAA,EAC7B,CAAC;AAEF,GAAE;", "names": ["start"]}