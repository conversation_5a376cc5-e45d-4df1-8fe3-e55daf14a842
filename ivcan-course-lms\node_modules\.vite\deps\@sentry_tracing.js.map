{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["import type {\n  RequestInstrumentationOptions as RequestInstrumentationOptionsT,\n  SpanStatusType as SpanStatusTypeT,\n} from '@sentry-internal/tracing';\nimport {\n  Apollo,\n  BROWSER_TRACING_INTEGRATION_ID as BROWSER_TRACING_INTEGRATION_ID_T,\n  BrowserTracing as BrowserTracingT,\n  Express,\n  GraphQL,\n  IdleTransaction as IdleTransactionT,\n  Mongo,\n  Mysql,\n  Postgres,\n  Prisma,\n  Span as SpanT,\n  SpanStatus as SpanStatusT,\n  TRACEPARENT_REGEXP as TRACEPARENT_REGEXP_T,\n  Transaction as TransactionT,\n  addExtensionMethods as addExtensionMethodsT,\n  defaultRequestInstrumentationOptions as defaultRequestInstrumentationOptionsT,\n  extractTraceparentData as extractTraceparentDataT,\n  getActiveTransaction as getActiveTransactionT,\n  hasTracingEnabled as hasTracingEnabledT,\n  instrumentOutgoingRequests as instrumentOutgoingRequestsT,\n  spanStatusfromHttpCode as spanStatusfromHttpCodeT,\n  startIdleTransaction as startIdleTransactionT,\n  stripUrlQueryAndFragment as stripUrlQueryAndFragmentT,\n} from '@sentry-internal/tracing';\n\n// BrowserTracing is already exported as part of `Integrations` below (and for the moment will remain so for\n// backwards compatibility), but that interferes with treeshaking, so we also export it separately\n// here.\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n * `BrowserTracing` can be imported from `@sentry/browser` or your framework SDK\n *\n * import { BrowserTracing } from '@sentry/browser';\n * new BrowserTracing()\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const BrowserTracing = BrowserTracingT;\n\n// BrowserTracing is already exported as part of `Integrations` below (and for the moment will remain so for\n// backwards compatibility), but that interferes with treeshaking, so we also export it separately\n// here.\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n * `BrowserTracing` can be imported from `@sentry/browser` or your framework SDK\n *\n * import { BrowserTracing } from '@sentry/browser';\n * new BrowserTracing()\n */\n// eslint-disable-next-line deprecation/deprecation\nexport type BrowserTracing = BrowserTracingT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\nexport const addExtensionMethods = addExtensionMethodsT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `getActiveTransaction` can be imported from `@sentry/node`, `@sentry/browser`, or your framework SDK\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const getActiveTransaction = getActiveTransactionT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `extractTraceparentData` can be imported from `@sentry/node`, `@sentry/browser`, or your framework SDK\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const extractTraceparentData = extractTraceparentDataT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `spanStatusfromHttpCode` can be imported from `@sentry/node`, `@sentry/browser`, or your framework SDK\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const spanStatusfromHttpCode = spanStatusfromHttpCodeT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `SpanStatusType` can be imported from `@sentry/node`, `@sentry/browser`, or your framework SDK\n */\nexport type SpanStatusType = SpanStatusTypeT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `Transaction` can be imported from `@sentry/node`, `@sentry/browser`, or your framework SDK\n */\nexport const Transaction = TransactionT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `Transaction` can be imported from `@sentry/node`, `@sentry/browser`, or your framework SDK\n */\nexport type Transaction = TransactionT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `Span` can be imported from `@sentry/node`, `@sentry/browser`, or your framework SDK\n */\nexport const Span = SpanT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `Span` can be imported from `@sentry/node`, `@sentry/browser`, or your framework SDK\n */\nexport type Span = SpanT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\nexport const BROWSER_TRACING_INTEGRATION_ID = BROWSER_TRACING_INTEGRATION_ID_T;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `defaultRequestInstrumentationOptions` can be imported from `@sentry/browser`, or your framework SDK\n */\nexport const defaultRequestInstrumentationOptions = defaultRequestInstrumentationOptionsT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `hasTracingEnabled` can be imported from `@sentry/utils`\n */\nexport const hasTracingEnabled = hasTracingEnabledT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `stripUrlQueryAndFragment` can be imported from `@sentry/utils`\n */\nexport const stripUrlQueryAndFragment = stripUrlQueryAndFragmentT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n *\n * `TRACEPARENT_REGEXP` can be imported from `@sentry/utils`\n */\nexport const TRACEPARENT_REGEXP = TRACEPARENT_REGEXP_T;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\nexport const IdleTransaction = IdleTransactionT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\nexport type IdleTransaction = IdleTransactionT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\nexport const instrumentOutgoingRequests = instrumentOutgoingRequestsT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\nexport const startIdleTransaction = startIdleTransactionT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const SpanStatus = SpanStatusT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport type SpanStatus = SpanStatusT;\n\n/**\n * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n */\nexport type RequestInstrumentationOptions = RequestInstrumentationOptionsT;\n\nexport const Integrations = {\n  /**\n   * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n   * `BrowserTracing` can be imported from `@sentry/browser` or your framework SDK\n   *\n   * import { BrowserTracing } from '@sentry/browser';\n   * new BrowserTracing()\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  BrowserTracing: BrowserTracing,\n  /**\n   * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n   * `Apollo` can be imported from `@sentry/node`\n   *\n   * import { Integrations } from '@sentry/node';\n   * new Integrations.Apollo({ ... })\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  Apollo: Apollo,\n  /**\n   * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n   * `Express` can be imported from `@sentry/node`\n   *\n   * import { Integrations } from '@sentry/node';\n   * new Integrations.Express({ ... })\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  Express: Express,\n  /**\n   * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n   * `GraphQL` can be imported from `@sentry/node`\n   *\n   * import { Integrations } from '@sentry/node';\n   * new Integrations.GraphQL({ ... })\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  GraphQL: GraphQL,\n  /**\n   * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n   * `Mongo` can be imported from `@sentry/node`\n   *\n   * import { Integrations } from '@sentry/node';\n   * new Integrations.Mongo({ ... })\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  Mongo: Mongo,\n  /**\n   * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n   * `Mysql` can be imported from `@sentry/node`\n   *\n   * import { Integrations } from '@sentry/node';\n   * new Integrations.Mysql({ ... })\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  Mysql: Mysql,\n  /**\n   * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n   * `Postgres` can be imported from `@sentry/node`\n   *\n   * import { Integrations } from '@sentry/node';\n   * new Integrations.Postgres({ ... })\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  Postgres: Postgres,\n  /**\n   * @deprecated `@sentry/tracing` has been deprecated and will be moved to to `@sentry/node`, `@sentry/browser`, or your framework SDK in the next major version.\n   * `Prisma` can be imported from `@sentry/node`\n   *\n   * import { Integrations } from '@sentry/node';\n   * new Integrations.Prisma({ ... })\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  Prisma: Prisma,\n};\n\n// Treeshakable guard to remove all code related to tracing\ndeclare const __SENTRY_TRACING__: boolean;\n\n// Guard for tree\nif (typeof __SENTRY_TRACING__ === 'undefined' || __SENTRY_TRACING__) {\n  // We are patching the global object with our hub extension methods\n  addExtensionMethodsT();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCO,IAAMA,kBAAiBC;AAkBvB,IAAMC,uBAAsBC;AAQ5B,IAAMC,wBAAuBC;AAQ7B,IAAMC,0BAAyBC;AAQ/B,IAAMC,0BAAyBC;AAc/B,IAAMC,eAAcC;AAcpB,IAAMC,QAAOC;AAYb,IAAMC,kCAAiCC;AAOvC,IAAMC,wCAAuCC;AAO7C,IAAMC,qBAAoBC;AAO1B,IAAMC,4BAA2BC;AAOjC,IAAMC,sBAAqBC;AAK3B,IAAMC,mBAAkBC;AAUxB,IAAMC,8BAA6BC;AAKnC,IAAMC,wBAAuBC;AAM7B,IAAMC,cAAaC;AAanB,IAAM,eAAe;;;;;;;;;EAS1B,gBAAgB/B;;;;;;;;;EAShB;;;;;;;;;EASA;;;;;;;;;EASA;;;;;;;;;EASA;;;;;;;;;EASA;;;;;;;;;EASA;;;;;;;;;EASA;AACF;AAMA,IAAI,OAAO,uBAAuB,eAAe,oBAAoB;AAEnEG,sBAAoB;AACtB;", "names": ["BrowserTracing", "BrowserTracingT", "addExtensionMethods", "addExtensionMethodsT", "getActiveTransaction", "getActiveTransactionT", "extractTraceparentData", "extractTraceparentDataT", "spanStatusfromHttpCode", "spanStatusfromHttpCodeT", "Transaction", "TransactionT", "Span", "SpanT", "BROWSER_TRACING_INTEGRATION_ID", "BROWSER_TRACING_INTEGRATION_ID_T", "defaultRequestInstrumentationOptions", "defaultRequestInstrumentationOptionsT", "hasTracingEnabled", "hasTracingEnabledT", "stripUrlQueryAndFragment", "stripUrlQueryAndFragmentT", "TRACEPARENT_REGEXP", "TRACEPARENT_REGEXP_T", "IdleTransaction", "IdleTransactionT", "instrumentOutgoingRequests", "instrumentOutgoingRequestsT", "startIdleTransaction", "startIdleTransactionT", "SpanStatus", "SpanStatusT"]}