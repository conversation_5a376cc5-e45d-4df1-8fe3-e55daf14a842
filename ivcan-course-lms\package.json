{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "prebuild": "node scripts/ensure-env.js", "build": "vite build", "postbuild": "node scripts/copy-netlify-files.js && node scripts/fix-netlify-mime-types.js", "build:dev": "vite build --mode development", "build:analyze": "node scripts/analyze-bundle.js", "build:fast": "vite build --minify esbuild", "build:prod": "node scripts/build-prod.js", "reset:theme": "echo 'Add the reset-theme.js script to your HTML or run the code in your browser console'", "lint": "eslint .", "preview": "vite preview", "preview:fast": "vite preview --port 8081 --host", "optimize": "node scripts/optimize-app.js", "supabase:migrations": "node scripts/apply-migrations.js", "supabase:types": "node scripts/generate-types.js", "check:supabase": "node scripts/check-supabase-health.js", "test:rls": "node scripts/test-rls-policies.js", "migrate": "node scripts/migrate.js", "migrate:create": "node scripts/migrate.js --create", "migrate:remote": "node scripts/migrate.js --remote", "test:course": "ts-node src/tests/courseApi.test.ts", "fix:course-completion": "node scripts/fix-course-completion.js", "fix:all-progress": "node scripts/fix-all-lesson-progress.js", "fix:user-progress": "node scripts/fix-user-progress.js", "fix:lesson-column": "node scripts/fix-lesson-completion-column.js", "fix:supabase-rpc": "node scripts/fix-supabase-rpc.js", "fix:performance-issues": "node scripts/fix-performance-issues.js", "reset:module-completion": "node scripts/reset-module-completion.js", "reset:all-module-completions": "node scripts/reset-all-module-completions.js", "disable:auto-completion": "node scripts/disable-auto-completion.js", "fix:incorrect-completions": "node scripts/fix-incorrect-completions.js", "fix:lesson-progress": "node scripts/fix-lesson-progress.js", "create:module-progress-table": "node scripts/create-user-module-progress-table.js", "fix:all-issues": "node scripts/fix-all-issues.js", "fix:storage-rls": "node scripts/fix-storage-rls.js", "run:storage-sql": "node scripts/run-storage-sql.js", "optimize:images": "node scripts/optimize-images.js", "update:browserslist": "node scripts/update-browserslist.js", "fix:profile": "node scripts/fix-profile-page.js", "fix:all-errors": "node scripts/fix-all-errors.js", "optimize:performance": "node scripts/optimize-performance.js", "optimize:bundle": "node scripts/optimize-bundle.js", "optimize:fonts": "node scripts/optimize-fonts.js", "optimize:css": "node scripts/optimize-css.js", "optimize:all": "node scripts/optimize-all.js", "fix:security": "node scripts/apply-security-fixes.js", "fix:database-security": "node scripts/fix-database-security.js", "fix:certificates": "node scripts/fix-certificates.js", "test:certificates": "node scripts/test-certificate-generation.js", "test:badge-logic": "node scripts/test-badge-certificate-logic.js", "organize:code": "node scripts/organize-code.js", "organize:check-unused": "node scripts/organize-code.js --check-unused", "organize:check-duplicates": "node scripts/organize-code.js --check-duplicates", "make:teacher": "node scripts/make-user-teacher.js", "fix:course-schema": "node scripts/fix-course-schema.js", "fix:supabase-courses": "node scripts/fix-supabase-course-integration.js", "fix:connection": "node scripts/fix-supabase-connection.js", "fix:netlify-mime": "node scripts/fix-netlify-mime-types.js", "db:reset": "node scripts/apply-new-database.js", "setup:google-oauth": "node scripts/setup-google-oauth.js", "analyze:bundle": "vite build --mode production && npx rollup-plugin-visualizer dist/stats.html", "a11y:check": "npx axe src --exit --tags wcag2a,wcag2aa,section508,experimental --reporter cli"}, "dependencies": {"@axe-core/react": "^4.7.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/react": "^7.92.0", "@sentry/tracing": "^7.92.0", "@tanstack/react-query": "^5.70.0", "@tiptap/extension-code-block-lowlight": "^2.12.0", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-strike": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/extension-youtube": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/dompurify": "^3.0.5", "@types/prismjs": "^1.26.5", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dompurify": "^3.2.5", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.6.2", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "lowlight": "^3.3.0", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "prismjs": "^1.30.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-markdown": "^9.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "remark-gfm": "^4.0.1", "sonner": "^1.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "turndown-plugin-gfm": "^1.0.2", "uuid": "^11.1.0", "vaul": "^0.9.3", "vite-plugin-compression": "^0.5.1", "zod": "^3.23.8"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.9.0", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^5.12.0", "sharp": "^0.34.1", "supabase": "^2.23.4", "tailwindcss": "^3.4.11", "terser": "^5.29.2", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-plugin-compression": "^0.5.1"}}