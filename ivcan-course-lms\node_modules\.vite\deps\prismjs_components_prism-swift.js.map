{"version": 3, "sources": ["../../prismjs/components/prism-swift.js"], "sourcesContent": ["Prism.languages.swift = {\n\t'comment': {\n\t\t// Nested comments are supported up to 2 levels\n\t\tpattern: /(^|[^\\\\:])(?:\\/\\/.*|\\/\\*(?:[^/*]|\\/(?!\\*)|\\*(?!\\/)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\*\\/)/,\n\t\tlookbehind: true,\n\t\tgreedy: true\n\t},\n\t'string-literal': [\n\t\t// https://docs.swift.org/swift-book/LanguageGuide/StringsAndCharacters.html\n\t\t{\n\t\t\tpattern: RegExp(\n\t\t\t\t/(^|[^\"#])/.source\n\t\t\t\t+ '(?:'\n\t\t\t\t// single-line string\n\t\t\t\t+ /\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^(])|[^\\\\\\r\\n\"])*\"/.source\n\t\t\t\t+ '|'\n\t\t\t\t// multi-line string\n\t\t\t\t+ /\"\"\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|[^(])|[^\\\\\"]|\"(?!\"\"))*\"\"\"/.source\n\t\t\t\t+ ')'\n\t\t\t\t+ /(?![\"#])/.source\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'interpolation': {\n\t\t\t\t\tpattern: /(\\\\\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\tinside: null // see below\n\t\t\t\t},\n\t\t\t\t'interpolation-punctuation': {\n\t\t\t\t\tpattern: /^\\)|\\\\\\($/,\n\t\t\t\t\talias: 'punctuation'\n\t\t\t\t},\n\t\t\t\t'punctuation': /\\\\(?=[\\r\\n])/,\n\t\t\t\t'string': /[\\s\\S]+/\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\tpattern: RegExp(\n\t\t\t\t/(^|[^\"#])(#+)/.source\n\t\t\t\t+ '(?:'\n\t\t\t\t// single-line string\n\t\t\t\t+ /\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^#])|[^\\\\\\r\\n])*?\"/.source\n\t\t\t\t+ '|'\n\t\t\t\t// multi-line string\n\t\t\t\t+ /\"\"\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|[^#])|[^\\\\])*?\"\"\"/.source\n\t\t\t\t+ ')'\n\t\t\t\t+ '\\\\2'\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'interpolation': {\n\t\t\t\t\tpattern: /(\\\\#+\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\tinside: null // see below\n\t\t\t\t},\n\t\t\t\t'interpolation-punctuation': {\n\t\t\t\t\tpattern: /^\\)|\\\\#+\\($/,\n\t\t\t\t\talias: 'punctuation'\n\t\t\t\t},\n\t\t\t\t'string': /[\\s\\S]+/\n\t\t\t}\n\t\t},\n\t],\n\n\t'directive': {\n\t\t// directives with conditions\n\t\tpattern: RegExp(\n\t\t\t/#/.source\n\t\t\t+ '(?:'\n\t\t\t+ (\n\t\t\t\t/(?:elseif|if)\\b/.source\n\t\t\t\t+ '(?:[ \\t]*'\n\t\t\t\t// This regex is a little complex. It's equivalent to this:\n\t\t\t\t//   (?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*<round>)?|<round>)(?:[ \\t]*(?:&&|\\|\\|))?\n\t\t\t\t// where <round> is a general parentheses expression.\n\t\t\t\t+ /(?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*\\((?:[^()]|\\([^()]*\\))*\\))?|\\((?:[^()]|\\([^()]*\\))*\\))(?:[ \\t]*(?:&&|\\|\\|))?/.source\n\t\t\t\t+ ')+'\n\t\t\t)\n\t\t\t+ '|'\n\t\t\t+ /(?:else|endif)\\b/.source\n\t\t\t+ ')'\n\t\t),\n\t\talias: 'property',\n\t\tinside: {\n\t\t\t'directive-name': /^#\\w+/,\n\t\t\t'boolean': /\\b(?:false|true)\\b/,\n\t\t\t'number': /\\b\\d+(?:\\.\\d+)*\\b/,\n\t\t\t'operator': /!|&&|\\|\\||[<>]=?/,\n\t\t\t'punctuation': /[(),]/\n\t\t}\n\t},\n\t'literal': {\n\t\tpattern: /#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\\b/,\n\t\talias: 'constant'\n\t},\n\t'other-directive': {\n\t\tpattern: /#\\w+\\b/,\n\t\talias: 'property'\n\t},\n\n\t'attribute': {\n\t\tpattern: /@\\w+/,\n\t\talias: 'atrule'\n\t},\n\n\t'function-definition': {\n\t\tpattern: /(\\bfunc\\s+)\\w+/,\n\t\tlookbehind: true,\n\t\talias: 'function'\n\t},\n\t'label': {\n\t\t// https://docs.swift.org/swift-book/LanguageGuide/ControlFlow.html#ID141\n\t\tpattern: /\\b(break|continue)\\s+\\w+|\\b[a-zA-Z_]\\w*(?=\\s*:\\s*(?:for|repeat|while)\\b)/,\n\t\tlookbehind: true,\n\t\talias: 'important'\n\t},\n\n\t'keyword': /\\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\\b/,\n\t'boolean': /\\b(?:false|true)\\b/,\n\t'nil': {\n\t\tpattern: /\\bnil\\b/,\n\t\talias: 'constant'\n\t},\n\n\t'short-argument': /\\$\\d+\\b/,\n\t'omit': {\n\t\tpattern: /\\b_\\b/,\n\t\talias: 'keyword'\n\t},\n\t'number': /\\b(?:[\\d_]+(?:\\.[\\de_]+)?|0x[a-f0-9_]+(?:\\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b/i,\n\n\t// A class name must start with an upper-case letter and be either 1 letter long or contain a lower-case letter.\n\t'class-name': /\\b[A-Z](?:[A-Z_\\d]*[a-z]\\w*)?\\b/,\n\t'function': /\\b[a-z_]\\w*(?=\\s*\\()/i,\n\t'constant': /\\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\\b/,\n\n\t// Operators are generic in Swift. Developers can even create new operators (e.g. +++).\n\t// https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html#ID481\n\t// This regex only supports ASCII operators.\n\t'operator': /[-+*/%=!<>&|^~?]+|\\.[.\\-+*/%=!<>&|^~?]+/,\n\t'punctuation': /[{}[\\]();,.:\\\\]/\n};\n\nPrism.languages.swift['string-literal'].forEach(function (rule) {\n\trule.inside['interpolation'].inside = Prism.languages.swift;\n});\n"], "mappings": ";AAAA,MAAM,UAAU,QAAQ;AAAA,EACvB,WAAW;AAAA;AAAA,IAEV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA;AAAA,IAEjB;AAAA,MACC,SAAS;AAAA,QACR,YAAY,SACV,QAEA,8DAA8D,SAC9D,MAEA,iEAAiE,SACjE,MACA,WAAW;AAAA,MACd;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,iBAAiB;AAAA,UAChB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA;AAAA,QACT;AAAA,QACA,6BAA6B;AAAA,UAC5B,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACA,eAAe;AAAA,QACf,UAAU;AAAA,MACX;AAAA,IACD;AAAA,IACA;AAAA,MACC,SAAS;AAAA,QACR,gBAAgB,SACd,QAEA,gEAAgE,SAChE,MAEA,2DAA2D,SAC3D;AAAA,MAEH;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,iBAAiB;AAAA,UAChB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA;AAAA,QACT;AAAA,QACA,6BAA6B;AAAA,UAC5B,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACA,UAAU;AAAA,MACX;AAAA,IACD;AAAA,EACD;AAAA,EAEA,aAAa;AAAA;AAAA,IAEZ,SAAS;AAAA,MACR,IAAI,SACF,SAED,kBAAkB,SAChB,aAIA,8GAA8G,SAC9G,QAED,MACA,mBAAmB,SACnB;AAAA,IACH;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,MACP,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,eAAe;AAAA,IAChB;AAAA,EACD;AAAA,EACA,WAAW;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EACA,mBAAmB;AAAA,IAClB,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EAEA,aAAa;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EAEA,uBAAuB;AAAA,IACtB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,EACR;AAAA,EACA,SAAS;AAAA;AAAA,IAER,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,EACR;AAAA,EAEA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,OAAO;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EAEA,kBAAkB;AAAA,EAClB,QAAQ;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EACA,UAAU;AAAA;AAAA,EAGV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA;AAAA;AAAA;AAAA,EAKZ,YAAY;AAAA,EACZ,eAAe;AAChB;AAEA,MAAM,UAAU,MAAM,gBAAgB,EAAE,QAAQ,SAAU,MAAM;AAC/D,OAAK,OAAO,eAAe,EAAE,SAAS,MAAM,UAAU;AACvD,CAAC;", "names": []}