// Test script to verify the lesson navigation fix
// Run with: node scripts/test-lesson-navigation-fix.js

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testLessonNavigationFix() {
  console.log('🔧 Testing Lesson Navigation Fix...\n');

  try {
    // 1. Get a course with modules and lessons
    console.log('1. Finding a course with modules and lessons...');
    
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('id, title')
      .limit(1);

    if (coursesError || !courses?.length) {
      console.log('❌ No courses found:', coursesError?.message);
      return;
    }

    const course = courses[0];
    console.log(`✅ Found course: "${course.title}" (ID: ${course.id})`);

    // 2. Get modules for this course
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id, title, module_number')
      .eq('course_id', course.id)
      .order('module_number', { ascending: true })
      .limit(2);

    if (modulesError || !modules?.length) {
      console.log('❌ No modules found:', modulesError?.message);
      return;
    }

    console.log(`✅ Found ${modules.length} modules`);

    // 3. Check for lessons and tests
    for (const module of modules) {
      console.log(`\n📚 Module: "${module.title}" (ID: ${module.id})`);

      // Check for lessons
      const { data: lessons, error: lessonsError } = await supabase
        .from('lessons')
        .select('slug, title')
        .eq('module_id', module.id)
        .order('created_at', { ascending: true })
        .limit(1);

      if (!lessonsError && lessons?.length) {
        console.log(`  ✅ Found lesson: "${lessons[0].title}" (slug: ${lessons[0].slug})`);
      }

      // Check for tests
      const { data: preTest, error: preTestError } = await supabase
        .from('module_tests')
        .select('id, title, type')
        .eq('module_id', module.id)
        .eq('type', 'pre_test')
        .maybeSingle();

      if (!preTestError && preTest) {
        console.log(`  ✅ Found pre-test: "${preTest.title}"`);
      }

      const { data: postTest, error: postTestError } = await supabase
        .from('module_tests')
        .select('id, title, type')
        .eq('module_id', module.id)
        .eq('type', 'post_test')
        .maybeSingle();

      if (!postTestError && postTest) {
        console.log(`  ✅ Found post-test: "${postTest.title}"`);
      }
    }

    // 4. Test URL patterns
    console.log('\n4. Testing URL patterns:');
    const baseUrl = 'http://localhost:8081';
    
    console.log('\n✅ FIXED Navigation URLs:');
    console.log(`  📍 Lesson: ${baseUrl}/course/${course.id}/lesson/{lessonSlug}`);
    console.log(`  📍 Pre-test: ${baseUrl}/course/${course.id}/module/{moduleId}?type=pre_test`);
    console.log(`  📍 Post-test: ${baseUrl}/course/${course.id}/module/{moduleId}?type=post_test`);
    
    console.log('\n❌ OLD (Broken) Navigation URLs:');
    console.log(`  📍 Pre-test: ${baseUrl}/course/${course.id}/module/{moduleId}/test?type=pre_test`);
    console.log(`  📍 Post-test: ${baseUrl}/course/${course.id}/module/{moduleId}/test?type=post_test`);

    // 5. Summary
    console.log('\n🎉 Navigation Fix Summary:');
    console.log('✅ Removed "/test" suffix from module test URLs');
    console.log('✅ Updated LessonFooter.tsx navigation logic');
    console.log('✅ Updated LessonNavigation.tsx navigation logic');
    console.log('✅ URLs now match the route pattern: /course/:courseId/module/:moduleId');
    console.log('\n📝 The 404 error when clicking "Next" to go to tests should now be fixed!');

  } catch (error) {
    console.error('❌ Unexpected error during testing:', error);
  }
}

// Run the test
testLessonNavigationFix();
