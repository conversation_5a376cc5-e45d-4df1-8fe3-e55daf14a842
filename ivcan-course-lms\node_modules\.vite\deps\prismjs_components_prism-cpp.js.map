{"version": 3, "sources": ["../../prismjs/components/prism-cpp.js"], "sourcesContent": ["(function (Prism) {\n\n\tvar keyword = /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/;\n\tvar modName = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(/<keyword>/g, function () { return keyword.source; });\n\n\tPrism.languages.cpp = Prism.languages.extend('c', {\n\t\t'class-name': [\n\t\t\t{\n\t\t\t\tpattern: RegExp(/(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source\n\t\t\t\t\t.replace(/<keyword>/g, function () { return keyword.source; })),\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t// This is intended to capture the class name of method implementations like:\n\t\t\t//   void foo::bar() const {}\n\t\t\t// However! The `foo` in the above example could also be a namespace, so we only capture the class name if\n\t\t\t// it starts with an uppercase letter. This approximation should give decent results.\n\t\t\t/\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/,\n\t\t\t// This will capture the class name before destructors like:\n\t\t\t//   Foo::~Foo() {}\n\t\t\t/\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i,\n\t\t\t// This also intends to capture the class name of method implementations but here the class has template\n\t\t\t// parameters, so it can't be a namespace (until C++ adds generic namespaces).\n\t\t\t/\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/\n\t\t],\n\t\t'keyword': keyword,\n\t\t'number': {\n\t\t\tpattern: /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,\n\t\t\tgreedy: true\n\t\t},\n\t\t'operator': />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,\n\t\t'boolean': /\\b(?:false|true)\\b/\n\t});\n\n\tPrism.languages.insertBefore('cpp', 'string', {\n\t\t'module': {\n\t\t\t// https://en.cppreference.com/w/cpp/language/modules\n\t\t\tpattern: RegExp(\n\t\t\t\t/(\\b(?:import|module)\\s+)/.source +\n\t\t\t\t'(?:' +\n\t\t\t\t// header-name\n\t\t\t\t/\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source +\n\t\t\t\t'|' +\n\t\t\t\t// module name or partition or both\n\t\t\t\t/<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(/<mod-name>/g, function () { return modName; }) +\n\t\t\t\t')'\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'string': /^[<\"][\\s\\S]+/,\n\t\t\t\t'operator': /:/,\n\t\t\t\t'punctuation': /\\./\n\t\t\t}\n\t\t},\n\t\t'raw-string': {\n\t\t\tpattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,\n\t\t\talias: 'string',\n\t\t\tgreedy: true\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('cpp', 'keyword', {\n\t\t'generic-function': {\n\t\t\tpattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,\n\t\t\tinside: {\n\t\t\t\t'function': /^\\w+/,\n\t\t\t\t'generic': {\n\t\t\t\t\tpattern: /<[\\s\\S]+/,\n\t\t\t\t\talias: 'class-name',\n\t\t\t\t\tinside: Prism.languages.cpp\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('cpp', 'operator', {\n\t\t'double-colon': {\n\t\t\tpattern: /::/,\n\t\t\talias: 'punctuation'\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('cpp', 'class-name', {\n\t\t// the base clause is an optional list of parent classes\n\t\t// https://en.cppreference.com/w/cpp/language/class\n\t\t'base-clause': {\n\t\t\tpattern: /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: Prism.languages.extend('cpp', {})\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('inside', 'double-colon', {\n\t\t// All untokenized words that are not namespaces should be class names\n\t\t'class-name': /\\b[a-z_]\\w*\\b(?!\\s*::)/i\n\t}, Prism.languages.cpp['base-clause']);\n\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAEjB,MAAI,UAAU;AACd,MAAI,UAAU,uCAAuC,OAAO,QAAQ,cAAc,WAAY;AAAE,WAAO,QAAQ;AAAA,EAAQ,CAAC;AAExH,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,KAAK;AAAA,IACjD,cAAc;AAAA,MACb;AAAA,QACC,SAAS,OAAO,gEAAgE,OAC9E,QAAQ,cAAc,WAAY;AAAE,iBAAO,QAAQ;AAAA,QAAQ,CAAC,CAAC;AAAA,QAC/D,YAAY;AAAA,MACb;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA;AAAA;AAAA;AAAA,MAGA;AAAA;AAAA;AAAA,MAGA;AAAA,IACD;AAAA,IACA,WAAW;AAAA,IACX,UAAU;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,IACT;AAAA,IACA,YAAY;AAAA,IACZ,WAAW;AAAA,EACZ,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,OAAO,UAAU;AAAA,IAC7C,UAAU;AAAA;AAAA,MAET,SAAS;AAAA,QACR,2BAA2B,SAC3B;AAAA,QAEA,mDAAmD,SACnD;AAAA,QAEA,kDAAkD,OAAO,QAAQ,eAAe,WAAY;AAAE,iBAAO;AAAA,QAAS,CAAC,IAC/G;AAAA,MACD;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,eAAe;AAAA,MAChB;AAAA,IACD;AAAA,IACA,cAAc;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,OAAO,WAAW;AAAA,IAC9C,oBAAoB;AAAA,MACnB,SAAS;AAAA,MACT,QAAQ;AAAA,QACP,YAAY;AAAA,QACZ,WAAW;AAAA,UACV,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQA,OAAM,UAAU;AAAA,QACzB;AAAA,MACD;AAAA,IACD;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,IAC/C,gBAAgB;AAAA,MACf,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,OAAO,cAAc;AAAA;AAAA;AAAA,IAGjD,eAAe;AAAA,MACd,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQA,OAAM,UAAU,OAAO,OAAO,CAAC,CAAC;AAAA,IACzC;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,UAAU,gBAAgB;AAAA;AAAA,IAEtD,cAAc;AAAA,EACf,GAAGA,OAAM,UAAU,IAAI,aAAa,CAAC;AAEtC,GAAE,KAAK;", "names": ["Prism"]}