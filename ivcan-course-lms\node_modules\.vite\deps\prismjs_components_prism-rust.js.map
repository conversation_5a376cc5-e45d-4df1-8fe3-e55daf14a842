{"version": 3, "sources": ["../../prismjs/components/prism-rust.js"], "sourcesContent": ["(function (Prism) {\n\n\tvar multilineComment = /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\//.source;\n\tfor (var i = 0; i < 2; i++) {\n\t\t// support 4 levels of nested comments\n\t\tmultilineComment = multilineComment.replace(/<self>/g, function () { return multilineComment; });\n\t}\n\tmultilineComment = multilineComment.replace(/<self>/g, function () { return /[^\\s\\S]/.source; });\n\n\n\tPrism.languages.rust = {\n\t\t'comment': [\n\t\t\t{\n\t\t\t\tpattern: RegExp(/(^|[^\\\\])/.source + multilineComment),\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(^|[^\\\\:])\\/\\/.*/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t}\n\t\t],\n\t\t'string': {\n\t\t\tpattern: /b?\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|b?r(#*)\"(?:[^\"]|\"(?!\\1))*\"\\1/,\n\t\t\tgreedy: true\n\t\t},\n\t\t'char': {\n\t\t\tpattern: /b?'(?:\\\\(?:x[0-7][\\da-fA-F]|u\\{(?:[\\da-fA-F]_*){1,6}\\}|.)|[^\\\\\\r\\n\\t'])'/,\n\t\t\tgreedy: true\n\t\t},\n\t\t'attribute': {\n\t\t\tpattern: /#!?\\[(?:[^\\[\\]\"]|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")*\\]/,\n\t\t\tgreedy: true,\n\t\t\talias: 'attr-name',\n\t\t\tinside: {\n\t\t\t\t'string': null // see below\n\t\t\t}\n\t\t},\n\n\t\t// Closure params should not be confused with bitwise OR |\n\t\t'closure-params': {\n\t\t\tpattern: /([=(,:]\\s*|\\bmove\\s*)\\|[^|]*\\||\\|[^|]*\\|(?=\\s*(?:\\{|->))/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'closure-punctuation': {\n\t\t\t\t\tpattern: /^\\||\\|$/,\n\t\t\t\t\talias: 'punctuation'\n\t\t\t\t},\n\t\t\t\trest: null // see below\n\t\t\t}\n\t\t},\n\n\t\t'lifetime-annotation': {\n\t\t\tpattern: /'\\w+/,\n\t\t\talias: 'symbol'\n\t\t},\n\n\t\t'fragment-specifier': {\n\t\t\tpattern: /(\\$\\w+:)[a-z]+/,\n\t\t\tlookbehind: true,\n\t\t\talias: 'punctuation'\n\t\t},\n\t\t'variable': /\\$\\w+/,\n\n\t\t'function-definition': {\n\t\t\tpattern: /(\\bfn\\s+)\\w+/,\n\t\t\tlookbehind: true,\n\t\t\talias: 'function'\n\t\t},\n\t\t'type-definition': {\n\t\t\tpattern: /(\\b(?:enum|struct|trait|type|union)\\s+)\\w+/,\n\t\t\tlookbehind: true,\n\t\t\talias: 'class-name'\n\t\t},\n\t\t'module-declaration': [\n\t\t\t{\n\t\t\t\tpattern: /(\\b(?:crate|mod)\\s+)[a-z][a-z_\\d]*/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'namespace'\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\b(?:crate|self|super)\\s*)::\\s*[a-z][a-z_\\d]*\\b(?:\\s*::(?:\\s*[a-z][a-z_\\d]*\\s*::)*)?/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'namespace',\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /::/\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'keyword': [\n\t\t\t// https://github.com/rust-lang/reference/blob/master/src/keywords.md\n\t\t\t/\\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\\b/,\n\t\t\t// primitives and str\n\t\t\t// https://doc.rust-lang.org/stable/rust-by-example/primitives.html\n\t\t\t/\\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\\b/\n\t\t],\n\n\t\t// functions can technically start with an upper-case letter, but this will introduce a lot of false positives\n\t\t// and Rust's naming conventions recommend snake_case anyway.\n\t\t// https://doc.rust-lang.org/1.0.0/style/style/naming/README.html\n\t\t'function': /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())/,\n\t\t'macro': {\n\t\t\tpattern: /\\b\\w+!/,\n\t\t\talias: 'property'\n\t\t},\n\t\t'constant': /\\b[A-Z_][A-Z_\\d]+\\b/,\n\t\t'class-name': /\\b[A-Z]\\w*\\b/,\n\n\t\t'namespace': {\n\t\t\tpattern: /(?:\\b[a-z][a-z_\\d]*\\s*::\\s*)*\\b[a-z][a-z_\\d]*\\s*::(?!\\s*<)/,\n\t\t\tinside: {\n\t\t\t\t'punctuation': /::/\n\t\t\t}\n\t\t},\n\n\t\t// Hex, oct, bin, dec numbers with visual separators and type suffix\n\t\t'number': /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\\b/,\n\t\t'boolean': /\\b(?:false|true)\\b/,\n\t\t'punctuation': /->|\\.\\.=|\\.{1,3}|::|[{}[\\];(),:]/,\n\t\t'operator': /[-+*\\/%!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?=?|[@?]/\n\t};\n\n\tPrism.languages.rust['closure-params'].inside.rest = Prism.languages.rust;\n\tPrism.languages.rust['attribute'].inside['string'] = Prism.languages.rust['string'];\n\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAEjB,MAAI,mBAAmB,8CAA8C;AACrE,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAE3B,uBAAmB,iBAAiB,QAAQ,WAAW,WAAY;AAAE,aAAO;AAAA,IAAkB,CAAC;AAAA,EAChG;AACA,qBAAmB,iBAAiB,QAAQ,WAAW,WAAY;AAAE,WAAO,UAAU;AAAA,EAAQ,CAAC;AAG/F,EAAAA,OAAM,UAAU,OAAO;AAAA,IACtB,WAAW;AAAA,MACV;AAAA,QACC,SAAS,OAAO,YAAY,SAAS,gBAAgB;AAAA,QACrD,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,UAAU;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,UAAU;AAAA;AAAA,MACX;AAAA,IACD;AAAA;AAAA,IAGA,kBAAkB;AAAA,MACjB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,uBAAuB;AAAA,UACtB,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACA,MAAM;AAAA;AAAA,MACP;AAAA,IACD;AAAA,IAEA,uBAAuB;AAAA,MACtB,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,IAEA,sBAAsB;AAAA,MACrB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,YAAY;AAAA,IAEZ,uBAAuB;AAAA,MACtB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,mBAAmB;AAAA,MAClB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA,IACA,sBAAsB;AAAA,MACrB;AAAA,QACC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,MACR;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,UACP,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAAA,IACA,WAAW;AAAA;AAAA,MAEV;AAAA;AAAA;AAAA,MAGA;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY;AAAA,IACZ,SAAS;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,IAEd,aAAa;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,QACP,eAAe;AAAA,MAChB;AAAA,IACD;AAAA;AAAA,IAGA,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,YAAY;AAAA,EACb;AAEA,EAAAA,OAAM,UAAU,KAAK,gBAAgB,EAAE,OAAO,OAAOA,OAAM,UAAU;AACrE,EAAAA,OAAM,UAAU,KAAK,WAAW,EAAE,OAAO,QAAQ,IAAIA,OAAM,UAAU,KAAK,QAAQ;AAEnF,GAAE,KAAK;", "names": ["Prism"]}