{"version": 3, "sources": ["../../prismjs/components/prism-kotlin.js"], "sourcesContent": ["(function (Prism) {\n\tPrism.languages.kotlin = Prism.languages.extend('clike', {\n\t\t'keyword': {\n\t\t\t// The lookbehind prevents wrong highlighting of e.g. kotlin.properties.get\n\t\t\tpattern: /(^|[^.])\\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'function': [\n\t\t\t{\n\t\t\t\tpattern: /(?:`[^\\r\\n`]+`|\\b\\w+)(?=\\s*\\()/,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\.)(?:`[^\\r\\n`]+`|\\w+)(?=\\s*\\{)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t}\n\t\t],\n\t\t'number': /\\b(?:0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?[fFL]?)\\b/,\n\t\t'operator': /\\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\\/*%<>]=?|[?:]:?|\\.\\.|&&|\\|\\||\\b(?:and|inv|or|shl|shr|ushr|xor)\\b/\n\t});\n\n\tdelete Prism.languages.kotlin['class-name'];\n\n\tvar interpolationInside = {\n\t\t'interpolation-punctuation': {\n\t\t\tpattern: /^\\$\\{?|\\}$/,\n\t\t\talias: 'punctuation'\n\t\t},\n\t\t'expression': {\n\t\t\tpattern: /[\\s\\S]+/,\n\t\t\tinside: Prism.languages.kotlin\n\t\t}\n\t};\n\n\tPrism.languages.insertBefore('kotlin', 'string', {\n\t\t// https://kotlinlang.org/spec/expressions.html#string-interpolation-expressions\n\t\t'string-literal': [\n\t\t\t{\n\t\t\t\tpattern: /\"\"\"(?:[^$]|\\$(?:(?!\\{)|\\{[^{}]*\\}))*?\"\"\"/,\n\t\t\t\talias: 'multiline',\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation': {\n\t\t\t\t\t\tpattern: /\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n\t\t\t\t\t\tinside: interpolationInside\n\t\t\t\t\t},\n\t\t\t\t\t'string': /[\\s\\S]+/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\"(?:[^\"\\\\\\r\\n$]|\\\\.|\\$(?:(?!\\{)|\\{[^{}]*\\}))*\"/,\n\t\t\t\talias: 'singleline',\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation': {\n\t\t\t\t\t\tpattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\tinside: interpolationInside\n\t\t\t\t\t},\n\t\t\t\t\t'string': /[\\s\\S]+/\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'char': {\n\t\t\t// https://kotlinlang.org/spec/expressions.html#character-literals\n\t\t\tpattern: /'(?:[^'\\\\\\r\\n]|\\\\(?:.|u[a-fA-F0-9]{0,4}))'/,\n\t\t\tgreedy: true\n\t\t}\n\t});\n\n\tdelete Prism.languages.kotlin['string'];\n\n\tPrism.languages.insertBefore('kotlin', 'keyword', {\n\t\t'annotation': {\n\t\t\tpattern: /\\B@(?:\\w+:)?(?:[A-Z]\\w*|\\[[^\\]]+\\])/,\n\t\t\talias: 'builtin'\n\t\t}\n\t});\n\n\tPrism.languages.insertBefore('kotlin', 'function', {\n\t\t'label': {\n\t\t\tpattern: /\\b\\w+@|@\\w+\\b/,\n\t\t\talias: 'symbol'\n\t\t}\n\t});\n\n\tPrism.languages.kt = Prism.languages.kotlin;\n\tPrism.languages.kts = Prism.languages.kotlin;\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AACjB,EAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,SAAS;AAAA,IACxD,WAAW;AAAA;AAAA,MAEV,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,YAAY;AAAA,MACX;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,IACA,UAAU;AAAA,IACV,YAAY;AAAA,EACb,CAAC;AAED,SAAOA,OAAM,UAAU,OAAO,YAAY;AAE1C,MAAI,sBAAsB;AAAA,IACzB,6BAA6B;AAAA,MAC5B,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,IACA,cAAc;AAAA,MACb,SAAS;AAAA,MACT,QAAQA,OAAM,UAAU;AAAA,IACzB;AAAA,EACD;AAEA,EAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA;AAAA,IAEhD,kBAAkB;AAAA,MACjB;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,UACP,iBAAiB;AAAA,YAChB,SAAS;AAAA,YACT,QAAQ;AAAA,UACT;AAAA,UACA,UAAU;AAAA,QACX;AAAA,MACD;AAAA,MACA;AAAA,QACC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,UACP,iBAAiB;AAAA,YAChB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACT;AAAA,UACA,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAAA,IACA,QAAQ;AAAA;AAAA,MAEP,SAAS;AAAA,MACT,QAAQ;AAAA,IACT;AAAA,EACD,CAAC;AAED,SAAOA,OAAM,UAAU,OAAO,QAAQ;AAEtC,EAAAA,OAAM,UAAU,aAAa,UAAU,WAAW;AAAA,IACjD,cAAc;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,aAAa,UAAU,YAAY;AAAA,IAClD,SAAS;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,EACD,CAAC;AAED,EAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AACrC,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AACvC,GAAE,KAAK;", "names": ["Prism"]}