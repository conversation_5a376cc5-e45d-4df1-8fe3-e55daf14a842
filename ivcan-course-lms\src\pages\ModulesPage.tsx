import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import Layout from '../components/Layout';
import ModuleList from '../components/module/ModuleList';
import SimpleFinishCourseButton from '../components/course/SimpleFinishCourseButton';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, BookOpen, Clock, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { fetchCourseModules } from '@/services/course/courseApi';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { PageContainer, ContentSection } from '@/components/ui/floating-sidebar-container';

const ModulesPage: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { user } = useAuth();

  // Fetch course details
  const { data: course, isLoading: isLoadingCourse } = useQuery({
    queryKey: ['course-details', courseId],
    queryFn: async () => {
      if (!courseId) return null;

      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .eq('id', courseId)
        .single();

      if (error) {
        toast({
          title: "Error fetching course",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data;
    },
    enabled: !!courseId,
  });

  // Fetch modules
  const { data: modules, isLoading: isLoadingModules } = useQuery({
    queryKey: ['course-modules', courseId, user?.id],
    queryFn: () => fetchCourseModules(courseId || '', user?.id),
    enabled: !!courseId,
  });

  const isLoading = isLoadingCourse || isLoadingModules;

  if (isLoading) {
    return (
      <Layout>
        <PageContainer pageType="module">
          <div className="flex justify-center items-center h-64">
            <Loader2 className="w-8 h-8 animate-spin text-primary" />
          </div>
        </PageContainer>
      </Layout>
    );
  }

  if (!course || !modules) {
    return (
      <Layout>
        <PageContainer pageType="module">
          <div className="text-center p-8">
            <h2 className="text-2xl font-bold mb-4">Course Not Found</h2>
            <p className="mb-6">The course you're looking for doesn't exist or you don't have permission to access it.</p>
            <Button onClick={() => navigate('/dashboard')}>
              Return to Dashboard
            </Button>
          </div>
        </PageContainer>
      </Layout>
    );
  }

  // Calculate total duration
  const totalDuration = modules.reduce((acc, module) => {
    return acc + (module.lessons?.reduce((lessonAcc, lesson) => {
      const minutes = parseInt(lesson.duration.split(':')[0]);
      return lessonAcc + (isNaN(minutes) ? 0 : minutes);
    }, 0) || 0);
  }, 0);

  // Calculate total lessons
  const totalLessons = modules.reduce((acc, module) => {
    return acc + (module.lessons?.length || 0);
  }, 0);

  return (
    <Layout>
      <PageContainer pageType="module">
        <ContentSection spacing="md">
          <div>
            <h1 className="text-3xl font-bold mb-2">{course.title}</h1>
            <p className="text-muted-foreground">Course Modules</p>
          </div>

          {/* Course Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50/80 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100/80 dark:hover:bg-gray-700/30">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20">
              <Clock className="w-5 h-5 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Duration</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">{totalDuration} minutes</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50/80 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100/80 dark:hover:bg-gray-700/30">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20">
              <BookOpen className="w-5 h-5 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Modules</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">{modules.length} total</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50/80 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100/80 dark:hover:bg-gray-700/30">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20">
              <FileText className="w-5 h-5 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Lessons</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">{totalLessons} total</p>
            </div>
          </div>
        </div>

          {/* Finish Course Button */}
          {user && modules && course && (
            <div className="mb-6">
              <SimpleFinishCourseButton
                courseId={course.id}
                userId={user.id}
                modules={modules}
                courseName={course.title}
              />
            </div>
          )}

          {/* Modules List */}
          <ModuleList modules={modules} courseId={course.id} />
        </ContentSection>
      </PageContainer>
    </Layout>
  );
};

export default ModulesPage; 