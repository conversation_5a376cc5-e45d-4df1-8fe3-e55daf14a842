// node_modules/prismjs/plugins/line-highlight/prism-line-highlight.js
(function() {
  if (typeof Prism === "undefined" || typeof document === "undefined" || !document.querySelector) {
    return;
  }
  var LINE_NUMBERS_CLASS = "line-numbers";
  var LINKABLE_LINE_NUMBERS_CLASS = "linkable-line-numbers";
  var NEW_LINE_EXP = /\n(?!$)/g;
  function $$(selector, container) {
    return Array.prototype.slice.call((container || document).querySelectorAll(selector));
  }
  function hasClass(element, className) {
    return element.classList.contains(className);
  }
  function callFunction(func) {
    func();
  }
  var isLineHeightRounded = /* @__PURE__ */ function() {
    var res;
    return function() {
      if (typeof res === "undefined") {
        var d = document.createElement("div");
        d.style.fontSize = "13px";
        d.style.lineHeight = "1.5";
        d.style.padding = "0";
        d.style.border = "0";
        d.innerHTML = "&nbsp;<br />&nbsp;";
        document.body.appendChild(d);
        res = d.offsetHeight === 38;
        document.body.removeChild(d);
      }
      return res;
    };
  }();
  function getContentBoxTopOffset(parent, child) {
    var parentStyle = getComputedStyle(parent);
    var childStyle = getComputedStyle(child);
    function pxToNumber(px) {
      return +px.substr(0, px.length - 2);
    }
    return child.offsetTop + pxToNumber(childStyle.borderTopWidth) + pxToNumber(childStyle.paddingTop) - pxToNumber(parentStyle.paddingTop);
  }
  function isActiveFor(pre) {
    if (!pre || !/pre/i.test(pre.nodeName)) {
      return false;
    }
    if (pre.hasAttribute("data-line")) {
      return true;
    }
    if (pre.id && Prism.util.isActive(pre, LINKABLE_LINE_NUMBERS_CLASS)) {
      return true;
    }
    return false;
  }
  var scrollIntoView = true;
  Prism.plugins.lineHighlight = {
    /**
     * Highlights the lines of the given pre.
     *
     * This function is split into a DOM measuring and mutate phase to improve performance.
     * The returned function mutates the DOM when called.
     *
     * @param {HTMLElement} pre
     * @param {string | null} [lines]
     * @param {string} [classes='']
     * @returns {() => void}
     */
    highlightLines: function highlightLines(pre, lines, classes) {
      lines = typeof lines === "string" ? lines : pre.getAttribute("data-line") || "";
      var ranges = lines.replace(/\s+/g, "").split(",").filter(Boolean);
      var offset = +pre.getAttribute("data-line-offset") || 0;
      var parseMethod = isLineHeightRounded() ? parseInt : parseFloat;
      var lineHeight = parseMethod(getComputedStyle(pre).lineHeight);
      var hasLineNumbers = Prism.util.isActive(pre, LINE_NUMBERS_CLASS);
      var codeElement = pre.querySelector("code");
      var parentElement = hasLineNumbers ? pre : codeElement || pre;
      var mutateActions = (
        /** @type {(() => void)[]} */
        []
      );
      var lineBreakMatch = codeElement.textContent.match(NEW_LINE_EXP);
      var numberOfLines = lineBreakMatch ? lineBreakMatch.length + 1 : 1;
      var codePreOffset = !codeElement || parentElement == codeElement ? 0 : getContentBoxTopOffset(pre, codeElement);
      ranges.forEach(function(currentRange) {
        var range = currentRange.split("-");
        var start2 = +range[0];
        var end = +range[1] || start2;
        end = Math.min(numberOfLines + offset, end);
        if (end < start2) {
          return;
        }
        var line = pre.querySelector('.line-highlight[data-range="' + currentRange + '"]') || document.createElement("div");
        mutateActions.push(function() {
          line.setAttribute("aria-hidden", "true");
          line.setAttribute("data-range", currentRange);
          line.className = (classes || "") + " line-highlight";
        });
        if (hasLineNumbers && Prism.plugins.lineNumbers) {
          var startNode = Prism.plugins.lineNumbers.getLine(pre, start2);
          var endNode = Prism.plugins.lineNumbers.getLine(pre, end);
          if (startNode) {
            var top = startNode.offsetTop + codePreOffset + "px";
            mutateActions.push(function() {
              line.style.top = top;
            });
          }
          if (endNode) {
            var height = endNode.offsetTop - startNode.offsetTop + endNode.offsetHeight + "px";
            mutateActions.push(function() {
              line.style.height = height;
            });
          }
        } else {
          mutateActions.push(function() {
            line.setAttribute("data-start", String(start2));
            if (end > start2) {
              line.setAttribute("data-end", String(end));
            }
            line.style.top = (start2 - offset - 1) * lineHeight + codePreOffset + "px";
            line.textContent = new Array(end - start2 + 2).join(" \n");
          });
        }
        mutateActions.push(function() {
          line.style.width = pre.scrollWidth + "px";
        });
        mutateActions.push(function() {
          parentElement.appendChild(line);
        });
      });
      var id = pre.id;
      if (hasLineNumbers && Prism.util.isActive(pre, LINKABLE_LINE_NUMBERS_CLASS) && id) {
        if (!hasClass(pre, LINKABLE_LINE_NUMBERS_CLASS)) {
          mutateActions.push(function() {
            pre.classList.add(LINKABLE_LINE_NUMBERS_CLASS);
          });
        }
        var start = parseInt(pre.getAttribute("data-start") || "1");
        $$(".line-numbers-rows > span", pre).forEach(function(lineSpan, i) {
          var lineNumber = i + start;
          lineSpan.onclick = function() {
            var hash = id + "." + lineNumber;
            scrollIntoView = false;
            location.hash = hash;
            setTimeout(function() {
              scrollIntoView = true;
            }, 1);
          };
        });
      }
      return function() {
        mutateActions.forEach(callFunction);
      };
    }
  };
  function applyHash() {
    var hash = location.hash.slice(1);
    $$(".temporary.line-highlight").forEach(function(line) {
      line.parentNode.removeChild(line);
    });
    var range = (hash.match(/\.([\d,-]+)$/) || [, ""])[1];
    if (!range || document.getElementById(hash)) {
      return;
    }
    var id = hash.slice(0, hash.lastIndexOf("."));
    var pre = document.getElementById(id);
    if (!pre) {
      return;
    }
    if (!pre.hasAttribute("data-line")) {
      pre.setAttribute("data-line", "");
    }
    var mutateDom = Prism.plugins.lineHighlight.highlightLines(pre, range, "temporary ");
    mutateDom();
    if (scrollIntoView) {
      document.querySelector(".temporary.line-highlight").scrollIntoView();
    }
  }
  var fakeTimer = 0;
  Prism.hooks.add("before-sanity-check", function(env) {
    var pre = env.element.parentElement;
    if (!isActiveFor(pre)) {
      return;
    }
    var num = 0;
    $$(".line-highlight", pre).forEach(function(line) {
      num += line.textContent.length;
      line.parentNode.removeChild(line);
    });
    if (num && /^(?: \n)+$/.test(env.code.slice(-num))) {
      env.code = env.code.slice(0, -num);
    }
  });
  Prism.hooks.add("complete", function completeHook(env) {
    var pre = env.element.parentElement;
    if (!isActiveFor(pre)) {
      return;
    }
    clearTimeout(fakeTimer);
    var hasLineNumbers = Prism.plugins.lineNumbers;
    var isLineNumbersLoaded = env.plugins && env.plugins.lineNumbers;
    if (hasClass(pre, LINE_NUMBERS_CLASS) && hasLineNumbers && !isLineNumbersLoaded) {
      Prism.hooks.add("line-numbers", completeHook);
    } else {
      var mutateDom = Prism.plugins.lineHighlight.highlightLines(pre);
      mutateDom();
      fakeTimer = setTimeout(applyHash, 1);
    }
  });
  window.addEventListener("hashchange", applyHash);
  window.addEventListener("resize", function() {
    var actions = $$("pre").filter(isActiveFor).map(function(pre) {
      return Prism.plugins.lineHighlight.highlightLines(pre);
    });
    actions.forEach(callFunction);
  });
})();
//# sourceMappingURL=prismjs_plugins_line-highlight_prism-line-highlight.js.map
