{"version": 3, "sources": ["../../prismjs/plugins/line-numbers/prism-line-numbers.js"], "sourcesContent": ["(function () {\n\n\tif (typeof Prism === 'undefined' || typeof document === 'undefined') {\n\t\treturn;\n\t}\n\n\t/**\n\t * Plugin name which is used as a class name for <pre> which is activating the plugin\n\t *\n\t * @type {string}\n\t */\n\tvar PLUGIN_NAME = 'line-numbers';\n\n\t/**\n\t * Regular expression used for determining line breaks\n\t *\n\t * @type {RegExp}\n\t */\n\tvar NEW_LINE_EXP = /\\n(?!$)/g;\n\n\n\t/**\n\t * Global exports\n\t */\n\tvar config = Prism.plugins.lineNumbers = {\n\t\t/**\n\t\t * Get node for provided line number\n\t\t *\n\t\t * @param {Element} element pre element\n\t\t * @param {number} number line number\n\t\t * @returns {Element|undefined}\n\t\t */\n\t\tgetLine: function (element, number) {\n\t\t\tif (element.tagName !== 'PRE' || !element.classList.contains(PLUGIN_NAME)) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar lineNumberRows = element.querySelector('.line-numbers-rows');\n\t\t\tif (!lineNumberRows) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar lineNumberStart = parseInt(element.getAttribute('data-start'), 10) || 1;\n\t\t\tvar lineNumberEnd = lineNumberStart + (lineNumberRows.children.length - 1);\n\n\t\t\tif (number < lineNumberStart) {\n\t\t\t\tnumber = lineNumberStart;\n\t\t\t}\n\t\t\tif (number > lineNumberEnd) {\n\t\t\t\tnumber = lineNumberEnd;\n\t\t\t}\n\n\t\t\tvar lineIndex = number - lineNumberStart;\n\n\t\t\treturn lineNumberRows.children[lineIndex];\n\t\t},\n\n\t\t/**\n\t\t * Resizes the line numbers of the given element.\n\t\t *\n\t\t * This function will not add line numbers. It will only resize existing ones.\n\t\t *\n\t\t * @param {HTMLElement} element A `<pre>` element with line numbers.\n\t\t * @returns {void}\n\t\t */\n\t\tresize: function (element) {\n\t\t\tresizeElements([element]);\n\t\t},\n\n\t\t/**\n\t\t * Whether the plugin can assume that the units font sizes and margins are not depended on the size of\n\t\t * the current viewport.\n\t\t *\n\t\t * Setting this to `true` will allow the plugin to do certain optimizations for better performance.\n\t\t *\n\t\t * Set this to `false` if you use any of the following CSS units: `vh`, `vw`, `vmin`, `vmax`.\n\t\t *\n\t\t * @type {boolean}\n\t\t */\n\t\tassumeViewportIndependence: true\n\t};\n\n\t/**\n\t * Resizes the given elements.\n\t *\n\t * @param {HTMLElement[]} elements\n\t */\n\tfunction resizeElements(elements) {\n\t\telements = elements.filter(function (e) {\n\t\t\tvar codeStyles = getStyles(e);\n\t\t\tvar whiteSpace = codeStyles['white-space'];\n\t\t\treturn whiteSpace === 'pre-wrap' || whiteSpace === 'pre-line';\n\t\t});\n\n\t\tif (elements.length == 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar infos = elements.map(function (element) {\n\t\t\tvar codeElement = element.querySelector('code');\n\t\t\tvar lineNumbersWrapper = element.querySelector('.line-numbers-rows');\n\t\t\tif (!codeElement || !lineNumbersWrapper) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\n\t\t\t/** @type {HTMLElement} */\n\t\t\tvar lineNumberSizer = element.querySelector('.line-numbers-sizer');\n\t\t\tvar codeLines = codeElement.textContent.split(NEW_LINE_EXP);\n\n\t\t\tif (!lineNumberSizer) {\n\t\t\t\tlineNumberSizer = document.createElement('span');\n\t\t\t\tlineNumberSizer.className = 'line-numbers-sizer';\n\n\t\t\t\tcodeElement.appendChild(lineNumberSizer);\n\t\t\t}\n\n\t\t\tlineNumberSizer.innerHTML = '0';\n\t\t\tlineNumberSizer.style.display = 'block';\n\n\t\t\tvar oneLinerHeight = lineNumberSizer.getBoundingClientRect().height;\n\t\t\tlineNumberSizer.innerHTML = '';\n\n\t\t\treturn {\n\t\t\t\telement: element,\n\t\t\t\tlines: codeLines,\n\t\t\t\tlineHeights: [],\n\t\t\t\toneLinerHeight: oneLinerHeight,\n\t\t\t\tsizer: lineNumberSizer,\n\t\t\t};\n\t\t}).filter(Boolean);\n\n\t\tinfos.forEach(function (info) {\n\t\t\tvar lineNumberSizer = info.sizer;\n\t\t\tvar lines = info.lines;\n\t\t\tvar lineHeights = info.lineHeights;\n\t\t\tvar oneLinerHeight = info.oneLinerHeight;\n\n\t\t\tlineHeights[lines.length - 1] = undefined;\n\t\t\tlines.forEach(function (line, index) {\n\t\t\t\tif (line && line.length > 1) {\n\t\t\t\t\tvar e = lineNumberSizer.appendChild(document.createElement('span'));\n\t\t\t\t\te.style.display = 'block';\n\t\t\t\t\te.textContent = line;\n\t\t\t\t} else {\n\t\t\t\t\tlineHeights[index] = oneLinerHeight;\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tinfos.forEach(function (info) {\n\t\t\tvar lineNumberSizer = info.sizer;\n\t\t\tvar lineHeights = info.lineHeights;\n\n\t\t\tvar childIndex = 0;\n\t\t\tfor (var i = 0; i < lineHeights.length; i++) {\n\t\t\t\tif (lineHeights[i] === undefined) {\n\t\t\t\t\tlineHeights[i] = lineNumberSizer.children[childIndex++].getBoundingClientRect().height;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tinfos.forEach(function (info) {\n\t\t\tvar lineNumberSizer = info.sizer;\n\t\t\tvar wrapper = info.element.querySelector('.line-numbers-rows');\n\n\t\t\tlineNumberSizer.style.display = 'none';\n\t\t\tlineNumberSizer.innerHTML = '';\n\n\t\t\tinfo.lineHeights.forEach(function (height, lineNumber) {\n\t\t\t\twrapper.children[lineNumber].style.height = height + 'px';\n\t\t\t});\n\t\t});\n\t}\n\n\t/**\n\t * Returns style declarations for the element\n\t *\n\t * @param {Element} element\n\t */\n\tfunction getStyles(element) {\n\t\tif (!element) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn window.getComputedStyle ? getComputedStyle(element) : (element.currentStyle || null);\n\t}\n\n\tvar lastWidth = undefined;\n\twindow.addEventListener('resize', function () {\n\t\tif (config.assumeViewportIndependence && lastWidth === window.innerWidth) {\n\t\t\treturn;\n\t\t}\n\t\tlastWidth = window.innerWidth;\n\n\t\tresizeElements(Array.prototype.slice.call(document.querySelectorAll('pre.' + PLUGIN_NAME)));\n\t});\n\n\tPrism.hooks.add('complete', function (env) {\n\t\tif (!env.code) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar code = /** @type {Element} */ (env.element);\n\t\tvar pre = /** @type {HTMLElement} */ (code.parentNode);\n\n\t\t// works only for <code> wrapped inside <pre> (not inline)\n\t\tif (!pre || !/pre/i.test(pre.nodeName)) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Abort if line numbers already exists\n\t\tif (code.querySelector('.line-numbers-rows')) {\n\t\t\treturn;\n\t\t}\n\n\t\t// only add line numbers if <code> or one of its ancestors has the `line-numbers` class\n\t\tif (!Prism.util.isActive(code, PLUGIN_NAME)) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Remove the class 'line-numbers' from the <code>\n\t\tcode.classList.remove(PLUGIN_NAME);\n\t\t// Add the class 'line-numbers' to the <pre>\n\t\tpre.classList.add(PLUGIN_NAME);\n\n\t\tvar match = env.code.match(NEW_LINE_EXP);\n\t\tvar linesNum = match ? match.length + 1 : 1;\n\t\tvar lineNumbersWrapper;\n\n\t\tvar lines = new Array(linesNum + 1).join('<span></span>');\n\n\t\tlineNumbersWrapper = document.createElement('span');\n\t\tlineNumbersWrapper.setAttribute('aria-hidden', 'true');\n\t\tlineNumbersWrapper.className = 'line-numbers-rows';\n\t\tlineNumbersWrapper.innerHTML = lines;\n\n\t\tif (pre.hasAttribute('data-start')) {\n\t\t\tpre.style.counterReset = 'linenumber ' + (parseInt(pre.getAttribute('data-start'), 10) - 1);\n\t\t}\n\n\t\tenv.element.appendChild(lineNumbersWrapper);\n\n\t\tresizeElements([pre]);\n\n\t\tPrism.hooks.run('line-numbers', env);\n\t});\n\n\tPrism.hooks.add('line-numbers', function (env) {\n\t\tenv.plugins = env.plugins || {};\n\t\tenv.plugins.lineNumbers = true;\n\t});\n\n}());\n"], "mappings": ";CAAC,WAAY;AAEZ,MAAI,OAAO,UAAU,eAAe,OAAO,aAAa,aAAa;AACpE;AAAA,EACD;AAOA,MAAI,cAAc;AAOlB,MAAI,eAAe;AAMnB,MAAI,SAAS,MAAM,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQxC,SAAS,SAAU,SAAS,QAAQ;AACnC,UAAI,QAAQ,YAAY,SAAS,CAAC,QAAQ,UAAU,SAAS,WAAW,GAAG;AAC1E;AAAA,MACD;AAEA,UAAI,iBAAiB,QAAQ,cAAc,oBAAoB;AAC/D,UAAI,CAAC,gBAAgB;AACpB;AAAA,MACD;AACA,UAAI,kBAAkB,SAAS,QAAQ,aAAa,YAAY,GAAG,EAAE,KAAK;AAC1E,UAAI,gBAAgB,mBAAmB,eAAe,SAAS,SAAS;AAExE,UAAI,SAAS,iBAAiB;AAC7B,iBAAS;AAAA,MACV;AACA,UAAI,SAAS,eAAe;AAC3B,iBAAS;AAAA,MACV;AAEA,UAAI,YAAY,SAAS;AAEzB,aAAO,eAAe,SAAS,SAAS;AAAA,IACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,QAAQ,SAAU,SAAS;AAC1B,qBAAe,CAAC,OAAO,CAAC;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,4BAA4B;AAAA,EAC7B;AAOA,WAAS,eAAe,UAAU;AACjC,eAAW,SAAS,OAAO,SAAU,GAAG;AACvC,UAAI,aAAa,UAAU,CAAC;AAC5B,UAAI,aAAa,WAAW,aAAa;AACzC,aAAO,eAAe,cAAc,eAAe;AAAA,IACpD,CAAC;AAED,QAAI,SAAS,UAAU,GAAG;AACzB;AAAA,IACD;AAEA,QAAI,QAAQ,SAAS,IAAI,SAAU,SAAS;AAC3C,UAAI,cAAc,QAAQ,cAAc,MAAM;AAC9C,UAAI,qBAAqB,QAAQ,cAAc,oBAAoB;AACnE,UAAI,CAAC,eAAe,CAAC,oBAAoB;AACxC,eAAO;AAAA,MACR;AAGA,UAAI,kBAAkB,QAAQ,cAAc,qBAAqB;AACjE,UAAI,YAAY,YAAY,YAAY,MAAM,YAAY;AAE1D,UAAI,CAAC,iBAAiB;AACrB,0BAAkB,SAAS,cAAc,MAAM;AAC/C,wBAAgB,YAAY;AAE5B,oBAAY,YAAY,eAAe;AAAA,MACxC;AAEA,sBAAgB,YAAY;AAC5B,sBAAgB,MAAM,UAAU;AAEhC,UAAI,iBAAiB,gBAAgB,sBAAsB,EAAE;AAC7D,sBAAgB,YAAY;AAE5B,aAAO;AAAA,QACN;AAAA,QACA,OAAO;AAAA,QACP,aAAa,CAAC;AAAA,QACd;AAAA,QACA,OAAO;AAAA,MACR;AAAA,IACD,CAAC,EAAE,OAAO,OAAO;AAEjB,UAAM,QAAQ,SAAU,MAAM;AAC7B,UAAI,kBAAkB,KAAK;AAC3B,UAAI,QAAQ,KAAK;AACjB,UAAI,cAAc,KAAK;AACvB,UAAI,iBAAiB,KAAK;AAE1B,kBAAY,MAAM,SAAS,CAAC,IAAI;AAChC,YAAM,QAAQ,SAAU,MAAM,OAAO;AACpC,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC5B,cAAI,IAAI,gBAAgB,YAAY,SAAS,cAAc,MAAM,CAAC;AAClE,YAAE,MAAM,UAAU;AAClB,YAAE,cAAc;AAAA,QACjB,OAAO;AACN,sBAAY,KAAK,IAAI;AAAA,QACtB;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AAED,UAAM,QAAQ,SAAU,MAAM;AAC7B,UAAI,kBAAkB,KAAK;AAC3B,UAAI,cAAc,KAAK;AAEvB,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC5C,YAAI,YAAY,CAAC,MAAM,QAAW;AACjC,sBAAY,CAAC,IAAI,gBAAgB,SAAS,YAAY,EAAE,sBAAsB,EAAE;AAAA,QACjF;AAAA,MACD;AAAA,IACD,CAAC;AAED,UAAM,QAAQ,SAAU,MAAM;AAC7B,UAAI,kBAAkB,KAAK;AAC3B,UAAI,UAAU,KAAK,QAAQ,cAAc,oBAAoB;AAE7D,sBAAgB,MAAM,UAAU;AAChC,sBAAgB,YAAY;AAE5B,WAAK,YAAY,QAAQ,SAAU,QAAQ,YAAY;AACtD,gBAAQ,SAAS,UAAU,EAAE,MAAM,SAAS,SAAS;AAAA,MACtD,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAOA,WAAS,UAAU,SAAS;AAC3B,QAAI,CAAC,SAAS;AACb,aAAO;AAAA,IACR;AAEA,WAAO,OAAO,mBAAmB,iBAAiB,OAAO,IAAK,QAAQ,gBAAgB;AAAA,EACvF;AAEA,MAAI,YAAY;AAChB,SAAO,iBAAiB,UAAU,WAAY;AAC7C,QAAI,OAAO,8BAA8B,cAAc,OAAO,YAAY;AACzE;AAAA,IACD;AACA,gBAAY,OAAO;AAEnB,mBAAe,MAAM,UAAU,MAAM,KAAK,SAAS,iBAAiB,SAAS,WAAW,CAAC,CAAC;AAAA,EAC3F,CAAC;AAED,QAAM,MAAM,IAAI,YAAY,SAAU,KAAK;AAC1C,QAAI,CAAC,IAAI,MAAM;AACd;AAAA,IACD;AAEA,QAAI;AAAA;AAAA,MAA+B,IAAI;AAAA;AACvC,QAAI;AAAA;AAAA,MAAkC,KAAK;AAAA;AAG3C,QAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,QAAQ,GAAG;AACvC;AAAA,IACD;AAGA,QAAI,KAAK,cAAc,oBAAoB,GAAG;AAC7C;AAAA,IACD;AAGA,QAAI,CAAC,MAAM,KAAK,SAAS,MAAM,WAAW,GAAG;AAC5C;AAAA,IACD;AAGA,SAAK,UAAU,OAAO,WAAW;AAEjC,QAAI,UAAU,IAAI,WAAW;AAE7B,QAAI,QAAQ,IAAI,KAAK,MAAM,YAAY;AACvC,QAAI,WAAW,QAAQ,MAAM,SAAS,IAAI;AAC1C,QAAI;AAEJ,QAAI,QAAQ,IAAI,MAAM,WAAW,CAAC,EAAE,KAAK,eAAe;AAExD,yBAAqB,SAAS,cAAc,MAAM;AAClD,uBAAmB,aAAa,eAAe,MAAM;AACrD,uBAAmB,YAAY;AAC/B,uBAAmB,YAAY;AAE/B,QAAI,IAAI,aAAa,YAAY,GAAG;AACnC,UAAI,MAAM,eAAe,iBAAiB,SAAS,IAAI,aAAa,YAAY,GAAG,EAAE,IAAI;AAAA,IAC1F;AAEA,QAAI,QAAQ,YAAY,kBAAkB;AAE1C,mBAAe,CAAC,GAAG,CAAC;AAEpB,UAAM,MAAM,IAAI,gBAAgB,GAAG;AAAA,EACpC,CAAC;AAED,QAAM,MAAM,IAAI,gBAAgB,SAAU,KAAK;AAC9C,QAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,QAAI,QAAQ,cAAc;AAAA,EAC3B,CAAC;AAEF,GAAE;", "names": []}