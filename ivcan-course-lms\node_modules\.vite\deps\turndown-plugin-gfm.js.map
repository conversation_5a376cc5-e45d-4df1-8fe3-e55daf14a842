{"version": 3, "sources": ["../../turndown-plugin-gfm/lib/turndown-plugin-gfm.es.js"], "sourcesContent": ["var highlightRegExp = /highlight-(?:text|source)-([a-z0-9]+)/;\n\nfunction highlightedCodeBlock (turndownService) {\n  turndownService.addRule('highlightedCodeBlock', {\n    filter: function (node) {\n      var firstChild = node.firstChild;\n      return (\n        node.nodeName === 'DIV' &&\n        highlightRegExp.test(node.className) &&\n        firstChild &&\n        firstChild.nodeName === 'PRE'\n      )\n    },\n    replacement: function (content, node, options) {\n      var className = node.className || '';\n      var language = (className.match(highlightRegExp) || [null, ''])[1];\n\n      return (\n        '\\n\\n' + options.fence + language + '\\n' +\n        node.firstChild.textContent +\n        '\\n' + options.fence + '\\n\\n'\n      )\n    }\n  });\n}\n\nfunction strikethrough (turndownService) {\n  turndownService.addRule('strikethrough', {\n    filter: ['del', 's', 'strike'],\n    replacement: function (content) {\n      return '~' + content + '~'\n    }\n  });\n}\n\nvar indexOf = Array.prototype.indexOf;\nvar every = Array.prototype.every;\nvar rules = {};\n\nrules.tableCell = {\n  filter: ['th', 'td'],\n  replacement: function (content, node) {\n    return cell(content, node)\n  }\n};\n\nrules.tableRow = {\n  filter: 'tr',\n  replacement: function (content, node) {\n    var borderCells = '';\n    var alignMap = { left: ':--', right: '--:', center: ':-:' };\n\n    if (isHeadingRow(node)) {\n      for (var i = 0; i < node.childNodes.length; i++) {\n        var border = '---';\n        var align = (\n          node.childNodes[i].getAttribute('align') || ''\n        ).toLowerCase();\n\n        if (align) border = alignMap[align] || border;\n\n        borderCells += cell(border, node.childNodes[i]);\n      }\n    }\n    return '\\n' + content + (borderCells ? '\\n' + borderCells : '')\n  }\n};\n\nrules.table = {\n  // Only convert tables with a heading row.\n  // Tables with no heading row are kept using `keep` (see below).\n  filter: function (node) {\n    return node.nodeName === 'TABLE' && isHeadingRow(node.rows[0])\n  },\n\n  replacement: function (content) {\n    // Ensure there are no blank lines\n    content = content.replace('\\n\\n', '\\n');\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.tableSection = {\n  filter: ['thead', 'tbody', 'tfoot'],\n  replacement: function (content) {\n    return content\n  }\n};\n\n// A tr is a heading row if:\n// - the parent is a THEAD\n// - or if its the first child of the TABLE or the first TBODY (possibly\n//   following a blank THEAD)\n// - and every cell is a TH\nfunction isHeadingRow (tr) {\n  var parentNode = tr.parentNode;\n  return (\n    parentNode.nodeName === 'THEAD' ||\n    (\n      parentNode.firstChild === tr &&\n      (parentNode.nodeName === 'TABLE' || isFirstTbody(parentNode)) &&\n      every.call(tr.childNodes, function (n) { return n.nodeName === 'TH' })\n    )\n  )\n}\n\nfunction isFirstTbody (element) {\n  var previousSibling = element.previousSibling;\n  return (\n    element.nodeName === 'TBODY' && (\n      !previousSibling ||\n      (\n        previousSibling.nodeName === 'THEAD' &&\n        /^\\s*$/i.test(previousSibling.textContent)\n      )\n    )\n  )\n}\n\nfunction cell (content, node) {\n  var index = indexOf.call(node.parentNode.childNodes, node);\n  var prefix = ' ';\n  if (index === 0) prefix = '| ';\n  return prefix + content + ' |'\n}\n\nfunction tables (turndownService) {\n  turndownService.keep(function (node) {\n    return node.nodeName === 'TABLE' && !isHeadingRow(node.rows[0])\n  });\n  for (var key in rules) turndownService.addRule(key, rules[key]);\n}\n\nfunction taskListItems (turndownService) {\n  turndownService.addRule('taskListItems', {\n    filter: function (node) {\n      return node.type === 'checkbox' && node.parentNode.nodeName === 'LI'\n    },\n    replacement: function (content, node) {\n      return (node.checked ? '[x]' : '[ ]') + ' '\n    }\n  });\n}\n\nfunction gfm (turndownService) {\n  turndownService.use([\n    highlightedCodeBlock,\n    strikethrough,\n    tables,\n    taskListItems\n  ]);\n}\n\nexport { gfm, highlightedCodeBlock, strikethrough, tables, taskListItems };\n"], "mappings": ";;;AAAA,IAAI,kBAAkB;AAEtB,SAAS,qBAAsB,iBAAiB;AAC9C,kBAAgB,QAAQ,wBAAwB;AAAA,IAC9C,QAAQ,SAAU,MAAM;AACtB,UAAI,aAAa,KAAK;AACtB,aACE,KAAK,aAAa,SAClB,gBAAgB,KAAK,KAAK,SAAS,KACnC,cACA,WAAW,aAAa;AAAA,IAE5B;AAAA,IACA,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,YAAY,UAAU,MAAM,eAAe,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;AAEjE,aACE,SAAS,QAAQ,QAAQ,WAAW,OACpC,KAAK,WAAW,cAChB,OAAO,QAAQ,QAAQ;AAAA,IAE3B;AAAA,EACF,CAAC;AACH;AAEA,SAAS,cAAe,iBAAiB;AACvC,kBAAgB,QAAQ,iBAAiB;AAAA,IACvC,QAAQ,CAAC,OAAO,KAAK,QAAQ;AAAA,IAC7B,aAAa,SAAU,SAAS;AAC9B,aAAO,MAAM,UAAU;AAAA,IACzB;AAAA,EACF,CAAC;AACH;AAEA,IAAI,UAAU,MAAM,UAAU;AAC9B,IAAI,QAAQ,MAAM,UAAU;AAC5B,IAAI,QAAQ,CAAC;AAEb,MAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,MAAM,IAAI;AAAA,EACnB,aAAa,SAAU,SAAS,MAAM;AACpC,WAAO,KAAK,SAAS,IAAI;AAAA,EAC3B;AACF;AAEA,MAAM,WAAW;AAAA,EACf,QAAQ;AAAA,EACR,aAAa,SAAU,SAAS,MAAM;AACpC,QAAI,cAAc;AAClB,QAAI,WAAW,EAAE,MAAM,OAAO,OAAO,OAAO,QAAQ,MAAM;AAE1D,QAAI,aAAa,IAAI,GAAG;AACtB,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,YAAI,SAAS;AACb,YAAI,SACF,KAAK,WAAW,CAAC,EAAE,aAAa,OAAO,KAAK,IAC5C,YAAY;AAEd,YAAI,MAAO,UAAS,SAAS,KAAK,KAAK;AAEvC,uBAAe,KAAK,QAAQ,KAAK,WAAW,CAAC,CAAC;AAAA,MAChD;AAAA,IACF;AACA,WAAO,OAAO,WAAW,cAAc,OAAO,cAAc;AAAA,EAC9D;AACF;AAEA,MAAM,QAAQ;AAAA;AAAA;AAAA,EAGZ,QAAQ,SAAU,MAAM;AACtB,WAAO,KAAK,aAAa,WAAW,aAAa,KAAK,KAAK,CAAC,CAAC;AAAA,EAC/D;AAAA,EAEA,aAAa,SAAU,SAAS;AAE9B,cAAU,QAAQ,QAAQ,QAAQ,IAAI;AACtC,WAAO,SAAS,UAAU;AAAA,EAC5B;AACF;AAEA,MAAM,eAAe;AAAA,EACnB,QAAQ,CAAC,SAAS,SAAS,OAAO;AAAA,EAClC,aAAa,SAAU,SAAS;AAC9B,WAAO;AAAA,EACT;AACF;AAOA,SAAS,aAAc,IAAI;AACzB,MAAI,aAAa,GAAG;AACpB,SACE,WAAW,aAAa,WAEtB,WAAW,eAAe,OACzB,WAAW,aAAa,WAAW,aAAa,UAAU,MAC3D,MAAM,KAAK,GAAG,YAAY,SAAU,GAAG;AAAE,WAAO,EAAE,aAAa;AAAA,EAAK,CAAC;AAG3E;AAEA,SAAS,aAAc,SAAS;AAC9B,MAAI,kBAAkB,QAAQ;AAC9B,SACE,QAAQ,aAAa,YACnB,CAAC,mBAEC,gBAAgB,aAAa,WAC7B,SAAS,KAAK,gBAAgB,WAAW;AAIjD;AAEA,SAAS,KAAM,SAAS,MAAM;AAC5B,MAAI,QAAQ,QAAQ,KAAK,KAAK,WAAW,YAAY,IAAI;AACzD,MAAI,SAAS;AACb,MAAI,UAAU,EAAG,UAAS;AAC1B,SAAO,SAAS,UAAU;AAC5B;AAEA,SAAS,OAAQ,iBAAiB;AAChC,kBAAgB,KAAK,SAAU,MAAM;AACnC,WAAO,KAAK,aAAa,WAAW,CAAC,aAAa,KAAK,KAAK,CAAC,CAAC;AAAA,EAChE,CAAC;AACD,WAAS,OAAO,MAAO,iBAAgB,QAAQ,KAAK,MAAM,GAAG,CAAC;AAChE;AAEA,SAAS,cAAe,iBAAiB;AACvC,kBAAgB,QAAQ,iBAAiB;AAAA,IACvC,QAAQ,SAAU,MAAM;AACtB,aAAO,KAAK,SAAS,cAAc,KAAK,WAAW,aAAa;AAAA,IAClE;AAAA,IACA,aAAa,SAAU,SAAS,MAAM;AACpC,cAAQ,KAAK,UAAU,QAAQ,SAAS;AAAA,IAC1C;AAAA,EACF,CAAC;AACH;AAEA,SAAS,IAAK,iBAAiB;AAC7B,kBAAgB,IAAI;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;", "names": []}